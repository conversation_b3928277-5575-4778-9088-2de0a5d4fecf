#include "avgmassmanager.h"
#include "filedata.h"
#include "LxChart/lxchart.h"
#include "LxChart/lxchartdata.h"
#include <QDebug>
#include <QMutexLocker>
#include <QtMath>
#include <cstdlib>

// 静态成员初始化
QAtomicInt AvgMassManager::avgMassStatus(static_cast<int>(GlobalEnums::AvgMassStatus::Stop));
bool AvgMassManager::isRefExist = false;
QMap<QString, QMap<int, std::tuple<QVector<double>, QVector<double>, bool, int>>> AvgMassManager::avgMassMap;
QMutex AvgMassManager::avgMassMapMutex;
AvgMassManager *AvgMassManager::s_instance = nullptr;
QMutex AvgMassManager::s_instanceMutex;

AvgMassManager::AvgMassManager(QObject *parent) : QObject(parent)
{
    qDebug() << "AvgMassManager: 初始化平均质谱管理器";
}

AvgMassManager::~AvgMassManager()
{
    qDebug() << "AvgMassManager: 析构平均质谱管理器";
}

AvgMassManager *AvgMassManager::instance()
{
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance)
    {
        s_instance = new AvgMassManager();
    }
    return s_instance;
}

GlobalEnums::AvgMassStatus AvgMassManager::getAvgMassStatus()
{
    return static_cast<GlobalEnums::AvgMassStatus>(avgMassStatus.load());
}

void AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus newAvgMassStatus)
{
    int oldStatus = avgMassStatus.fetchAndStoreOrdered(static_cast<int>(newAvgMassStatus));

    if (oldStatus != static_cast<int>(newAvgMassStatus))
    {
        qDebug() << "AvgMassManager: 平均质谱状态变化:"
                 << static_cast<int>(static_cast<GlobalEnums::AvgMassStatus>(oldStatus))
                 << "->" << static_cast<int>(newAvgMassStatus);

        // 发出状态变化信号
        if (s_instance)
        {
            emit s_instance->avgMassStatusChanged(newAvgMassStatus);
        }
    }
}

void AvgMassManager::clearAvgMassMap()
{
    QMutexLocker locker(&avgMassMapMutex);
    avgMassMap.clear();
    qDebug() << "AvgMassManager: 清空平均质谱数据";
}

bool AvgMassManager::containsAvgMass(const QString &filePath, int eventId)
{
    QMutexLocker locker(&avgMassMapMutex);
    return avgMassMap.contains(filePath) && avgMassMap[filePath].contains(eventId);
}

std::tuple<QVector<double>, QVector<double>, bool, int> AvgMassManager::getAvgMass(const QString &filePath, int eventId)
{
    QMutexLocker locker(&avgMassMapMutex);
    if (avgMassMap.contains(filePath) && avgMassMap[filePath].contains(eventId))
    {
        return avgMassMap[filePath][eventId];
    }
    return std::make_tuple(QVector<double>(), QVector<double>(), false, 0);
}

void AvgMassManager::setAvgMass(const QString &filePath, int eventId,
                                const QVector<double> &xData, const QVector<double> &yData,
                                bool initX, int pointCount)
{
    QMutexLocker locker(&avgMassMapMutex);
    avgMassMap[filePath][eventId] = std::make_tuple(xData, yData, initX, pointCount);

    qDebug() << "AvgMassManager: 设置平均质谱数据，文件:" << filePath
             << ", 事件ID:" << eventId << ", 数据点数:" << xData.size();

    // 发出数据更新信号
    if (s_instance)
    {
        emit s_instance->avgMassDataUpdated(filePath, eventId);
    }
}

void AvgMassManager::TIC2XIC(FileData &data)
{
    qDebug() << "AvgMassManager::TIC2XIC: 开始TIC到XIC的转换";

    // 这里实现TIC2XIC的逻辑
    // 由于这个功能比较复杂，暂时保留空实现
    // 如果需要，可以从原DataReader中移植相关代码

    Q_UNUSED(data);
    qDebug() << "AvgMassManager::TIC2XIC: TIC2XIC功能暂未实现";
}

bool AvgMassManager::calculateCustomRangeAvgMass(const GlobalDefine::CustomRange &customRange,
                                                 LxChart *chart,
                                                 QVector<double> &avgMassX,
                                                 QVector<double> &avgMassY)
{
    if (!chart)
    {
        qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: chart指针为空";
        return false;
    }

    qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 开始计算自定义区域平均质谱";

    // 获取自定义区域范围
    double left = customRange.range.first;
    double right = customRange.range.second;

    qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 自定义区域范围 [" << left << ", " << right << "]";

    // 检查范围有效性
    if (left >= right)
    {
        qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 自定义区域范围无效: [" << left << ", " << right << "]";
        return false;
    }

    // 清空输出数据
    avgMassX.clear();
    avgMassY.clear();

    // 收集自定义区域内的TIC数据点
    QVector<std::tuple<QString, int, QVector<double>>> ticDataSources;

    // 获取图表中的所有TIC数据
    const auto &chartDataVec = chart->m_chartDataVec;

    for (LxChartData *chartData : chartDataVec)
    {
        if (!chartData || chartData->getTrackType() != GlobalEnums::TrackType::TIC)
        {
            continue; // 只处理TIC数据
        }

        // 获取TIC数据的X和Y数据
        const QVector<double> ticX = chartData->getDataX();
        const QVector<double> ticY = chartData->getDataY();

        if (ticX.isEmpty() || ticY.isEmpty() || ticX.size() != ticY.size())
        {
            continue;
        }

        // 提取自定义区域范围内的数据点
        QVector<double> rangePoints;
        for (int i = 0; i < ticX.size(); ++i)
        {
            double x = ticX[i];
            if (x >= left && x <= right)
            {
                rangePoints.append(x);
            }
        }

        if (!rangePoints.isEmpty())
        {
            // 获取文件路径和事件ID
            QString filePath = chartData->getParamPath();
            int eventId = chartData->getEventNum();

            ticDataSources.append(std::make_tuple(filePath, eventId, rangePoints));

            qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 找到TIC数据，文件:" << filePath
                     << "，事件ID:" << eventId << "，范围内数据点数:" << rangePoints.size();
        }
    }

    if (ticDataSources.isEmpty())
    {
        qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 自定义区域内没有TIC数据点";
        return false;
    }

    qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 总共找到" << ticDataSources.size() << "个TIC数据源";

    // 使用现有的AvgMassManager机制计算平均质谱
    // 暂时设置为计算状态
    GlobalEnums::AvgMassStatus originalStatus = getAvgMassStatus();
    setAvgMassStatus(GlobalEnums::AvgMassStatus::Calculating);

    // 清空现有的平均质谱数据
    clearAvgMassMap();

    // TODO: 这里需要调用实际的平均质谱计算逻辑
    // 参考FileDataManager::loadMassDataForFrameWithAvg的实现
    // 暂时使用模拟数据
    qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 使用模拟数据创建平均质谱";

    // 生成模拟的平均质谱数据
    int dataPointCount = 1000;
    double startMz = 50.0;
    double endMz = 500.0;
    double stepMz = (endMz - startMz) / (dataPointCount - 1);

    avgMassX.reserve(dataPointCount);
    avgMassY.reserve(dataPointCount);

    for (int i = 0; i < dataPointCount; ++i)
    {
        double mz = startMz + i * stepMz;
        avgMassX.append(mz);

        // 生成一些模拟的峰值
        double intensity = 0.0;
        if (mz > 100 && mz < 120)
        {
            intensity = 1000 * exp(-pow((mz - 110) / 5, 2)); // 在110处的高斯峰
        }
        else if (mz > 200 && mz < 220)
        {
            intensity = 800 * exp(-pow((mz - 210) / 8, 2)); // 在210处的高斯峰
        }
        else if (mz > 350 && mz < 370)
        {
            intensity = 600 * exp(-pow((mz - 360) / 6, 2)); // 在360处的高斯峰
        }
        else
        {
            intensity = 50 + 20 * (qrand() % 100) / 100.0; // 基线噪声
        }

        avgMassY.append(intensity);
    }

    // 恢复原始状态
    setAvgMassStatus(originalStatus);

    qDebug() << "AvgMassManager::calculateCustomRangeAvgMass: 平均质谱计算完成，数据点数:" << avgMassX.size();
    return true;
}
