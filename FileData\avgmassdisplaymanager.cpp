#include "avgmassdisplaymanager.h"
#include "LxChart/lxticchartdata.h"
#include "LxChart/lxchart.h"
#include "LxChart/lxchartdata.h"
#include "filedata.h"
#include "filedatamanager.h"
#include "avgmassmanager.h"
#include <QDebug>
#include <QThread>
#include <QCoreApplication>
#include <QtMath>
#include <cstdlib>

// 静态成员初始化
AvgMassDisplayManager *AvgMassDisplayManager::s_instance = nullptr;
QMutex AvgMassDisplayManager::s_instanceMutex;

AvgMassDisplayManager::AvgMassDisplayManager(QObject *parent) : QObject(parent)
{
    qDebug() << "AvgMassDisplayManager: 初始化平均质谱显示管理器";
}

AvgMassDisplayManager::~AvgMassDisplayManager()
{
    qDebug() << "AvgMassDisplayManager: 析构平均质谱显示管理器";
    clearAllAvgMass();
}

AvgMassDisplayManager *AvgMassDisplayManager::instance()
{
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance)
    {
        s_instance = new AvgMassDisplayManager();
    }
    return s_instance;
}

bool AvgMassDisplayManager::createAvgMassForCustomRange(GlobalDefine::CustomRange &customRange, LxChart *chart)
{
    if (!chart)
    {
        qDebug() << "AvgMassDisplayManager::createAvgMassForCustomRange: chart指针为空";
        return false;
    }

    QMutexLocker locker(&m_mutex);

    // 检查是否已经有平均质谱
    if (customRange.avgMassTicData)
    {
        qDebug() << "AvgMassDisplayManager::createAvgMassForCustomRange: 自定义区域已有平均质谱，索引:" << customRange.index;
        return true;
    }

    qDebug() << "AvgMassDisplayManager::createAvgMassForCustomRange: 开始为自定义区域创建平均质谱，索引:" << customRange.index;

    // 计算平均质谱数据
    QVector<double> avgMassX, avgMassY;
    if (!calculateAvgMassData(customRange, chart, avgMassX, avgMassY))
    {
        qDebug() << "AvgMassDisplayManager::createAvgMassForCustomRange: 计算平均质谱数据失败";
        return false;
    }

    // 创建TIC数据对象
    LxTicChartData *ticData = createAvgMassTicData(avgMassX, avgMassY, customRange.index);
    if (!ticData)
    {
        qDebug() << "AvgMassDisplayManager::createAvgMassForCustomRange: 创建TIC数据失败";
        return false;
    }

    // 关联到自定义区域
    customRange.avgMassTicData = ticData;

    // 建立映射关系
    m_avgMassTicToCustomRangeMap[ticData->getUniqueID()] = customRange.index;

    qDebug() << "AvgMassDisplayManager::createAvgMassForCustomRange: 平均质谱创建成功，TIC ID:" << ticData->getUniqueID();

    // 发射信号
    emit avgMassCreated(customRange.index, ticData);

    return true;
}

bool AvgMassDisplayManager::removeAvgMassForCustomRange(GlobalDefine::CustomRange &customRange, LxChart *chart)
{
    if (!chart)
    {
        qDebug() << "AvgMassDisplayManager::removeAvgMassForCustomRange: chart指针为空";
        return false;
    }

    QMutexLocker locker(&m_mutex);

    if (!customRange.avgMassTicData)
    {
        qDebug() << "AvgMassDisplayManager::removeAvgMassForCustomRange: 自定义区域没有平均质谱，索引:" << customRange.index;
        return true;
    }

    qDebug() << "AvgMassDisplayManager::removeAvgMassForCustomRange: 开始删除平均质谱，索引:" << customRange.index;

    QString ticId = customRange.avgMassTicData->getUniqueID();

    // 从映射中移除
    m_avgMassTicToCustomRangeMap.remove(ticId);

    // 从图表中移除TIC数据
    chart->RemoveLxChartDataByUniqueID(ticId);

    // 清理自定义区域中的引用
    customRange.avgMassTicData = nullptr;
    customRange.hasAvgMass = false;
    customRange.avgMassUniqueId.clear();

    qDebug() << "AvgMassDisplayManager::removeAvgMassForCustomRange: 平均质谱删除成功，TIC ID:" << ticId;

    // 发射信号
    emit avgMassRemoved(customRange.index);

    return true;
}

bool AvgMassDisplayManager::hasAvgMassForCustomRange(const GlobalDefine::CustomRange &customRange) const
{
    QMutexLocker locker(&m_mutex);
    return customRange.avgMassTicData != nullptr;
}

int AvgMassDisplayManager::findCustomRangeIndexByTicId(const QString &uniqueId) const
{
    QMutexLocker locker(&m_mutex);
    return m_avgMassTicToCustomRangeMap.value(uniqueId, -1);
}

void AvgMassDisplayManager::clearAllAvgMass()
{
    QMutexLocker locker(&m_mutex);
    qDebug() << "AvgMassDisplayManager::clearAllAvgMass: 清除所有平均质谱数据";
    m_avgMassTicToCustomRangeMap.clear();
}

bool AvgMassDisplayManager::onAvgMassTicDeleted(const QString &uniqueId, LxChart *chart)
{
    if (!chart)
    {
        qDebug() << "AvgMassDisplayManager::onAvgMassTicDeleted: chart指针为空";
        return false;
    }

    QMutexLocker locker(&m_mutex);

    // 查找对应的自定义区域索引
    int customRangeIndex = m_avgMassTicToCustomRangeMap.value(uniqueId, -1);
    if (customRangeIndex == -1)
    {
        qDebug() << "AvgMassDisplayManager::onAvgMassTicDeleted: 未找到对应的自定义区域，TIC ID:" << uniqueId;
        return false;
    }

    qDebug() << "AvgMassDisplayManager::onAvgMassTicDeleted: 平均质谱TIC被删除，对应自定义区域索引:" << customRangeIndex;

    // 从映射中移除
    m_avgMassTicToCustomRangeMap.remove(uniqueId);

    // 获取自定义区域并清理
    auto &vecCustomRange = chart->getVecCustomRange();
    if (customRangeIndex >= 0 && customRangeIndex < vecCustomRange.size())
    {
        GlobalDefine::CustomRange &customRange = vecCustomRange[customRangeIndex];

        // 清理自定义区域中的平均质谱引用
        customRange.avgMassTicData = nullptr;
        customRange.hasAvgMass = false;
        customRange.avgMassUniqueId.clear();

        // 删除自定义区域（这会触发图形元素的清理）
        chart->removeCustomRange(customRangeIndex);

        qDebug() << "AvgMassDisplayManager::onAvgMassTicDeleted: 已删除对应的自定义区域，索引:" << customRangeIndex;
    }

    // 发射信号
    emit avgMassRemoved(customRangeIndex);

    return true;
}

bool AvgMassDisplayManager::calculateAvgMassData(const GlobalDefine::CustomRange &customRange,
                                                 LxChart *chart,
                                                 QVector<double> &avgMassX,
                                                 QVector<double> &avgMassY)
{
    qDebug() << "AvgMassDisplayManager::calculateAvgMassData: 开始计算平均质谱数据";

    // 直接使用AvgMassManager进行计算
    AvgMassManager *avgMassManager = AvgMassManager::instance();
    if (!avgMassManager)
    {
        qDebug() << "AvgMassDisplayManager::calculateAvgMassData: 无法获取AvgMassManager实例";
        return false;
    }

    // 调用AvgMassManager的自定义区域平均质谱计算方法
    bool success = avgMassManager->calculateCustomRangeAvgMass(customRange, chart, avgMassX, avgMassY);

    if (success)
    {
        qDebug() << "AvgMassDisplayManager::calculateAvgMassData: 平均质谱计算成功，数据点数:" << avgMassX.size();
    }
    else
    {
        qDebug() << "AvgMassDisplayManager::calculateAvgMassData: 平均质谱计算失败";
    }

    return success;
}

// getCustomRangeTicPoints方法已移动到AvgMassManager::calculateCustomRangeAvgMass中

LxTicChartData *AvgMassDisplayManager::createAvgMassTicData(const QVector<double> &avgMassX,
                                                            const QVector<double> &avgMassY,
                                                            int customRangeIndex)
{
    qDebug() << "AvgMassDisplayManager::createAvgMassTicData: 创建平均质谱TIC数据，自定义区域索引:" << customRangeIndex;

    // 创建一个特殊的TIC数据对象
    // 使用特殊的路径标识这是平均质谱数据
    QString avgMassPath = QString("AvgMass_CustomRange_%1").arg(customRangeIndex);

    LxTicChartData *ticData = nullptr;

    // 确保在主线程中创建
    if (QThread::currentThread() == QCoreApplication::instance()->thread())
    {
        ticData = new LxTicChartData(avgMassPath, -1, // 使用-1作为特殊的事件ID
                                     GlobalEnums::IonMode::NagativeIon,
                                     GlobalEnums::ScanMode::FullScan,
                                     "平均质谱", "平均质谱", "平均质谱");
    }
    else
    {
        // 在主线程中创建
        QMetaObject::invokeMethod(QCoreApplication::instance(), [&]()
                                  { ticData = new LxTicChartData(avgMassPath, -1,
                                                                 GlobalEnums::IonMode::NagativeIon,
                                                                 GlobalEnums::ScanMode::FullScan,
                                                                 "平均质谱", "平均质谱", "平均质谱"); }, Qt::BlockingQueuedConnection);
    }

    if (ticData)
    {
        // 设置标题为"平均质谱"
        ticData->setTitle("平均质谱");

        // 设置数据 - 使用setDataThreadSafe方法
        ticData->setDataThreadSafe(avgMassX, avgMassY);

        // 设置特殊的颜色，区别于普通TIC
        QColor avgMassColor(255, 165, 0); // 橙色
        ticData->setLineColor(avgMassColor);
        ticData->setOriginalColor(avgMassColor);

        qDebug() << "AvgMassDisplayManager::createAvgMassTicData: TIC数据创建成功，数据点数:" << avgMassX.size();
    }
    else
    {
        qDebug() << "AvgMassDisplayManager::createAvgMassTicData: TIC数据创建失败";
    }

    return ticData;
}
