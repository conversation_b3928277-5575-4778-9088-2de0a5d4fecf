#ifndef AVGMASSMANAGER_H
#define AVGMASSMANAGER_H

#include <QObject>
#include <QMap>
#include <QVector>
#include <QAtomicInt>
#include <QMutex>
#include <tuple>
#include "Globals/GlobalDefine.h"

// 前向声明
class FileData;

/**
 * @brief 平均质谱管理器 - 替代DataReader的静态成员
 *
 * 这个类管理平均质谱相关的功能，完全独立于DLL依赖
 */
class AvgMassManager : public QObject
{
    Q_OBJECT

public:
    explicit AvgMassManager(QObject *parent = nullptr);
    ~AvgMassManager();

    // 获取单例实例
    static AvgMassManager *instance();

    // 平均质谱状态管理
    static GlobalEnums::AvgMassStatus getAvgMassStatus();
    static void setAvgMassStatus(GlobalEnums::AvgMassStatus newAvgMassStatus);

    // 背景区域存在标志
    static bool isRefExist;

    // 平均质谱数据管理
    // 最外层QMap:所有Tic文件的平均质谱Map
    // 第二个Map:某个质谱文件里的某个事件对应的平均质谱Map
    // tuple:0:平均质谱X数据 1:平均质谱Y数据 2:是否初始化X轴数据 3:需要除的点数（需要平均的总质谱数量）
    static QMap<QString, QMap<int, std::tuple<QVector<double>, QVector<double>, bool, int>>> avgMassMap;

    // 清空平均质谱数据
    static void clearAvgMassMap();

    // 检查是否包含指定文件和事件的平均质谱数据
    static bool containsAvgMass(const QString &filePath, int eventId);

    // 获取平均质谱数据
    static std::tuple<QVector<double>, QVector<double>, bool, int> getAvgMass(const QString &filePath, int eventId);

    // 设置平均质谱数据
    static void setAvgMass(const QString &filePath, int eventId,
                           const QVector<double> &xData, const QVector<double> &yData,
                           bool initX, int pointCount);

    // TIC2XIC功能（从DataReader移植过来）
    void TIC2XIC(FileData &data);

    // 自定义区域平均质谱计算
    /**
     * @brief 为自定义区域计算平均质谱
     * @param customRange 自定义区域
     * @param chart LxChart指针，用于获取TIC数据
     * @param avgMassX 输出：平均质谱X数据
     * @param avgMassY 输出：平均质谱Y数据
     * @return 是否计算成功
     */
    bool calculateCustomRangeAvgMass(const GlobalDefine::CustomRange &customRange,
                                     class LxChart *chart,
                                     QVector<double> &avgMassX,
                                     QVector<double> &avgMassY);

private:
    // 平均质谱计算状态 默认停止计算 只有处于Ready状态下才可以使用平均质谱数据
    static QAtomicInt avgMassStatus;

    // 线程安全保护
    static QMutex avgMassMapMutex;

    // 单例实例
    static AvgMassManager *s_instance;
    static QMutex s_instanceMutex;

signals:
    // 平均质谱状态变化信号
    void avgMassStatusChanged(GlobalEnums::AvgMassStatus status);

    // 平均质谱数据更新信号
    void avgMassDataUpdated(const QString &filePath, int eventId);
};

#endif // AVGMASSMANAGER_H
