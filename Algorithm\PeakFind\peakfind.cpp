#include "peakfind.h"
#include <iostream>

PeakFind::PeakFind(QObject *parent) : QObject{parent}
{
}

void PeakFind::searchPeaksWithDefaultParams(LxChart *chart)
{
    TaskManager::instance()->run([=]()
                                 {
        foreach (LxChartData *data, chart->m_chartDataVec) {
            if (data->getHasFindPeak()) {
                continue;
            }

            // 获取校正后的数据用于寻峰和SNR计算
            std::vector<double> correctedData = cubicBaselineCorrection(movingAverage(data->getDataY().toStdVector(), 2));

            // 🔍 使用带X轴数据的寻峰函数来计算真实半峰宽
            std::vector<double> xDataStd = data->getDataX().toStdVector();
            std::vector<double> yDataStd = data->getDataY().toStdVector();
            qDebug() << "🚀 开始寻峰，数据点数:" << yDataStd.size() << ", X轴数据点数:" << xDataStd.size();

            data->peakVec = searchPeaksWithXData(yDataStd, xDataStd);

            for (int i = 0; i < data->peakVec.size(); i++) {
                Peak &p = data->peakVec[i];
                p.pTop = QPointF(data->getDataX().at(p.top), data->getDataY().at(p.top));
                p.pStart = QPointF(data->getDataX().at(p.start), data->getDataY().at(p.start));
                p.pEnd = QPointF(data->getDataX().at(p.end), data->getDataY().at(p.end));
                // qDebug() << p.pTop << p.pStart << p.pEnd;
                if (p.pTop == p.pEnd) {
                    // qDebug() << "p.pTop == p.pEnd " << p.pTop << p.pEnd;
                }
            }

            // 计算SNR（使用校正后的数据，默认folderWidthOfNoise=10）
            if (!data->peakVec.empty()) {
                std::vector<double> snrList = CalcSNRofPeakList(correctedData, data->peakVec, 10);
                for (int i = 0; i < data->peakVec.size() && i < snrList.size(); i++) {
                    data->peakVec[i].snr = snrList[i];
                }

                // 输出所有峰的详细信息
                qDebug() << "=== 峰数据详细信息 (默认参数) ===";
                qDebug() << "曲线ID:" << data->getUniqueID() << "曲线类型:" << static_cast<int>(data->getTrackType()) << "峰数量:" << data->peakVec.size();
                for (int i = 0; i < data->peakVec.size(); i++) {
                    const Peak &peak = data->peakVec[i];
                    if (data->getTrackType() == GlobalEnums::TrackType::MS) {
                        // 质谱数据：显示 m/z, 峰面积, 峰高
                        // qDebug() << QString("MASS峰%1: m/z=%2, 峰面积=%3, 峰高=%4, SNR=%5")
                        //             .arg(i+1)
                        //             .arg(peak.pTop.x(), 0, 'f', 2)
                        //             .arg(peak.area, 0, 'f', 2)
                        //             .arg(peak.height, 0, 'f', 2)
                        //             .arg(peak.snr, 0, 'f', 2);
                    } else {
                        // TIC/XIC数据：显示 时间, 峰面积, 峰高, 信噪比
                        // qDebug() << QString("TIC峰%1: 时间=%2, 峰面积=%3, 峰高=%4, 信噪比=%5, FWHM=%6")
                        //                 .arg(i + 1)
                        //                 .arg(peak.pTop.x(), 0, 'f', 2)
                        //                 .arg(peak.area, 0, 'f', 2)
                        //                 .arg(peak.height, 0, 'f', 2)
                        //                 .arg(peak.snr, 0, 'f', 2)
                        //                 .arg(peak.fwhm, 0, 'f', 2);
                    }
                }
            }
        }
        // qDebug() << __FUNCTION__ << "寻峰完成" << QThread::currentThread();
        getInstance().sg_searchPeaksSuccess(chart->getWindowId()); });
}

void PeakFind::serachPeaks(LxChart *chart, uint smoothType, uint BLineCorrect, uint slope, uint area, uint height)
{
    TaskManager::instance()->run([=]()
                                 {
        // qDebug() << "开始寻峰，曲线数量:" << chart->m_chartDataVec.size();

        int processedCount = 0;
        int skippedCount = 0;

        foreach (LxChartData *data, chart->m_chartDataVec) {
            if (!data) {
                qDebug() << "PeakFind::serachPeaks: 跳过空数据指针";
                continue;
            }

            if (data->getHasFindPeak()) {
                // qDebug() << "跳过已寻峰的曲线，ID:" << data->getUniqueID();
                skippedCount++;
                continue;
            }

            // 🎯 检查是否为MRM/SIM类型的MASS数据，MRM/SIM的MASS数据不需要寻峰
            // 注意：MRM/SIM类型的TIC和XIC仍然需要寻峰
            if (data->isMrmData()) {
                qDebug() << "PeakFind::serachPeaks: 跳过MRM/SIM类型的MASS数据寻峰，UniqueID:" << data->getUniqueID()
                         << "，扫描模式:" << static_cast<int>(data->getScanMode());
                data->setHasFindPeak(true); // 标记为已处理
                skippedCount++;
                continue;
            }

            // qDebug() << "开始处理曲线，ID:" << data->getUniqueID();

            std::vector<double> vec;
            if (smoothType == 0) {
                vec = movingAverage(data->getDataY().toStdVector(), 5);
            } else if (smoothType == 1) {
                vec = gaussianSmooth(data->getDataY().toStdVector(), 5, 1);
            } else if (smoothType == 2) {
                vec = savitzkyGolay(data->getDataY().toStdVector(), 5, 2);
            }

            if (BLineCorrect == 0) {
                vec = cubicBaselineCorrection(vec);
            } else if (BLineCorrect == 1) {
                vec = alsBaselineCorrection(vec);
            }

            // 使用传入的参数进行寻峰
            data->peakVec = findPeaks(vec, 20, 5, slope, area, height);

            for (int i = 0; i < data->peakVec.size(); i++) {
                Peak &p = data->peakVec[i];
                p.pTop = QPointF(data->getDataX().at(p.top), data->getDataY().at(p.top));
                p.pStart = QPointF(data->getDataX().at(p.start), data->getDataY().at(p.start));
                p.pEnd = QPointF(data->getDataX().at(p.end), data->getDataY().at(p.end));
                // qDebug() << p.pTop << p.pStart << p.pEnd;
                if (p.pTop == p.pEnd) {
                    // qDebug() << "p.pTop == p.pEnd " << p.pTop << p.pEnd;
                }
            }

            // 计算SNR（使用校正后的数据，默认folderWidthOfNoise=10）
            if (!data->peakVec.empty()) {
                std::vector<double> snrList = CalcSNRofPeakList(vec, data->peakVec, 10);
                for (int i = 0; i < data->peakVec.size() && i < snrList.size(); i++) {
                    data->peakVec[i].snr = snrList[i];
                }

                // 输出所有峰的详细信息
                qDebug() << "=== 峰数据详细信息 (自定义参数) ===";
                qDebug() << "曲线ID:" << data->getUniqueID() << "曲线类型:" << static_cast<int>(data->getTrackType()) << "峰数量:" << data->peakVec.size();
                for (int i = 0; i < data->peakVec.size(); i++) {
                    const Peak &peak = data->peakVec[i];
                    if (data->getTrackType() == GlobalEnums::TrackType::MS) {
                        // 质谱数据：显示 m/z, 峰面积, 峰高
                        //     qDebug() << QString("MASS峰%1: m/z=%2, 峰面积=%3, 峰高=%4, SNR=%5")
                        //                     .arg(i + 1)
                        //                     .arg(peak.pTop.x(), 0, 'f', 2)
                        //                     .arg(peak.area, 0, 'f', 2)
                        //                     .arg(peak.height, 0, 'f', 2)
                        //                     .arg(peak.snr, 0, 'f', 2);
                        // } else {
                        //     // TIC/XIC数据：显示 时间, 峰面积, 峰高, 信噪比
                        //     qDebug() << QString("TIC峰%1: 时间=%2, 峰面积=%3, 峰高=%4, 信噪比=%5, FWHM=%6")
                        //                     .arg(i + 1)
                        //                     .arg(peak.pTop.x(), 0, 'f', 2)
                        //                     .arg(peak.area, 0, 'f', 2)
                        //                     .arg(peak.height, 0, 'f', 2)
                        //                     .arg(peak.snr, 0, 'f', 2)
                        //                     .arg(peak.fwhm, 0, 'f', 2);
                    }
                }
            }

            // 标记曲线为已寻峰
            data->setHasFindPeak(true);
            // qDebug() << "曲线寻峰完成，ID:" << data->getUniqueID() << "，峰数量:" << data->peakVec.size();
            processedCount++;
        }

        // qDebug() << "寻峰完成，处理:" << processedCount << "，跳过:" << skippedCount;
        getInstance().sg_searchPeaksSuccess(chart->getWindowId()); });
}

void PeakFind::searchPeaksWithConfig(LxChart *chart, const PeakFindingConfig &config, bool forceReprocess)
{
    TaskManager::instance()->run([=]()
                                 {
        // qDebug() << "开始使用配置参数寻峰，曲线数量:" << chart->m_chartDataVec.size();
        qDebug() << "使用默认参数:" << config.useDefault << "，强制重新处理:" << forceReprocess;

        int processedCount = 0;
        int skippedCount = 0;

        foreach (LxChartData *data, chart->m_chartDataVec) {
            if (!data) {
                qDebug() << "PeakFind::searchPeaksWithConfig: 跳过空数据指针";
                continue;
            }

            if (data->getHasFindPeak() && !forceReprocess) {
                qDebug() << "跳过已寻峰的曲线，ID:" << data->getUniqueID();
                skippedCount++;
                continue;
            }

            if (data->getHasFindPeak() && forceReprocess) {
                qDebug() << "强制重新寻峰，ID:" << data->getUniqueID();
                // 清除之前的峰数据
                data->peakVec.clear();
            }

            // 🎯 检查是否为MRM/SIM类型的MASS数据，MRM/SIM的MASS数据不需要寻峰
            // 注意：MRM/SIM类型的TIC和XIC仍然需要寻峰
            if (data->isMrmData()) {
                qDebug() << "PeakFind::searchPeaksWithConfig: 跳过MRM/SIM类型的MASS数据寻峰，UniqueID:" << data->getUniqueID()
                         << "，扫描模式:" << static_cast<int>(data->getScanMode());
                data->setHasFindPeak(true); // 标记为已处理
                skippedCount++;
                continue;
            }

            qDebug() << "开始处理曲线，ID:" << data->getUniqueID();

            std::vector<double> vec = data->getDataY().toStdVector();

            // 获取参数（无论是否使用默认参数，都需要folderWidthOfNoise）
            const SmoothParams &smooth = config.smoothParams;
            const BaselineParams &baseline = config.baselineParams;
            const PeakFindParams &peakFind = config.peakFindParams;

            if (config.useDefault) {
                // 使用默认参数（与原来的逻辑保持一致）
                vec = movingAverage(vec, 5);
                vec = cubicBaselineCorrection(vec);

                // 🔍 使用带X轴数据的寻峰函数
                std::vector<double> xDataStd = data->getDataX().toStdVector();
                qDebug() << "🚀 默认参数寻峰，数据点数:" << vec.size() << ", X轴数据点数:" << xDataStd.size();
                data->peakVec = findPeaks(vec, 20, 5, 2.0, 0.0, 0.0, &xDataStd);
            } else {
                // 使用自定义参数

                // 调试信息：打印当前使用的参数
                qDebug() << "=== 自定义寻峰参数 ===";
                qDebug() << "平滑类型:" << static_cast<int>(smooth.type) << "窗口大小:" << smooth.windowSize;
                qDebug() << "高斯σ:" << smooth.gaussianSigma << "S-G阶数:" << smooth.sgPolyOrder;
                qDebug() << "基线类型:" << static_cast<int>(baseline.type);
                if (baseline.type == BaselineType::ALS) {
                    qDebug() << "ALS λ:" << baseline.alsLambda << "p:" << baseline.alsP << "迭代:" << baseline.alsMaxIter;
                }
                qDebug() << "噪声窗口:" << peakFind.noiseWindow << "最小峰宽:" << peakFind.minPeakWidth;
                qDebug() << "斜率因子:" << peakFind.slopeFactor << "最小面积:" << peakFind.minPeakArea << "最小高度:" << peakFind.minPeakHeight;

                // 第一步：平滑处理
                switch (smooth.type) {
                case SmoothType::Average:
                    vec = movingAverage(vec, smooth.windowSize);
                    break;
                case SmoothType::Gaussian:
                    vec = gaussianSmooth(vec, smooth.windowSize, smooth.gaussianSigma);
                    break;
                case SmoothType::SGolay:
                    vec = savitzkyGolay(vec, smooth.windowSize, smooth.sgPolyOrder);
                    break;
                }

                // 第二步：基线校正
                switch (baseline.type) {
                case BaselineType::Cubic:
                    vec = cubicBaselineCorrection(vec);
                    break;
                case BaselineType::ALS:
                    vec = alsBaselineCorrection(vec, baseline.alsLambda, baseline.alsP, baseline.alsMaxIter);
                    break;
                }

                // 第三步：寻峰（使用带X轴数据的版本）
                std::vector<double> xDataStd = data->getDataX().toStdVector();
                qDebug() << "🚀 自定义参数寻峰，数据点数:" << vec.size() << ", X轴数据点数:" << xDataStd.size();
                data->peakVec = findPeaks(vec, peakFind.noiseWindow, peakFind.minPeakWidth, peakFind.slopeFactor, peakFind.minPeakArea, peakFind.minPeakHeight, &xDataStd);
            }

            // 设置峰的坐标点
            for (int i = 0; i < data->peakVec.size(); i++) {
                Peak &p = data->peakVec[i];
                p.pTop = QPointF(data->getDataX().at(p.top), data->getDataY().at(p.top));
                p.pStart = QPointF(data->getDataX().at(p.start), data->getDataY().at(p.start));
                p.pEnd = QPointF(data->getDataX().at(p.end), data->getDataY().at(p.end));
            }

            // 计算SNR（使用校正后的数据）
            if (!data->peakVec.empty()) {
                std::vector<double> snrList = CalcSNRofPeakList(vec, data->peakVec, peakFind.folderWidthOfNoise);
                for (int i = 0; i < data->peakVec.size() && i < snrList.size(); i++) {
                    data->peakVec[i].snr = snrList[i];
                }

                // 输出所有峰的详细信息
                qDebug() << "=== 峰数据详细信息 (配置参数) ===";
                qDebug() << "曲线ID:" << data->getUniqueID() << "曲线类型:" << static_cast<int>(data->getTrackType()) << "峰数量:" << data->peakVec.size()
                         << "噪声窗口倍数:" << peakFind.folderWidthOfNoise;
                for (int i = 0; i < data->peakVec.size(); i++) {
                    const Peak &peak = data->peakVec[i];
                    if (data->getTrackType() == GlobalEnums::TrackType::MS) {
                        // 质谱数据：显示 m/z, 峰面积, 峰高
                        // qDebug() << QString("MASS峰%1: m/z=%2, 峰面积=%3, 峰高=%4, SNR=%5")
                        //                 .arg(i + 1)
                        //                 .arg(peak.pTop.x(), 0, 'f', 2)
                        //                 .arg(peak.area, 0, 'f', 2)
                        //                 .arg(peak.height, 0, 'f', 2)
                        //                 .arg(peak.snr, 0, 'f', 2);
                    } else {
                        // TIC/XIC数据：显示 时间, 峰面积, 峰高, 信噪比
                        // qDebug() << QString("TIC峰%1: 时间=%2, 峰面积=%3, 峰高=%4, 信噪比=%5, FWHM=%6")
                        //                 .arg(i + 1)
                        //                 .arg(peak.pTop.x(), 0, 'f', 2)
                        //                 .arg(peak.area, 0, 'f', 2)
                        //                 .arg(peak.height, 0, 'f', 2)
                        //                 .arg(peak.snr, 0, 'f', 2)
                        //                 .arg(peak.fwhm, 0, 'f', 2);
                    }
                }
            }

            // 标记曲线为已寻峰
            data->setHasFindPeak(true);
            processedCount++;

            // qDebug() << "曲线寻峰完成，ID:" << data->getUniqueID() << "，峰数量:" << data->peakVec.size();
        }

        // qDebug() << "配置寻峰完成，处理:" << processedCount << "，跳过:" << skippedCount;
        getInstance().sg_searchPeaksSuccess(chart->getWindowId()); });
}
