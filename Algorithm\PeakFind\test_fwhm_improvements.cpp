#include "wh_peakSearch.h"
#include <iostream>
#include <vector>
#include <iomanip>

// 测试FWHM改进功能
void testFWHMImprovements()
{
    std::cout << "=== FWHM改进功能测试 ===" << std::endl;

    // 创建测试数据：模拟一个高斯峰
    std::vector<double> signal = {
        1.0, 1.2, 1.5, 2.0, 3.0, 5.0, 8.0, 12.0, 15.0, 18.0,      // 峰起始部分
        20.0, 18.0, 15.0, 12.0, 8.0, 5.0, 3.0, 2.0, 1.5, 1.2, 1.0 // 峰下降部分
    };

    // 创建对应的X轴数据（时间，单位：分钟）
    std::vector<double> xData;
    for (int i = 0; i < signal.size(); ++i)
    {
        xData.push_back(i * 0.1); // 每个点间隔0.1分钟
    }

    // 峰的参数
    int peakStart = 2; // 峰起点索引
    int peakTop = 10;  // 峰顶点索引
    int peakEnd = 18;  // 峰终点索引

    std::cout << std::fixed << std::setprecision(4);

    // 测试原始方法（返回点数）
    double fwhmPoints = calculateFWHM(signal, peakStart, peakTop, peakEnd);
    std::cout << "原始方法 - FWHM (点数): " << fwhmPoints << std::endl;

    // 手动计算真实FWHM（原始方法）
    double timeInterval = xData[1] - xData[0];
    double realFwhmOld = fwhmPoints * timeInterval;
    std::cout << "原始方法 - 真实FWHM (分钟): " << realFwhmOld << " (= " << fwhmPoints << " × " << timeInterval << ")" << std::endl;

    // 测试新方法（直接返回真实X轴差值）
    double fwhmReal = calculateFWHMWithXData(signal, xData, peakStart, peakTop, peakEnd);
    std::cout << "新方法 - 真实FWHM (分钟): " << fwhmReal << std::endl;

    // 测试获取详细信息的方法（只有插值索引）
    FWHMDetails detailsIndex = getFWHMDetails(signal, peakStart, peakTop, peakEnd);
    std::cout << "详细信息方法（插值索引）:" << std::endl;
    std::cout << "  左侧半高点插值索引: " << detailsIndex.leftIndex << std::endl;
    std::cout << "  右侧半高点插值索引: " << detailsIndex.rightIndex << std::endl;
    std::cout << "  半峰宽(点数): " << detailsIndex.fwhmPoints << std::endl;

    // 测试获取详细信息的方法（包含真实坐标）
    FWHMDetails detailsReal = getFWHMDetailsWithXData(signal, xData, peakStart, peakTop, peakEnd);
    std::cout << "详细信息方法（包含真实坐标）:" << std::endl;
    std::cout << "  左侧半高点插值索引: " << detailsReal.leftIndex << std::endl;
    std::cout << "  右侧半高点插值索引: " << detailsReal.rightIndex << std::endl;
    std::cout << "  左侧半高点X坐标: " << detailsReal.leftX << " 分钟" << std::endl;
    std::cout << "  右侧半高点X坐标: " << detailsReal.rightX << " 分钟" << std::endl;
    std::cout << "  半峰宽(点数): " << detailsReal.fwhmPoints << std::endl;
    std::cout << "  半峰宽(真实): " << detailsReal.fwhmReal << " 分钟" << std::endl;

    // 测试获取半高点坐标的方法（向后兼容）
    FWHMPoints fwhmPoints = getFWHMPoints(signal, xData, peakStart, peakTop, peakEnd);
    std::cout << "半高点坐标方法（向后兼容）:" << std::endl;
    std::cout << "  左侧半高点X坐标: " << fwhmPoints.leftX << " 分钟" << std::endl;
    std::cout << "  右侧半高点X坐标: " << fwhmPoints.rightX << " 分钟" << std::endl;
    std::cout << "  半峰宽: " << fwhmPoints.fwhm << " 分钟" << std::endl;

    // 验证结果一致性
    std::cout << "\n=== 结果验证 ===" << std::endl;
    std::cout << "原始方法转换结果: " << realFwhmOld << std::endl;
    std::cout << "新方法直接结果: " << fwhmReal << std::endl;
    std::cout << "详细信息方法结果: " << detailsReal.fwhmReal << std::endl;
    std::cout << "半高点方法结果: " << fwhmPoints.fwhm << std::endl;

    // 验证插值索引的使用
    std::cout << "\n=== 插值索引验证 ===" << std::endl;
    std::cout << "通过插值索引手动计算真实半峰宽:" << std::endl;
    std::cout << "左侧插值索引: " << detailsIndex.leftIndex << std::endl;
    std::cout << "右侧插值索引: " << detailsIndex.rightIndex << std::endl;

    // 手动将插值索引转换为真实X坐标
    double manualLeftX = xData[0] + detailsIndex.leftIndex * (xData[1] - xData[0]);
    double manualRightX = xData[0] + detailsIndex.rightIndex * (xData[1] - xData[0]);
    double manualFwhm = manualRightX - manualLeftX;

    std::cout << "手动计算左侧X坐标: " << manualLeftX << " 分钟" << std::endl;
    std::cout << "手动计算右侧X坐标: " << manualRightX << " 分钟" << std::endl;
    std::cout << "手动计算半峰宽: " << manualFwhm << " 分钟" << std::endl;

    double tolerance = 1e-6;
    bool consistent = (std::abs(realFwhmOld - fwhmReal) < tolerance) &&
                      (std::abs(fwhmReal - detailsReal.fwhmReal) < tolerance) &&
                      (std::abs(detailsReal.fwhmReal - fwhmPoints.fwhm) < tolerance) &&
                      (std::abs(manualFwhm - realFwhmOld) < tolerance);

    std::cout << "结果一致性: " << (consistent ? "✓ 通过" : "✗ 失败") << std::endl;

    // 显示峰的详细信息
    std::cout << "\n=== 峰信息 ===" << std::endl;
    std::cout << "峰顶点: (" << xData[peakTop] << ", " << signal[peakTop] << ")" << std::endl;
    std::cout << "峰高度: " << signal[peakTop] << std::endl;
    std::cout << "半高度: " << signal[peakTop] / 2.0 << std::endl;
    std::cout << "峰起点: (" << xData[peakStart] << ", " << signal[peakStart] << ")" << std::endl;
    std::cout << "峰终点: (" << xData[peakEnd] << ", " << signal[peakEnd] << ")" << std::endl;
}

// 测试新的寻峰函数
void testNewPeakFindingFunctions()
{
    std::cout << "\n=== 测试新的寻峰函数 ===" << std::endl;

    // 创建测试数据：包含多个峰的信号
    std::vector<double> signal = {
        1.0, 1.2, 1.5, 2.0, 3.0, 5.0, 8.0, 12.0, 15.0, 18.0, // 第一个峰
        20.0, 18.0, 15.0, 12.0, 8.0, 5.0, 3.0, 2.0, 1.5, 1.2,
        1.0, 1.1, 1.3, 1.8, 2.5, 4.0, 7.0, 11.0, 14.0, 16.0, // 第二个峰
        17.0, 16.0, 14.0, 11.0, 7.0, 4.0, 2.5, 1.8, 1.3, 1.1, 1.0};

    // 创建对应的X轴数据（时间，单位：分钟）
    std::vector<double> xData;
    for (int i = 0; i < signal.size(); ++i)
    {
        xData.push_back(i * 0.05); // 每个点间隔0.05分钟
    }

    std::cout << std::fixed << std::setprecision(4);

    // 使用原来的寻峰函数
    std::vector<Peak> peaksOld = searchPeaks(signal);
    std::cout << "原来的寻峰函数找到 " << peaksOld.size() << " 个峰:" << std::endl;
    for (size_t i = 0; i < peaksOld.size(); ++i)
    {
        const auto &peak = peaksOld[i];
        double timeInterval = xData.size() >= 2 ? (xData[1] - xData[0]) : 1.0;
        double convertedFwhm = peak.fwhm * timeInterval;
        std::cout << "  峰" << (i + 1) << ": 顶点索引=" << peak.top
                  << ", FWHM(点数)=" << peak.fwhm
                  << ", 转换后FWHM=" << convertedFwhm << " 分钟" << std::endl;
    }

    // 使用新的寻峰函数（带X轴数据）
    std::vector<Peak> peaksNew = searchPeaksWithXData(signal, xData);
    std::cout << "\n新的寻峰函数找到 " << peaksNew.size() << " 个峰:" << std::endl;
    for (size_t i = 0; i < peaksNew.size(); ++i)
    {
        const auto &peak = peaksNew[i];
        std::cout << "  峰" << (i + 1) << ": 顶点索引=" << peak.top
                  << ", FWHM(点数)=" << peak.fwhm
                  << ", 真实FWHM=" << peak.fwhmReal << " 分钟" << std::endl;
    }

    // 对比结果
    std::cout << "\n=== 结果对比 ===" << std::endl;
    if (peaksOld.size() == peaksNew.size())
    {
        double timeInterval = xData.size() >= 2 ? (xData[1] - xData[0]) : 1.0;
        for (size_t i = 0; i < peaksOld.size(); ++i)
        {
            double convertedFwhm = peaksOld[i].fwhm * timeInterval;
            double difference = std::abs(convertedFwhm - peaksNew[i].fwhmReal);
            std::cout << "峰" << (i + 1) << " FWHM对比: 转换结果=" << convertedFwhm
                      << ", 直接计算=" << peaksNew[i].fwhmReal
                      << ", 差值=" << difference << std::endl;
        }
    }
    else
    {
        std::cout << "峰数量不一致，无法对比" << std::endl;
    }
}

int main()
{
    testFWHMImprovements();
    testNewPeakFindingFunctions();
    return 0;
}
