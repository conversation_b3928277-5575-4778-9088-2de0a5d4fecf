#pragma once
#include <QPointF>
#include <QGraphicsEllipseItem>
#include <QGraphicsPathItem>
#include <vector>
struct Peak
{
    int top;                                ///< 峰顶点索引
    int start;                              ///< 峰起点索引
    int end;                                ///< 峰终点索引
    double height;                          ///< 峰高度
    double area;                            ///< 峰面积
    double fwhm;                            ///< 半峰宽
    double snr = 0.0;                       ///< 信噪比 (Signal-to-Noise Ratio)
    QPointF pTop;                           // 峰顶点
    QPointF pStart;                         // 峰起点
    QPointF pEnd;                           // 峰终点
    QGraphicsEllipseItem *item = nullptr;   // 已废弃：不再绘制峰点标记
    QGraphicsPathItem *shadeItem = nullptr; // 用于绘制峰的阴影区域
    QGraphicsTextItem *textItem = nullptr;  // 用于显示峰值的文本标签
    QGraphicsLineItem *lineItem = nullptr;  // 用于连接峰点和文本标签的引线

    // 阈值控制相关状态
    bool visible = true;              // 当前是否可见（受阈值控制）
    bool originalTextVisible = false; // 峰标签的原始显示状态（不受阈值影响）
};

std::vector<double> baselineCorrection(const std::vector<double> &signal, int windowSize = 5);

// 计算信号的导数（斜率）
std::vector<double> calculateDerivative(const std::vector<double> &signal);

// 动态噪声估计（计算信号的局部标准差）
double estimateNoiseLevel(const std::vector<double> &signal, int windowSize);

// 线性插值辅助函数
double linearInterpolate(double x0, double y0, double x1, double y1, double targetY);

// 计算半峰宽（返回点数差值）
double calculateFWHM(const std::vector<double> &signal, int peakStart, int peakTop, int peakEnd);

// 计算半峰宽（返回真实X轴差值）
double calculateFWHMWithXData(const std::vector<double> &signal, const std::vector<double> &xData,
                              int peakStart, int peakTop, int peakEnd);

// 半峰宽详细信息结构体
struct FWHMDetails
{
    double leftIndex;  // 左侧半高点的插值索引
    double rightIndex; // 右侧半高点的插值索引
    double fwhmPoints; // 半峰宽（点数差值）
    double leftX;      // 左侧半高点的X坐标（需要X轴数据）
    double rightX;     // 右侧半高点的X坐标（需要X轴数据）
    double fwhmReal;   // 真实半峰宽值（需要X轴数据）
};

// 获取半峰宽详细信息（包含插值索引）
FWHMDetails getFWHMDetails(const std::vector<double> &signal, int peakStart, int peakTop, int peakEnd);

// 获取半峰宽详细信息（包含真实坐标，需要X轴数据）
FWHMDetails getFWHMDetailsWithXData(const std::vector<double> &signal, const std::vector<double> &xData,
                                    int peakStart, int peakTop, int peakEnd);

// 获取半高处的两个点坐标（保持向后兼容）
struct FWHMPoints
{
    double leftX;  // 左侧半高点的X坐标
    double rightX; // 右侧半高点的X坐标
    double fwhm;   // 半峰宽值（rightX - leftX）
};
FWHMPoints getFWHMPoints(const std::vector<double> &signal, const std::vector<double> &xData,
                         int peakStart, int peakTop, int peakEnd);

std::vector<Peak> findPeaks(const std::vector<double> &signal, int noiseWindow = 20, int minPeakWidth = 5, double slopeFactor = 2.0, double minPeakArea = 0.0,
                            double minPeakHeight = 0.0 // 峰高不指定限制为0.0，此时以噪声10倍高度为峰高限值
);

///
/// \brief 用默认参数，从原始数据寻峰
/// \param original_signal_intensity_vector
/// \return
///
std::vector<Peak> searchPeaks(const std::vector<double> &original_signal_intensity_vector);

///
/// \brief CalcSNRofPeakList 所有峰的信噪比计算
/// \param signal_intensity_vector 寻峰数据
/// \param peakList 寻得的峰列表
/// \param folderWidthOfNoise 噪声窗口倍数，默认10
/// \return
///
std::vector<double> CalcSNRofPeakList(const std::vector<double> &signal_intensity_vector, const std::vector<Peak> &peakList, int folderWidthOfNoise = 10);

// 声明 SNRofSinglePeak 函数
double SNRofSinglePeak(Peak target_peak, const std::vector<double> &original_signal_intensity_vector, int folderWidthOfNoise = 10);
