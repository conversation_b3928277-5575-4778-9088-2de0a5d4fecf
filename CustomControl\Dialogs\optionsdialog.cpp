#include "optionsdialog.h"
#include "ui_optionsdialog.h"
#include <QApplication>
#include <QFontDialog>

// 前向声明，避免直接包含可能引起冲突的头文件
class MainWindow;

OptionsDialog::OptionsDialog(QWidget *parent) : QDialog(parent), ui(new Ui::OptionsDialog)
{
    ui->setupUi(this);

    // 🎯 关键修复：先加载配置设置UI控件值，再连接信号槽
    // 这样避免UI更新时触发信号槽覆盖参数值
    qDebug() << "OptionsDialog::构造函数: 开始初始化，先加载配置再连接信号";
    reloadConfigurations();

    setupValidators();
    connectAll();

    qDebug() << "OptionsDialog::构造函数: 初始化完成";
}

void OptionsDialog::reloadConfigurations()
{
    qDebug() << "OptionsDialog::reloadConfigurations: 重新加载所有OptionsDialog配置";

    // 加载所有OptionsDialog配置
    if (OptionsDialogSettings::loadFromXML()) {
        initUiParams();
        updatePeakFindingUI();
        updatePeakFindingMassUI();
        qDebug() << "OptionsDialog::reloadConfigurations: 所有配置加载成功";
    } else {
        qDebug() << "OptionsDialog::reloadConfigurations: 配置加载失败，使用默认值";
        setupPeakFindingUI(); // 只有在加载失败时才使用默认值
    }

    qDebug() << "OptionsDialog::reloadConfigurations: UI更新完成";
}

OptionsDialog::~OptionsDialog()
{
    delete ui;
}

void OptionsDialog::show()
{
    qDebug() << "OptionsDialog::show: 对话框显示";

    // 🎯 修复：不再重复加载配置，构造函数中已经加载过了
    // 只保存初始寻峰参数用于后续比较
    m_initialPeakParams = OptionsDialogSettings::getPeakFindingParametersSettings();
    m_initialPeakParamsMass = OptionsDialogSettings::getPeakFindingParametersMassSettings();
    qDebug() << "OptionsDialog::show: 保存初始寻峰参数和质谱寻峰参数用于变化检测";

    // 调用基类的show方法
    QDialog::show();

    qDebug() << "OptionsDialog::show: 对话框显示完成";
}

void OptionsDialog::connectAll()
{
    connect(ui->comboBox_spectra, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged), this,
            [=](int index) { ui->stackedWidget_peak->setCurrentIndex(index); });

    // 连接寻峰参数相关信号
    connect(ui->checkBox_use_default, &QCheckBox::toggled, this, &OptionsDialog::onUseDefaultChanged);

    // 平滑参数信号
    connect(ui->comboBox_smooth, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &OptionsDialog::onSmoothParamsChanged);
    connect(ui->comboBox_window_width, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &OptionsDialog::onSmoothParamsChanged);
    connect(ui->doubleSpinBox_gaussian_sigma, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &OptionsDialog::onSmoothParamsChanged);
    connect(ui->spinBox_polyOrder, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onSmoothParamsChanged);

    // 基线校正参数信号
    connect(ui->comboBox_baseline, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &OptionsDialog::onBaselineParamsChanged);
    connect(ui->lineEdit_als_lambda, &QLineEdit::textChanged, this, &OptionsDialog::onBaselineParamsChanged);
    connect(ui->lineEdit_als_p, &QLineEdit::textChanged, this, &OptionsDialog::onBaselineParamsChanged);
    connect(ui->spinBox_als_count, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onBaselineParamsChanged);

    // 寻峰参数信号
    connect(ui->spinBox_noise_window, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onPeakFindParamsChanged);
    connect(ui->spinBox_min_peak_width, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onPeakFindParamsChanged);
    connect(ui->doubleSpinBox_slope, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &OptionsDialog::onPeakFindParamsChanged);
    connect(ui->lineEdit_min_peak_area, &QLineEdit::textChanged, this, &OptionsDialog::onPeakFindParamsChanged);
    connect(ui->lineEdit_min_peak_height, &QLineEdit::textChanged, this, &OptionsDialog::onPeakFindParamsChanged);
    connect(ui->spinBox_folderWidthOfNoise, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onPeakFindParamsChanged); // 添加SNR参数信号连接

    // 连接质谱积分参数相关信号
    connect(ui->checkBox_use_default_mass, &QCheckBox::toggled, this, &OptionsDialog::onUseDefaultMassChanged);

    // 质谱平滑参数信号
    connect(ui->comboBox_smooth_mass, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &OptionsDialog::onSmoothMassParamsChanged);
    connect(ui->comboBox_window_width_mass, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &OptionsDialog::onSmoothMassParamsChanged);
    connect(ui->doubleSpinBox_gaussian_sigma_mass, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &OptionsDialog::onSmoothMassParamsChanged);
    connect(ui->spinBox_polyOrder_mass, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onSmoothMassParamsChanged);

    // 质谱基线校正参数信号
    connect(ui->comboBox_baseline_mass, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &OptionsDialog::onBaselineMassParamsChanged);
    connect(ui->lineEdit_als_lambda_mass, &QLineEdit::textChanged, this, &OptionsDialog::onBaselineMassParamsChanged);
    connect(ui->lineEdit_als_p_mass, &QLineEdit::textChanged, this, &OptionsDialog::onBaselineMassParamsChanged);
    connect(ui->spinBox_als_count_mass, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onBaselineMassParamsChanged);

    // 质谱寻峰参数信号
    connect(ui->spinBox_noise_window_mass, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onPeakFindMassParamsChanged);
    connect(ui->spinBox_min_peak_width_mass, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onPeakFindMassParamsChanged);
    connect(ui->doubleSpinBox_slope_mass, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &OptionsDialog::onPeakFindMassParamsChanged);
    connect(ui->lineEdit_min_peak_area_mass, &QLineEdit::textChanged, this, &OptionsDialog::onPeakFindMassParamsChanged);
    connect(ui->lineEdit_min_peak_height_mass, &QLineEdit::textChanged, this, &OptionsDialog::onPeakFindMassParamsChanged);
    connect(ui->spinBox_folderWidthOfNoise_mass, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onPeakFindMassParamsChanged);

    connect(ui->comboBox_label_Ch, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged), [=](int index) {
        if (index == 6) {
            // 因为我把所有的标签显示类型枚举显示到一起了，所以ALL放在最后的索引和控件索引不一致，需要单独再判断一次
            PeakFindingMarkSettings::getChromatogramsSettings().FiledType = LabelFiledType::All;
        } else if (index == 5) {
            PeakFindingMarkSettings::getChromatogramsSettings().FiledType = LabelFiledType::FWHM;

        } else {
            PeakFindingMarkSettings::getChromatogramsSettings().FiledType = from_uint(index);
        }
        // 立即刷新色谱图的峰标签
        emit peakLabelTypeChanged(GlobalEnums::TrackType::TIC);
    });
    connect(ui->spinBox_threshold, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getChromatogramsSettings().PeakThreshold = value; });
    connect(ui->spinBox_noise, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getChromatogramsSettings().NoisePercent = value; });
    connect(ui->spinBox_splitValue, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getChromatogramsSettings().PeakSplit = value; });
    connect(ui->spinBox_fittingLevel, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getChromatogramsSettings().Fitting = value; });
    connect(ui->spinBox_diff_Ch, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getChromatogramsSettings().Diff = value; });

    connect(ui->comboBox_label_mass, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged), [=](int index) {
        if (index == 5) {
            // 因为我把所有的标签显示类型枚举显示到一起了，所以ALL放在最后的索引和控件索引不一致，需要单独再判断一次
            PeakFindingMarkSettings::getMassSettings().FiledType = LabelFiledType::All;
        } else if (index == 4) {
            PeakFindingMarkSettings::getMassSettings().FiledType = LabelFiledType::FWHM;

        } else {
            PeakFindingMarkSettings::getMassSettings().FiledType = from_uint(index);
        }
        // 立即刷新质谱图的峰标签
        emit peakLabelTypeChanged(GlobalEnums::TrackType::MS);
    });
    connect(ui->spinBox_threshold_mass, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getMassSettings().PeakThreshold = value; });

    connect(ui->spinBox_centroidHeightPercentage, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getMassSettings().CentroidHeightPercentage = value; });

    connect(ui->comboBox_label_DAD, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged), [=](int index) {
        PeakFindingMarkSettings::getDADSettings().FiledType = from_uint(index);
        // 立即刷新DAD图的峰标签
        emit peakLabelTypeChanged(GlobalEnums::TrackType::DAD);
    });

    connect(ui->spinBox_threshold_DAD, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getDADSettings().PeakThreshold = value; });

    connect(ui->spinBox_noise_DAD, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getDADSettings().NoisePercent = value; });

    connect(ui->spinBox_splitValue_DAD, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getDADSettings().PeakSplit = value; });

    connect(ui->spinBox_fittingLevel_DAD, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getDADSettings().Fitting = value; });

    connect(ui->spinBox_diff_DAD, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            [=](int value) { PeakFindingMarkSettings::getDADSettings().Diff = value; });

    // 移除了多余的保存按钮连接，只保留btn_ok

    // 连接外观页面按钮
    connect(ui->btn_setAxisLabelFont, &QPushButton::clicked, this, &OptionsDialog::onSetAxisLabelFontClicked);
    connect(ui->btn_setAutoLabelFont, &QPushButton::clicked, this, &OptionsDialog::onSetAutoLabelFontClicked);
    connect(ui->btn_setCustomLabelDefalut, &QPushButton::clicked, this, &OptionsDialog::onSetCustomLabelDefaultClicked);
    connect(ui->btn_setAxisTitleFont, &QPushButton::clicked, this, &OptionsDialog::onSetAxisTitleFontClicked);
    connect(ui->btn_setTitleFont, &QPushButton::clicked, this, &OptionsDialog::onSetTitleFontClicked);

    // 连接线宽控件
    connect(ui->spinBox_lineWidth, QOverload<int>::of(&QSpinBox::valueChanged), this, &OptionsDialog::onLineWidthChanged);

    // 只保留OK按钮，用于保存所有参数
    connect(ui->btn_ok, &QPushButton::clicked, this, &OptionsDialog::onOkClicked);
}

void OptionsDialog::initUiParams()
{
    // 设置色谱图参数
    PeakFindingChromatograms &peakFindingChromatograms = PeakFindingMarkSettings::getChromatogramsSettings();
    ui->comboBox_label_Ch->setCurrentIndex(to_uint(peakFindingChromatograms.FiledType));
    ui->spinBox_threshold->setValue(peakFindingChromatograms.PeakThreshold);
    ui->spinBox_noise->setValue(peakFindingChromatograms.NoisePercent);
    ui->spinBox_splitValue->setValue(peakFindingChromatograms.PeakSplit);
    ui->spinBox_fittingLevel->setValue(peakFindingChromatograms.Fitting);
    ui->spinBox_diff_Ch->setValue(peakFindingChromatograms.Diff);

    // 设置质谱图参数
    PeakFindingMass &peakFindingMass = PeakFindingMarkSettings::getMassSettings();
    ui->comboBox_label_mass->setCurrentIndex(to_uint(peakFindingMass.FiledType));
    ui->spinBox_threshold_mass->setValue(peakFindingMass.PeakThreshold);
    ui->spinBox_centroidHeightPercentage->setValue(peakFindingMass.CentroidHeightPercentage);

    // 设置紫外谱图
    PeakFindingDAD &peakFindingDAD = PeakFindingMarkSettings::getDADSettings();
    ui->comboBox_label_DAD->setCurrentIndex(to_uint(peakFindingDAD.FiledType));
    ui->spinBox_threshold_DAD->setValue(peakFindingDAD.PeakThreshold);
    ui->spinBox_noise_DAD->setValue(peakFindingDAD.NoisePercent);
    ui->spinBox_splitValue_DAD->setValue(peakFindingDAD.PeakSplit);
    ui->spinBox_fittingLevel_DAD->setValue(peakFindingDAD.Fitting);
    ui->spinBox_diff_DAD->setValue(peakFindingDAD.Diff);

    // 设置外观参数
    const AppearanceSettings &appearanceSettings = OptionsDialogSettings::getAppearanceSettings();
    ui->spinBox_lineWidth->setValue(static_cast<int>(appearanceSettings.spectrumLineWidth));
}

// ==================== 寻峰参数相关方法实现 ====================

PeakFindingParameters OptionsDialog::getPeakFindingParameters() const
{
    return OptionsDialogSettings::getPeakFindingParametersSettings();
}

AppearanceSettings OptionsDialog::getAppearanceSettings() const
{
    return OptionsDialogSettings::getAppearanceSettings();
}

void OptionsDialog::setupPeakFindingUI()
{
    qDebug() << "OptionsDialog::setupPeakFindingUI: 设置寻峰UI默认值";

    // 使用结构体的默认构造函数值来设置UI
    PeakFindingParameters defaultParams;
    PeakFindingParametersMass defaultParamsMass;

    // 设置液相参数默认值
    ui->checkBox_use_default->setChecked(defaultParams.useDefault);

    // 设置平滑参数默认值
    ui->comboBox_smooth->setCurrentIndex(defaultParams.smoothType);

    // 设置窗口大小 - 需要找到对应的索引
    QString windowSizeStr = QString::number(defaultParams.windowSize);
    int windowIndex = ui->comboBox_window_width->findText(windowSizeStr);
    if (windowIndex >= 0) {
        ui->comboBox_window_width->setCurrentIndex(windowIndex);
    } else {
        ui->comboBox_window_width->setCurrentIndex(1); // 默认索引1
    }

    ui->doubleSpinBox_gaussian_sigma->setValue(defaultParams.gaussianSigma);
    ui->spinBox_polyOrder->setValue(defaultParams.polyOrder);

    // 设置基线校正参数默认值
    ui->comboBox_baseline->setCurrentIndex(defaultParams.baselineType);
    ui->lineEdit_als_lambda->setText(QString::number(defaultParams.alsLambda, 'e', 0));
    ui->lineEdit_als_p->setText(QString::number(defaultParams.alsP));
    ui->spinBox_als_count->setValue(defaultParams.alsMaxIter);

    // 设置寻峰参数默认值
    ui->spinBox_noise_window->setValue(defaultParams.noiseWindow);
    ui->spinBox_min_peak_width->setValue(defaultParams.minPeakWidth);
    ui->doubleSpinBox_slope->setValue(defaultParams.slopeFactor);
    ui->lineEdit_min_peak_area->setText(QString::number(defaultParams.minPeakArea));
    ui->lineEdit_min_peak_height->setText(QString::number(defaultParams.minPeakHeight));
    ui->spinBox_folderWidthOfNoise->setValue(defaultParams.folderWidthOfNoise);

    // 设置质谱参数默认值
    ui->checkBox_use_default_mass->setChecked(defaultParamsMass.useDefault_mass);

    // 设置质谱平滑参数默认值
    ui->comboBox_smooth_mass->setCurrentIndex(defaultParamsMass.smoothType_mass);

    // 设置质谱窗口大小
    QString windowSizeStrMass = QString::number(defaultParamsMass.windowSize_mass);
    int windowIndexMass = ui->comboBox_window_width_mass->findText(windowSizeStrMass);
    if (windowIndexMass >= 0) {
        ui->comboBox_window_width_mass->setCurrentIndex(windowIndexMass);
    } else {
        ui->comboBox_window_width_mass->setCurrentIndex(1); // 默认索引1
    }

    ui->doubleSpinBox_gaussian_sigma_mass->setValue(defaultParamsMass.gaussianSigma_mass);
    ui->spinBox_polyOrder_mass->setValue(defaultParamsMass.polyOrder_mass);

    // 设置质谱基线校正参数默认值
    ui->comboBox_baseline_mass->setCurrentIndex(defaultParamsMass.baselineType_mass);
    ui->lineEdit_als_lambda_mass->setText(QString::number(defaultParamsMass.alsLambda_mass, 'e', 0));
    ui->lineEdit_als_p_mass->setText(QString::number(defaultParamsMass.alsP_mass));
    ui->spinBox_als_count_mass->setValue(defaultParamsMass.alsMaxIter_mass);

    // 设置质谱寻峰参数默认值
    ui->spinBox_noise_window_mass->setValue(defaultParamsMass.noiseWindow_mass);
    ui->spinBox_min_peak_width_mass->setValue(defaultParamsMass.minPeakWidth_mass);
    ui->doubleSpinBox_slope_mass->setValue(defaultParamsMass.slopeFactor_mass);
    ui->lineEdit_min_peak_area_mass->setText(QString::number(defaultParamsMass.minPeakArea_mass));
    ui->lineEdit_min_peak_height_mass->setText(QString::number(defaultParamsMass.minPeakHeight_mass));
    ui->spinBox_folderWidthOfNoise_mass->setValue(defaultParamsMass.folderWidthOfNoise_mass);

    qDebug() << "OptionsDialog::setupPeakFindingUI: 默认值设置完成";
}

void OptionsDialog::setupValidators()
{
    // 设置ALS lambda的正则表达式验证器（科学计数法）
    QRegularExpression lambdaRegex("^[1-9](\\.\\d+)?[eE][+-]?\\d+$|^\\d+(\\.\\d+)?$");
    QRegularExpressionValidator *lambdaValidator = new QRegularExpressionValidator(lambdaRegex, this);
    ui->lineEdit_als_lambda->setValidator(lambdaValidator);

    // 设置ALS p的正则表达式验证器（小数）
    QRegularExpression pRegex("^0\\.(0{0,2}[1-9]\\d*|[1-9]\\d*)$|^0\\.1$");
    QRegularExpressionValidator *pValidator = new QRegularExpressionValidator(pRegex, this);
    ui->lineEdit_als_p->setValidator(pValidator);

    // 设置峰面积和峰高的验证器（非负数）
    QRegularExpression areaHeightRegex("^\\d+(\\.\\d+)?$|^0$");
    QRegularExpressionValidator *areaValidator = new QRegularExpressionValidator(areaHeightRegex, this);
    QRegularExpressionValidator *heightValidator = new QRegularExpressionValidator(areaHeightRegex, this);
    ui->lineEdit_min_peak_area->setValidator(areaValidator);
    ui->lineEdit_min_peak_height->setValidator(heightValidator);
}

void OptionsDialog::updatePeakFindingUI()
{
    const PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();

    qDebug() << "OptionsDialog::updatePeakFindingUI: 更新液相UI，参数值:";
    qDebug() << "  useDefault:" << params.useDefault;
    qDebug() << "  smoothType:" << params.smoothType;
    qDebug() << "  windowSize:" << params.windowSize;
    qDebug() << "  gaussianSigma:" << params.gaussianSigma;
    qDebug() << "  polyOrder:" << params.polyOrder;
    qDebug() << "  baselineType:" << params.baselineType;
    qDebug() << "  alsLambda:" << params.alsLambda;
    qDebug() << "  alsP:" << params.alsP;
    qDebug() << "  alsMaxIter:" << params.alsMaxIter;
    qDebug() << "  noiseWindow:" << params.noiseWindow;
    qDebug() << "  minPeakWidth:" << params.minPeakWidth;
    qDebug() << "  slopeFactor:" << params.slopeFactor;
    qDebug() << "  minPeakArea:" << params.minPeakArea;
    qDebug() << "  minPeakHeight:" << params.minPeakHeight;
    qDebug() << "  folderWidthOfNoise:" << params.folderWidthOfNoise;

    // 更新使用默认值复选框
    ui->checkBox_use_default->setChecked(params.useDefault);

    // 更新GroupBox的启用状态
    bool enableCustom = !params.useDefault;
    ui->groupBox_smooth->setEnabled(enableCustom);
    ui->groupBox_baseline->setEnabled(enableCustom);
    ui->groupBox_find_peak->setEnabled(enableCustom);

    // 无论是否使用默认值，都要更新UI控件显示当前配置的值

    // 更新平滑参数
    ui->comboBox_smooth->setCurrentIndex(params.smoothType);
    qDebug() << "  设置平滑方法索引:" << params.smoothType << "当前显示:" << ui->comboBox_smooth->currentText();

    // 设置窗口大小
    QString windowSizeStr = QString::number(params.windowSize);
    int windowIndex = ui->comboBox_window_width->findText(windowSizeStr);
    if (windowIndex >= 0) {
        ui->comboBox_window_width->setCurrentIndex(windowIndex);
        qDebug() << "  设置窗口大小:" << params.windowSize << "索引:" << windowIndex;
    } else {
        qDebug() << "  警告：找不到窗口大小" << params.windowSize << "对应的索引";
    }

    ui->doubleSpinBox_gaussian_sigma->setValue(params.gaussianSigma);
    qDebug() << "  设置高斯σ:" << params.gaussianSigma << "当前显示:" << ui->doubleSpinBox_gaussian_sigma->value();

    ui->spinBox_polyOrder->setValue(params.polyOrder);
    qDebug() << "  设置多项式阶数:" << params.polyOrder << "当前显示:" << ui->spinBox_polyOrder->value();

    // 更新基线校正参数
    ui->comboBox_baseline->setCurrentIndex(params.baselineType);
    qDebug() << "  设置基线方法索引:" << params.baselineType << "当前显示:" << ui->comboBox_baseline->currentText();

    ui->lineEdit_als_lambda->setText(QString::number(params.alsLambda, 'e', 0));
    ui->lineEdit_als_p->setText(QString::number(params.alsP));
    ui->spinBox_als_count->setValue(params.alsMaxIter);

    // 更新寻峰参数
    ui->spinBox_noise_window->setValue(params.noiseWindow);
    ui->spinBox_min_peak_width->setValue(params.minPeakWidth);
    ui->doubleSpinBox_slope->setValue(params.slopeFactor);
    ui->lineEdit_min_peak_area->setText(QString::number(params.minPeakArea));
    ui->lineEdit_min_peak_height->setText(QString::number(params.minPeakHeight));
    ui->spinBox_folderWidthOfNoise->setValue(params.folderWidthOfNoise);

    qDebug() << "OptionsDialog::updatePeakFindingUI: 液相UI更新完成";

    // 验证UI控件是否正确设置
    qDebug() << "OptionsDialog::updatePeakFindingUI: 验证UI控件设置结果:";
    qDebug() << "  窗口大小控件显示:" << ui->comboBox_window_width->currentText() << "期望:" << params.windowSize;
    qDebug() << "  高斯σ控件显示:" << ui->doubleSpinBox_gaussian_sigma->value() << "期望:" << params.gaussianSigma;
    qDebug() << "  噪声窗口控件显示:" << ui->spinBox_noise_window->value() << "期望:" << params.noiseWindow;
}

void OptionsDialog::updatePeakFindingMassUI()
{
    const PeakFindingParametersMass &params = OptionsDialogSettings::getPeakFindingParametersMassSettings();

    qDebug() << "OptionsDialog::updatePeakFindingMassUI: 更新质谱UI，参数值:";
    qDebug() << "  useDefault_mass:" << params.useDefault_mass;
    qDebug() << "  smoothType_mass:" << params.smoothType_mass;
    qDebug() << "  windowSize_mass:" << params.windowSize_mass;
    qDebug() << "  gaussianSigma_mass:" << params.gaussianSigma_mass;
    qDebug() << "  polyOrder_mass:" << params.polyOrder_mass;
    qDebug() << "  baselineType_mass:" << params.baselineType_mass;
    qDebug() << "  alsLambda_mass:" << params.alsLambda_mass;
    qDebug() << "  alsP_mass:" << params.alsP_mass;
    qDebug() << "  alsMaxIter_mass:" << params.alsMaxIter_mass;
    qDebug() << "  noiseWindow_mass:" << params.noiseWindow_mass;
    qDebug() << "  minPeakWidth_mass:" << params.minPeakWidth_mass;
    qDebug() << "  slopeFactor_mass:" << params.slopeFactor_mass;
    qDebug() << "  minPeakArea_mass:" << params.minPeakArea_mass;
    qDebug() << "  minPeakHeight_mass:" << params.minPeakHeight_mass;
    qDebug() << "  folderWidthOfNoise_mass:" << params.folderWidthOfNoise_mass;

    // 更新使用默认值复选框
    ui->checkBox_use_default_mass->setChecked(params.useDefault_mass);

    // 更新GroupBox的启用状态
    bool enableCustom = !params.useDefault_mass;
    ui->groupBox_smooth_2->setEnabled(enableCustom);
    ui->groupBox_baseline_2->setEnabled(enableCustom);
    ui->groupBox_find_peak_2->setEnabled(enableCustom);

    // 无论是否使用默认值，都要更新UI控件显示当前配置的值

    // 更新平滑参数
    ui->comboBox_smooth_mass->setCurrentIndex(params.smoothType_mass);
    qDebug() << "  设置质谱平滑方法索引:" << params.smoothType_mass << "当前显示:" << ui->comboBox_smooth_mass->currentText();

    // 设置窗口大小
    QString windowSizeStr = QString::number(params.windowSize_mass);
    int windowIndex = ui->comboBox_window_width_mass->findText(windowSizeStr);
    if (windowIndex >= 0) {
        ui->comboBox_window_width_mass->setCurrentIndex(windowIndex);
        qDebug() << "  设置质谱窗口大小:" << params.windowSize_mass << "索引:" << windowIndex;
    } else {
        qDebug() << "  警告：找不到质谱窗口大小" << params.windowSize_mass << "对应的索引";
    }

    ui->doubleSpinBox_gaussian_sigma_mass->setValue(params.gaussianSigma_mass);
    qDebug() << "  设置质谱高斯σ:" << params.gaussianSigma_mass << "当前显示:" << ui->doubleSpinBox_gaussian_sigma_mass->value();

    ui->spinBox_polyOrder_mass->setValue(params.polyOrder_mass);
    qDebug() << "  设置质谱多项式阶数:" << params.polyOrder_mass << "当前显示:" << ui->spinBox_polyOrder_mass->value();

    // 更新基线校正参数
    ui->comboBox_baseline_mass->setCurrentIndex(params.baselineType_mass);
    qDebug() << "  设置质谱基线方法索引:" << params.baselineType_mass << "当前显示:" << ui->comboBox_baseline_mass->currentText();

    ui->lineEdit_als_lambda_mass->setText(QString::number(params.alsLambda_mass, 'e', 0));
    ui->lineEdit_als_p_mass->setText(QString::number(params.alsP_mass));
    ui->spinBox_als_count_mass->setValue(params.alsMaxIter_mass);

    // 更新寻峰参数
    ui->spinBox_noise_window_mass->setValue(params.noiseWindow_mass);
    ui->spinBox_min_peak_width_mass->setValue(params.minPeakWidth_mass);
    ui->doubleSpinBox_slope_mass->setValue(params.slopeFactor_mass);
    ui->lineEdit_min_peak_area_mass->setText(QString::number(params.minPeakArea_mass));
    ui->lineEdit_min_peak_height_mass->setText(QString::number(params.minPeakHeight_mass));
    ui->spinBox_folderWidthOfNoise_mass->setValue(params.folderWidthOfNoise_mass);

    qDebug() << "OptionsDialog::updatePeakFindingMassUI: 质谱UI更新完成";

    // 验证UI控件是否正确设置
    qDebug() << "OptionsDialog::updatePeakFindingMassUI: 验证UI控件设置结果:";
    qDebug() << "  质谱窗口大小控件显示:" << ui->comboBox_window_width_mass->currentText() << "期望:" << params.windowSize_mass;
    qDebug() << "  质谱高斯σ控件显示:" << ui->doubleSpinBox_gaussian_sigma_mass->value() << "期望:" << params.gaussianSigma_mass;
    qDebug() << "  质谱噪声窗口控件显示:" << ui->spinBox_noise_window_mass->value() << "期望:" << params.noiseWindow_mass;
}

void OptionsDialog::updatePeakFindingConfig()
{
    qDebug() << "=== 更新寻峰配置参数 ===";

    PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();

    // 更新使用默认参数标志
    params.useDefault = ui->checkBox_use_default->isChecked();

    // 更新平滑参数
    params.smoothType = ui->comboBox_smooth->currentIndex();
    params.windowSize = ui->comboBox_window_width->currentText().toInt();
    params.gaussianSigma = ui->doubleSpinBox_gaussian_sigma->value();
    params.polyOrder = ui->spinBox_polyOrder->value();

    // 更新基线校正参数
    params.baselineType = ui->comboBox_baseline->currentIndex();
    params.alsLambda = ui->lineEdit_als_lambda->text().toDouble();
    params.alsP = ui->lineEdit_als_p->text().toDouble();
    params.alsMaxIter = ui->spinBox_als_count->value();

    // 更新寻峰参数
    params.noiseWindow = ui->spinBox_noise_window->value();
    params.minPeakWidth = ui->spinBox_min_peak_width->value();
    params.slopeFactor = ui->doubleSpinBox_slope->value();
    params.minPeakArea = ui->lineEdit_min_peak_area->text().toDouble();
    params.minPeakHeight = ui->lineEdit_min_peak_height->text().toDouble();
    params.folderWidthOfNoise = ui->spinBox_folderWidthOfNoise->value();

    // 调试信息：打印更新后的参数
    // qDebug() << "使用默认:" << params.useDefault;
    // qDebug() << "平滑:" << params.smoothType << "窗口:" << params.windowSize;
    // qDebug() << "基线:" << params.baselineType << "ALS λ:" << params.alsLambda;
    // qDebug() << "寻峰 - 噪声窗口:" << params.noiseWindow << "斜率因子:" << params.slopeFactor << "SNR噪声窗口倍数:" << params.folderWidthOfNoise;
}

void OptionsDialog::updatePeakFindingMassConfig()
{
    qDebug() << "=== 更新质谱寻峰配置参数 ===";

    PeakFindingParametersMass &params = OptionsDialogSettings::getPeakFindingParametersMassSettings();

    // 更新使用默认参数标志
    params.useDefault_mass = ui->checkBox_use_default_mass->isChecked();

    // 更新平滑参数
    params.smoothType_mass = ui->comboBox_smooth_mass->currentIndex();
    params.windowSize_mass = ui->comboBox_window_width_mass->currentText().toInt();
    params.gaussianSigma_mass = ui->doubleSpinBox_gaussian_sigma_mass->value();
    params.polyOrder_mass = ui->spinBox_polyOrder_mass->value();

    // 更新基线校正参数
    params.baselineType_mass = ui->comboBox_baseline_mass->currentIndex();
    params.alsLambda_mass = ui->lineEdit_als_lambda_mass->text().toDouble();
    params.alsP_mass = ui->lineEdit_als_p_mass->text().toDouble();
    params.alsMaxIter_mass = ui->spinBox_als_count_mass->value();

    // 更新寻峰参数
    params.noiseWindow_mass = ui->spinBox_noise_window_mass->value();
    params.minPeakWidth_mass = ui->spinBox_min_peak_width_mass->value();
    params.slopeFactor_mass = ui->doubleSpinBox_slope_mass->value();
    params.minPeakArea_mass = ui->lineEdit_min_peak_area_mass->text().toDouble();
    params.minPeakHeight_mass = ui->lineEdit_min_peak_height_mass->text().toDouble();
    params.folderWidthOfNoise_mass = ui->spinBox_folderWidthOfNoise_mass->value();

    qDebug() << "质谱寻峰配置参数更新完成";
}

bool OptionsDialog::validatePeakFindingParams()
{
    const PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();
    if (params.useDefault)
        return true; // 默认参数总是有效的

    // 验证ALS lambda参数
    if (ui->comboBox_baseline->currentIndex() == 1) { // ALS模式
        bool ok;
        double lambda = ui->lineEdit_als_lambda->text().toDouble(&ok);
        if (!ok || lambda < 1e3 || lambda > 1e7) {
            QMessageBox::warning(this, "参数错误", "ALS λ参数必须在1e3到1e7之间");
            return false;
        }

        double p = ui->lineEdit_als_p->text().toDouble(&ok);
        if (!ok || p < 0.001 || p > 0.1) {
            QMessageBox::warning(this, "参数错误", "ALS p参数必须在0.001到0.1之间");
            return false;
        }
    }

    // 验证峰面积和峰高参数
    bool ok;
    double area = ui->lineEdit_min_peak_area->text().toDouble(&ok);
    if (!ok || area < 0) {
        QMessageBox::warning(this, "参数错误", "最小峰面积必须为非负数");
        return false;
    }

    double height = ui->lineEdit_min_peak_height->text().toDouble(&ok);
    if (!ok || height < 0) {
        QMessageBox::warning(this, "参数错误", "最小峰高必须为非负数");
        return false;
    }

    return true;
}

// ==================== 槽函数实现 ====================

void OptionsDialog::onUseDefaultChanged(bool useDefault)
{
    PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();
    params.useDefault = useDefault;

    // 更新GroupBox的启用状态
    ui->groupBox_smooth->setEnabled(!useDefault);
    ui->groupBox_baseline->setEnabled(!useDefault);
    ui->groupBox_find_peak->setEnabled(!useDefault);

    qDebug() << "寻峰参数模式切换:" << (useDefault ? "默认参数" : "自定义参数") << "（临时，未保存）";
}

void OptionsDialog::onSmoothParamsChanged()
{
    const PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();
    if (!params.useDefault) {
        // 只更新临时配置，不保存到XML
        updatePeakFindingConfig();
        qDebug() << "平滑参数已更新（临时，未保存）";
    }
}

void OptionsDialog::onBaselineParamsChanged()
{
    const PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();
    if (!params.useDefault) {
        // 只更新临时配置，不保存到XML
        updatePeakFindingConfig();
        qDebug() << "基线校正参数已更新（临时，未保存）";
    }
}

void OptionsDialog::onPeakFindParamsChanged()
{
    const PeakFindingParameters &params = OptionsDialogSettings::getPeakFindingParametersSettings();
    if (!params.useDefault) {
        // 只更新临时配置，不保存到XML
        updatePeakFindingConfig();
        qDebug() << "寻峰参数已更新（临时，未保存）";
    }
}

// 移除了onSaveClicked方法，功能合并到onOkClicked

// 移除了onResetClicked方法，功能合并到onResetAllOptionsClicked

void OptionsDialog::onClearPeakStatusClicked()
{
    QMessageBox::StandardButton reply =
        QMessageBox::question(this, "清除寻峰状态", "确定要清除所有图表的寻峰状态吗？\n这将允许重新寻峰。", QMessageBox::Yes | QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        // 这里需要实现清除所有图表寻峰状态的逻辑
        // 可以通过信号通知MainWindow或直接访问图表管理器
        QMessageBox::information(this, "清除完成", "所有图表的寻峰状态已清除，可以重新寻峰");
    }
}

// 移除了onSaveAndReintegralClicked方法，重新积分功能合并到onOkClicked

// ==================== 质谱积分参数槽函数实现 ====================

void OptionsDialog::onUseDefaultMassChanged(bool useDefault)
{
    PeakFindingParametersMass &params = OptionsDialogSettings::getPeakFindingParametersMassSettings();
    params.useDefault_mass = useDefault;

    // 更新GroupBox的启用状态
    ui->groupBox_smooth_2->setEnabled(!useDefault);
    ui->groupBox_baseline_2->setEnabled(!useDefault);
    ui->groupBox_find_peak_2->setEnabled(!useDefault);

    qDebug() << "质谱寻峰参数模式切换:" << (useDefault ? "默认参数" : "自定义参数") << "（临时，未保存）";
}

void OptionsDialog::onSmoothMassParamsChanged()
{
    const PeakFindingParametersMass &params = OptionsDialogSettings::getPeakFindingParametersMassSettings();
    if (!params.useDefault_mass) {
        // 只更新临时配置，不保存到XML
        updatePeakFindingMassConfig();
        qDebug() << "质谱平滑参数已更新（临时，未保存）";
    }
}

void OptionsDialog::onBaselineMassParamsChanged()
{
    const PeakFindingParametersMass &params = OptionsDialogSettings::getPeakFindingParametersMassSettings();
    if (!params.useDefault_mass) {
        // 只更新临时配置，不保存到XML
        updatePeakFindingMassConfig();
        qDebug() << "质谱基线校正参数已更新（临时，未保存）";
    }
}

void OptionsDialog::onPeakFindMassParamsChanged()
{
    const PeakFindingParametersMass &params = OptionsDialogSettings::getPeakFindingParametersMassSettings();
    if (!params.useDefault_mass) {
        // 只更新临时配置，不保存到XML
        updatePeakFindingMassConfig();
        qDebug() << "质谱寻峰参数已更新（临时，未保存）";
    }
}

// ==================== 外观页面按钮槽函数实现 ====================

void OptionsDialog::onSetAxisLabelFontClicked()
{
    AppearanceSettings &settings = OptionsDialogSettings::getAppearanceSettings();

    // 创建当前字体，包括Effects
    QFont currentFont(settings.axisLabelFontFamily, settings.axisLabelFontSize);
    currentFont.setBold(settings.axisLabelFontBold);
    currentFont.setItalic(settings.axisLabelFontItalic);
    currentFont.setStrikeOut(settings.axisLabelFontStrikeOut);
    currentFont.setUnderline(settings.axisLabelFontUnderline);

    // 使用系统字体对话框
    bool ok;
    QFont selectedFont = QFontDialog::getFont(&ok, currentFont, this, "选择坐标轴标签字体");

    if (ok) {
        // 更新设置，包括Effects
        settings.axisLabelFontFamily = selectedFont.family();
        settings.axisLabelFontSize = selectedFont.pointSize();
        settings.axisLabelFontBold = selectedFont.bold();
        settings.axisLabelFontItalic = selectedFont.italic();
        settings.axisLabelFontStrikeOut = selectedFont.strikeOut();
        settings.axisLabelFontUnderline = selectedFont.underline();

        qDebug() << "坐标轴标签字体已更新：" << selectedFont.family() << selectedFont.pointSize() << "删除线:" << selectedFont.strikeOut()
                 << "下划线:" << selectedFont.underline();
    }
}

void OptionsDialog::onSetAutoLabelFontClicked()
{
    AppearanceSettings &settings = OptionsDialogSettings::getAppearanceSettings();

    // 创建当前字体，包括Effects
    QFont currentFont(settings.autoLabelFontFamily, settings.autoLabelFontSize);
    currentFont.setBold(settings.autoLabelFontBold);
    currentFont.setItalic(settings.autoLabelFontItalic);
    currentFont.setStrikeOut(settings.autoLabelFontStrikeOut);
    currentFont.setUnderline(settings.autoLabelFontUnderline);

    // 使用系统字体对话框
    bool ok;
    QFont selectedFont = QFontDialog::getFont(&ok, currentFont, this, "选择自动标签字体");

    if (ok) {
        // 更新设置，包括Effects
        settings.autoLabelFontFamily = selectedFont.family();
        settings.autoLabelFontSize = selectedFont.pointSize();
        settings.autoLabelFontBold = selectedFont.bold();
        settings.autoLabelFontItalic = selectedFont.italic();
        settings.autoLabelFontStrikeOut = selectedFont.strikeOut();
        settings.autoLabelFontUnderline = selectedFont.underline();

        qDebug() << "自动标签字体已更新：" << selectedFont.family() << selectedFont.pointSize() << "删除线:" << selectedFont.strikeOut()
                 << "下划线:" << selectedFont.underline();
    }
}

void OptionsDialog::onSetCustomLabelDefaultClicked()
{
    AppearanceSettings &settings = OptionsDialogSettings::getAppearanceSettings();

    // 创建当前字体，包括Effects
    QFont currentFont(settings.customLabelFontFamily, settings.customLabelFontSize);
    currentFont.setBold(settings.customLabelFontBold);
    currentFont.setItalic(settings.customLabelFontItalic);
    currentFont.setStrikeOut(settings.customLabelFontStrikeOut);
    currentFont.setUnderline(settings.customLabelFontUnderline);

    // 使用系统字体对话框
    bool ok;
    QFont selectedFont = QFontDialog::getFont(&ok, currentFont, this, "选择手动标注默认字体");

    if (ok) {
        // 更新设置，包括Effects
        settings.customLabelFontFamily = selectedFont.family();
        settings.customLabelFontSize = selectedFont.pointSize();
        settings.customLabelFontBold = selectedFont.bold();
        settings.customLabelFontItalic = selectedFont.italic();
        settings.customLabelFontStrikeOut = selectedFont.strikeOut();
        settings.customLabelFontUnderline = selectedFont.underline();

        qDebug() << "手动标注默认字体已更新：" << selectedFont.family() << selectedFont.pointSize() << "删除线:" << selectedFont.strikeOut()
                 << "下划线:" << selectedFont.underline();
    }
}

void OptionsDialog::onSetAxisTitleFontClicked()
{
    AppearanceSettings &settings = OptionsDialogSettings::getAppearanceSettings();

    // 创建当前字体，包括Effects
    QFont currentFont(settings.axisTitleFontFamily, settings.axisTitleFontSize);
    currentFont.setBold(settings.axisTitleFontBold);
    currentFont.setItalic(settings.axisTitleFontItalic);
    currentFont.setStrikeOut(settings.axisTitleFontStrikeOut);
    currentFont.setUnderline(settings.axisTitleFontUnderline);

    // 使用系统字体对话框
    bool ok;
    QFont selectedFont = QFontDialog::getFont(&ok, currentFont, this, "选择坐标轴标题字体");

    if (ok) {
        // 更新设置，包括Effects
        settings.axisTitleFontFamily = selectedFont.family();
        settings.axisTitleFontSize = selectedFont.pointSize();
        settings.axisTitleFontBold = selectedFont.bold();
        settings.axisTitleFontItalic = selectedFont.italic();
        settings.axisTitleFontStrikeOut = selectedFont.strikeOut();
        settings.axisTitleFontUnderline = selectedFont.underline();

        qDebug() << "坐标轴标题字体已更新：" << selectedFont.family() << selectedFont.pointSize() << "删除线:" << selectedFont.strikeOut()
                 << "下划线:" << selectedFont.underline();
    }
}

void OptionsDialog::onSetTitleFontClicked()
{
    AppearanceSettings &settings = OptionsDialogSettings::getAppearanceSettings();

    // 创建当前字体，包括Effects
    QFont currentFont(settings.titleFontFamily, settings.titleFontSize);
    currentFont.setBold(settings.titleFontBold);
    currentFont.setItalic(settings.titleFontItalic);
    currentFont.setStrikeOut(settings.titleFontStrikeOut);
    currentFont.setUnderline(settings.titleFontUnderline);

    // 使用系统字体对话框
    bool ok;
    QFont selectedFont = QFontDialog::getFont(&ok, currentFont, this, "选择图层标题字体");

    if (ok) {
        // 更新设置，包括Effects
        settings.titleFontFamily = selectedFont.family();
        settings.titleFontSize = selectedFont.pointSize();
        settings.titleFontBold = selectedFont.bold();
        settings.titleFontItalic = selectedFont.italic();
        settings.titleFontStrikeOut = selectedFont.strikeOut();
        settings.titleFontUnderline = selectedFont.underline();

        qDebug() << "图层标题字体已更新：" << selectedFont.family() << selectedFont.pointSize() << "删除线:" << selectedFont.strikeOut()
                 << "下划线:" << selectedFont.underline();
    }
}

void OptionsDialog::onLineWidthChanged(int value)
{
    AppearanceSettings &settings = OptionsDialogSettings::getAppearanceSettings();
    settings.spectrumLineWidth = static_cast<double>(value);

    qDebug() << "谱图线宽已更新：" << value << "px（临时，未保存）";
}

void OptionsDialog::onOkClicked()
{
    qDebug() << "OptionsDialog::onOkClicked: OK按钮被点击 - 保存所有参数";

    // 1. 验证寻峰参数有效性
    if (!validatePeakFindingParams()) {
        qDebug() << "OptionsDialog::onOkClicked: 寻峰参数验证失败";
        return; // 验证失败，不关闭对话框
    }

    // 2. 更新所有配置参数
    updatePeakFindingConfig();
    updatePeakFindingMassConfig();
    qDebug() << "OptionsDialog::onOkClicked: 配置参数已更新";

    // 3. 检查寻峰参数是否发生变化（与对话框打开时的初始参数比较）
    PeakFindingParameters currentPeakParams = OptionsDialogSettings::getPeakFindingParametersSettings();
    PeakFindingParametersMass currentPeakParamsMass = OptionsDialogSettings::getPeakFindingParametersMassSettings();
    bool peakParamsChanged = (m_initialPeakParams != currentPeakParams);
    bool peakParamsMassChanged = (m_initialPeakParamsMass != currentPeakParamsMass);
    qDebug() << "OptionsDialog::onOkClicked: 液相寻峰参数是否变化：" << peakParamsChanged;
    qDebug() << "OptionsDialog::onOkClicked: 质谱寻峰参数是否变化：" << peakParamsMassChanged;

    // 5. 保存所有OptionsDialog配置到XML和内存
    bool saveSuccess = OptionsDialogSettings::saveToXML();
    if (saveSuccess) {
        qDebug() << "OptionsDialog::onOkClicked: 所有配置已保存到OptionsDialog.xml";
    } else {
        qDebug() << "OptionsDialog::onOkClicked: 配置保存失败";
        QMessageBox::warning(this, "保存失败", "配置保存失败，请检查文件权限。");
    }

    // 6. 根据寻峰参数是否变化决定是否重新积分
    if (!peakParamsChanged && !peakParamsMassChanged) {
        // 寻峰参数未变化，只保存配置，不重新积分
        if (saveSuccess) {
            QMessageBox::information(this, "保存成功", "外观设置已保存到OptionsDialog.xml");
        } else {
            QMessageBox::warning(this, "保存失败", "配置保存失败，请检查文件权限。");
        }

        qDebug() << "OptionsDialog::onOkClicked: 寻峰参数未变化，不需要重新积分，关闭对话框";
        this->accept(); // 关闭对话框
        return;
    }

    // 7. 寻峰参数发生变化，需要重新积分
    qDebug() << "OptionsDialog::onOkClicked: 寻峰参数发生变化，开始重新积分";
    QWidget *mainWindow = nullptr;

    // 查找MainWindow实例
    foreach (QWidget *widget, QApplication::topLevelWidgets()) {
        if (widget && widget->metaObject()->className() == QString("MainWindow")) {
            mainWindow = widget;
            break;
        }
    }

    if (mainWindow) {
        QString reintegrateMessage;

        // 根据参数变化情况调用不同的重新积分方法
        if (peakParamsChanged && peakParamsMassChanged) {
            // 液相和质谱参数都变化，重新积分所有曲线
            qDebug() << "OptionsDialog::onOkClicked: 液相和质谱参数都变化，重新积分所有曲线";
            QMetaObject::invokeMethod(mainWindow, "reintegrateAllCharts", Qt::QueuedConnection);
            reintegrateMessage = "正在重新积分所有TIC/XIC/MASS曲线...";
        } else if (peakParamsChanged) {
            // 只有液相参数变化，只重新积分TIC/XIC曲线
            qDebug() << "OptionsDialog::onOkClicked: 液相参数变化，重新积分TIC/XIC曲线";
            QMetaObject::invokeMethod(mainWindow, "reintegrateChromatogramCharts", Qt::QueuedConnection);
            reintegrateMessage = "正在重新积分所有TIC/XIC曲线...";
        } else if (peakParamsMassChanged) {
            // 只有质谱参数变化，只重新积分MASS曲线
            qDebug() << "OptionsDialog::onOkClicked: 质谱参数变化，重新积分MASS曲线";
            QMetaObject::invokeMethod(mainWindow, "reintegrateMassCharts", Qt::QueuedConnection);
            reintegrateMessage = "正在重新积分所有MASS曲线...";
        }

        // 显示成功信息
        if (saveSuccess) {
            QMessageBox::information(this, "保存并重新积分", QString("寻峰参数已保存到OptionsDialog.xml\n%1").arg(reintegrateMessage));
        } else {
            QMessageBox::information(this, "重新积分", QString("寻峰参数保存失败，但正在使用当前参数重新积分曲线...\n%1").arg(reintegrateMessage));
        }

        qDebug() << "OptionsDialog::onOkClicked: 操作完成，关闭对话框";
        this->accept(); // 关闭对话框并返回Accepted
    } else {
        qDebug() << "OptionsDialog::onOkClicked: 未找到主窗口实例";

        if (saveSuccess) {
            QString changedParamsInfo;
            if (peakParamsChanged && peakParamsMassChanged) {
                changedParamsInfo = "液相和质谱寻峰参数";
            } else if (peakParamsChanged) {
                changedParamsInfo = "液相寻峰参数";
            } else if (peakParamsMassChanged) {
                changedParamsInfo = "质谱寻峰参数";
            }

            QMessageBox::information(this, "保存成功", QString("%1已保存到OptionsDialog.xml\n但无法自动重新积分，请手动重新加载数据。").arg(changedParamsInfo));
            this->accept(); // 保存成功时关闭对话框
        } else {
            QMessageBox::warning(this, "操作失败", "寻峰参数保存失败且无法重新积分，请检查文件权限后重试。");
            // 保存失败时不关闭对话框，让用户重试
        }
    }
}

void OptionsDialog::onResetAllOptionsClicked()
{
    qDebug() << "OptionsDialog::onResetAllOptionsClicked: Reset all options按钮被点击";

    QMessageBox::StandardButton reply = QMessageBox::question(
        this, "重置所有选项", "确定要重置所有选项到默认值吗？\n这将重置外观设置、寻峰参数和峰识别标记设置。", QMessageBox::Yes | QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        // 重置所有配置为默认值
        OptionsDialogSettings::getPeakFindingParametersSettings() = PeakFindingParameters();
        OptionsDialogSettings::getPeakFindingParametersMassSettings() = PeakFindingParametersMass();
        OptionsDialogSettings::getAppearanceSettings() = AppearanceSettings();
        OptionsDialogSettings::getChromatogramsSettings() = PeakFindingChromatograms();
        OptionsDialogSettings::getMassSettings() = PeakFindingMass();
        OptionsDialogSettings::getDADSettings() = PeakFindingDAD();

        qDebug() << "OptionsDialog::onResetAllOptionsClicked: 所有配置已重置为默认值";

        // 更新UI显示
        updatePeakFindingUI();
        updatePeakFindingMassUI();
        initUiParams();

        // 保存重置后的配置
        if (OptionsDialogSettings::saveToXML()) {
            QMessageBox::information(this, "重置成功", "所有选项已重置为默认值并保存");
            qDebug() << "OptionsDialog::onResetAllOptionsClicked: 重置配置保存成功";
        } else {
            QMessageBox::warning(this, "重置失败", "重置成功但保存失败，请检查文件权限");
            qDebug() << "OptionsDialog::onResetAllOptionsClicked: 重置配置保存失败";
        }
    }
}
