#ifndef AVGMASSDISPLAYMANAGER_H
#define AVGMASSDISPLAYMANAGER_H

#include <QObject>
#include <QMap>
#include <QVector>
#include <QMutex>
#include <QMutexLocker>
#include "Globals/GlobalDefine.h"
#include "Globals/GlobalEnums.h"

// 前向声明
class LxTicChartData;
class FileData;
class LxChart;
class LxChartData;

/**
 * @brief 平均质谱显示管理器
 *
 * 负责管理自定义区域的平均质谱计算和显示，包括：
 * 1. 从自定义区域计算平均质谱
 * 2. 创建对应的LxTicChartData对象
 * 3. 管理平均质谱与自定义区域的关联
 * 4. 处理删除时的双向清理
 */
class AvgMassDisplayManager : public QObject
{
    Q_OBJECT

public:
    explicit AvgMassDisplayManager(QObject *parent = nullptr);
    ~AvgMassDisplayManager();

    // 获取单例实例
    static AvgMassDisplayManager *instance();

    /**
     * @brief 为自定义区域创建平均质谱
     * @param customRange 自定义区域引用
     * @param chart LxChart指针，用于获取数据和添加TIC
     * @return 是否创建成功
     */
    bool createAvgMassForCustomRange(GlobalDefine::CustomRange &customRange, LxChart *chart);

    /**
     * @brief 删除自定义区域的平均质谱
     * @param customRange 自定义区域引用
     * @param chart LxChart指针，用于移除TIC
     * @return 是否删除成功
     */
    bool removeAvgMassForCustomRange(GlobalDefine::CustomRange &customRange, LxChart *chart);

    /**
     * @brief 检查自定义区域是否有关联的平均质谱
     * @param customRange 自定义区域引用
     * @return 是否有关联的平均质谱
     */
    bool hasAvgMassForCustomRange(const GlobalDefine::CustomRange &customRange) const;

    /**
     * @brief 通过平均质谱TIC的UniqueID查找对应的自定义区域索引
     * @param uniqueId 平均质谱TIC的UniqueID
     * @return 自定义区域索引，-1表示未找到
     */
    int findCustomRangeIndexByTicId(const QString &uniqueId) const;

    /**
     * @brief 清除所有平均质谱数据
     */
    void clearAllAvgMass();

    /**
     * @brief 处理平均质谱TIC被删除的回调
     * @param uniqueId 被删除的平均质谱TIC的UniqueID
     * @param chart LxChart指针，用于删除对应的自定义区域
     * @return 是否处理成功
     */
    bool onAvgMassTicDeleted(const QString &uniqueId, LxChart *chart);

private:
    /**
     * @brief 计算自定义区域内的平均质谱数据
     * @param customRange 自定义区域
     * @param chart LxChart指针
     * @param avgMassX 输出：平均质谱X数据
     * @param avgMassY 输出：平均质谱Y数据
     * @return 是否计算成功
     */
    bool calculateAvgMassData(const GlobalDefine::CustomRange &customRange,
                              LxChart *chart,
                              QVector<double> &avgMassX,
                              QVector<double> &avgMassY);

    // getCustomRangeTicPoints方法已移动到AvgMassManager中

    /**
     * @brief 创建平均质谱的TIC数据对象
     * @param avgMassX 平均质谱X数据
     * @param avgMassY 平均质谱Y数据
     * @param customRangeIndex 自定义区域索引
     * @return LxTicChartData指针
     */
    LxTicChartData *createAvgMassTicData(const QVector<double> &avgMassX,
                                         const QVector<double> &avgMassY,
                                         int customRangeIndex);

private:
    // 平均质谱TIC的UniqueID到自定义区域索引的映射
    QMap<QString, int> m_avgMassTicToCustomRangeMap;

    // 线程安全保护
    mutable QMutex m_mutex;

    // 单例实例
    static AvgMassDisplayManager *s_instance;
    static QMutex s_instanceMutex;

signals:
    /**
     * @brief 平均质谱创建完成信号
     * @param customRangeIndex 自定义区域索引
     * @param ticData 创建的TIC数据
     */
    void avgMassCreated(int customRangeIndex, LxTicChartData *ticData);

    /**
     * @brief 平均质谱删除完成信号
     * @param customRangeIndex 自定义区域索引
     */
    void avgMassRemoved(int customRangeIndex);
};

#endif // AVGMASSDISPLAYMANAGER_H
