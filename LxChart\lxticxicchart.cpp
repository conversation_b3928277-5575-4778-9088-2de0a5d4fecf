#include "lxticxicchart.h"
#include <QMouseEvent>
#include <QDebug>
#include <QCoreApplication>
#include <QEventLoop>
#include <QTimer>
#include <QElapsedTimer>
#include <QMessageBox>
// 移除DataReader DLL依赖，使用AvgMassManager
// #include "FileData/datareader.h"
#include "FileData/avgmassmanager.h"
#include "Algorithm/PeakFind/peakfind.h"
#include "FileData/taskmanager.h"
#include "Algorithm/PeakFind/wh_peakSearch.h"
#include "Algorithm/PeakFind/wh_signal_smothing.h"
#include "Algorithm/PeakFind/wh_baselineCorrect.h"

// 静态变量初始化
bool LxTicXicChart::s_isLoadingMass = false;

LxTicXicChart::LxTicXicChart(QWidget *parent)
    : LxChart(QList<GlobalEnums::TrackType>{GlobalEnums::TrackType::TIC, GlobalEnums::TrackType::XIC}, parent), m_batchAddMode(false)
{
    // 初始化批量更新定时器
    m_batchUpdateTimer = new QTimer(this);
    m_batchUpdateTimer->setSingleShot(true);
    m_batchUpdateTimer->setInterval(50); // 50ms延迟，确保所有数据都添加完成
    connect(m_batchUpdateTimer, &QTimer::timeout, this, [this]()
            {
        qDebug() << "LxTicXicChart: 批量更新定时器触发，执行延迟的坐标轴更新";
        UpdateGlobalRange();
        m_batchAddMode = false; });

    initTicXicChart();
}

LxTicXicChart::~LxTicXicChart()
{
    qDebug() << "LxTicXicChart::~LxTicXicChart: 开始析构";

    // 强制处理所有待处理的事件
    QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents);

    // 清理所有数据
    try
    {
        clearAllTicXicData();
        qDebug() << "LxTicXicChart::~LxTicXicChart: 数据清理完成";
    }
    catch (...)
    {
        qDebug() << "LxTicXicChart::~LxTicXicChart: 数据清理时发生异常";
    }

    // 再次处理事件确保清理完成
    QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents);

    qDebug() << "LxTicXicChart::~LxTicXicChart: 析构完成";
}

void LxTicXicChart::initTicXicChart()
{
    // 设置图表标题
    m_chart->setTitle(tr("TIC/XIC 组合图"));

    // 每个曲线使用自己的随机颜色，不需要颜色同步

    // 连接PeakFind信号
    connectPeakFindSignals();
}

void LxTicXicChart::addTicData(TicChartData *ticData)
{
    if (!ticData)
    {
        qDebug() << "LxTicXicChart::addTicData: ticData为空";
        return;
    }

    int eventId = ticData->getEventNum();
    QString paramPath = ticData->getParamPath();

    // 创建复合键
    QString compositeKey = generateCompositeKey(paramPath, eventId);

    // 检查是否已存在相同文件的相同事件ID的TIC数据
    if (m_ticDataMap.contains(compositeKey))
    {
        qDebug() << "LxTicXicChart::addTicData: 相同文件的事件ID" << eventId << "已存在，先移除旧数据";
        internalRemoveTicData(compositeKey);
    }

    // 添加到基类的数据管理
    AddLxChartData(ticData);

    // 添加到TIC数据映射，使用复合键
    m_ticDataMap[compositeKey] = ticData;

    qDebug() << "LxTicXicChart::addTicData: 添加TIC到map，复合键:" << compositeKey
             << "，UniqueID:" << ticData->getUniqueID()
             << "，文件路径:" << ticData->getParamPath()
             << "，事件ID:" << ticData->getEventNum()
             << "，对象地址:" << (void *)ticData
             << "，当前map大小:" << m_ticDataMap.size();

    // 新的单一曲线管理，不需要设置TIC状态

    // 检查是否存在对应的XIC数据（通过事件ID和文件路径匹配）
    bool hasMatchingXic = false;
    for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
    {
        XicChartData *xicData = it.value();
        if (xicData && xicData->getTic_event_id() == eventId && xicData->getParamPath() == paramPath)
        {
            hasMatchingXic = true;
            break;
        }
    }

    if (hasMatchingXic)
    {
        emit ticXicPairAdded(eventId);
        qDebug() << "LxTicXicChart::addTicData: 发射ticXicPairAdded信号，事件ID:" << eventId;
    }

    // qDebug() << "LxTicXicChart::addTicData: 添加TIC数据，事件ID:" << eventId;

    // 检查是否已设置背景区域，如果设置了则自动计算新TIC的平均质谱
    checkAndCalculateAvgMassForNewTic(ticData);

    // 自动对新增的TIC曲线进行积分计算
    performAutoIntegrationForNewCurve(ticData);
}

void LxTicXicChart::addXicData(XicChartData *xicData)
{
    QElapsedTimer timer;
    timer.start();

    qDebug() << "LxTicXicChart::addXicData: 方法开始执行";

    if (!xicData)
    {
        qDebug() << "LxTicXicChart::addXicData: xicData为空";
        return;
    }

    qDebug() << "LxTicXicChart::addXicData: xicData指针有效，开始获取属性";

    int eventId = xicData->getTic_event_id(); // XIC通过tic_event_id关联TIC
    QString paramPath = xicData->getParamPath();

    qDebug() << "LxTicXicChart::addXicData: 开始添加XIC数据，事件ID:" << eventId << "，路径:" << paramPath << "，数据点数:" << xicData->getData().size();

    qDebug() << "LxTicXicChart::addXicData: 准备生成复合键";
    // 创建复合键
    QString compositeKey = generateCompositeKey(paramPath, eventId);
    qDebug() << "LxTicXicChart::addXicData: 复合键生成成功:" << compositeKey;

    // 调试：打印当前所有XIC映射
    qDebug() << "LxTicXicChart::addXicData: 当前XIC映射数量:" << m_xicDataMap.size();
    for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
    {
        qDebug() << "LxTicXicChart::addXicData: 现有XIC映射 -" << it.key() << ":" << it.value()->getUniqueID();
    }

    // 检查是否已存在相同UniqueID的XIC数据（理论上不应该发生，因为UniqueID是唯一的）
    if (m_xicDataMap.contains(xicData->getUniqueID()))
    {
        qDebug() << "LxTicXicChart::addXicData: 警告：发现重复的UniqueID，先移除旧数据";
        RemoveLxChartDataByUniqueID(xicData->getUniqueID());
    }

    // 查找对应的TIC数据，通过TIC来创建XIC
    TicChartData *ticData = m_ticDataMap.value(compositeKey, nullptr);
    if (ticData)
    {
        // 通过TicChartData创建新的XIC数据
        QUuid xicId = ticData->createXicData();
        if (!xicId.isNull())
        {
            // 获取新创建的XIC数据
            XicChartData *createdXicData = ticData->getXicData(xicId);
            if (createdXicData)
            {
                // 复制外部XIC数据到内部创建的XIC
                QVector<QPointF> sourceData = xicData->getData();
                qDebug() << "LxTicXicChart::addXicData: 准备复制XIC数据，源数据点数:" << sourceData.size();
                if (!sourceData.isEmpty())
                {
                    qDebug() << "   源数据第一个点:" << sourceData.first();
                    qDebug() << "   源数据最后一个点:" << sourceData.last();
                    qDebug() << "   源数据范围: X[" << xicData->getMinX() << "~" << xicData->getMaxX() << "] Y[" << xicData->getMinY() << "~"
                             << xicData->getMaxY() << "]";
                }

                createdXicData->setData(sourceData);
                createdXicData->setTic_event_id(eventId);

                // 验证复制后的数据
                QVector<QPointF> copiedData = createdXicData->getData();
                qDebug() << "LxTicXicChart::addXicData: 复制后数据验证，数据点数:" << copiedData.size();
                if (!copiedData.isEmpty())
                {
                    qDebug() << "   复制后第一个点:" << copiedData.first();
                    qDebug() << "   复制后最后一个点:" << copiedData.last();
                    qDebug() << "   复制后范围: X[" << createdXicData->getMinX() << "~" << createdXicData->getMaxX() << "] Y[" << createdXicData->getMinY()
                             << "~" << createdXicData->getMaxY() << "]";
                }

                // 添加XIC到基类的数据管理（这会创建LxChartLegend并建立交互连接）
                AddLxChartData(createdXicData);

                // 添加到XIC映射（使用XIC的UniqueID作为键，确保每个XIC都有唯一键）
                m_xicDataMap[createdXicData->getUniqueID()] = createdXicData;

                qDebug() << "LxTicXicChart::addXicData: 通过TIC创建新XIC成功，XIC ID:" << xicId.toString() << "，XIC标题:" << createdXicData->getTitle();

                // 自动对新增的XIC曲线进行积分计算
                performAutoIntegrationForNewCurve(createdXicData);
            }
        }
    }
    else
    {
        qDebug() << "LxTicXicChart::addXicData: 未找到对应的TIC数据，直接添加外部XIC";

        // 直接添加外部XIC数据（这会创建LxChartLegend并建立交互连接）
        AddLxChartData(xicData);

        // 添加到XIC数据映射（使用XIC的UniqueID作为键）
        m_xicDataMap[xicData->getUniqueID()] = xicData;

        qDebug() << "LxTicXicChart::addXicData: 直接添加外部XIC数据（无TIC关联）";

        // 自动对新增的XIC曲线进行积分计算
        performAutoIntegrationForNewCurve(xicData);
    }

    qDebug() << "LxTicXicChart::addXicData: 找到对应的TIC数据，已建立关联";

    emit ticXicPairAdded(eventId);
    qDebug() << "LxTicXicChart::addXicData: 发射ticXicPairAdded信号，事件ID:" << eventId;

    qDebug() << "LxTicXicChart::addXicData: 添加XIC数据完成，事件ID:" << eventId;

    // 确保所有Qt对象操作都已完成
    QCoreApplication::processEvents();

    qDebug() << "LxTicXicChart::addXicData: 方法完全结束";
}

void LxTicXicChart::addTicXicPair(TicChartData *ticData, XicChartData *xicData)
{
    if (!ticData || !xicData)
    {
        qDebug() << "LxTicXicChart::addTicXicPair: 数据为空";
        return;
    }

    int ticEventId = ticData->getEventNum();
    int xicEventId = xicData->getTic_event_id();

    // 检查事件ID是否匹配
    if (ticEventId != xicEventId)
    {
        qDebug() << "LxTicXicChart::addTicXicPair: TIC事件ID" << ticEventId << "与XIC事件ID" << xicEventId << "不匹配";
        return;
    }

    // 创建复合键
    QString ticCompositeKey = generateCompositeKey(ticData->getParamPath(), ticEventId);

    // 检查TIC是否已存在，如果不存在则添加
    if (!m_ticDataMap.contains(ticCompositeKey))
    {
        // 添加TIC到基类的数据管理
        AddLxChartData(ticData);
        // 添加到TIC映射
        m_ticDataMap[ticCompositeKey] = ticData;
        qDebug() << "LxTicXicChart::addTicXicPair: 添加新的TIC数据，事件ID:" << ticEventId;
    }

    // 将XIC数据添加到TicChartData内部管理
    QUuid xicId = ticData->createXicData();
    if (!xicId.isNull())
    {
        // 获取新创建的XIC数据
        XicChartData *createdXicData = ticData->getXicData(xicId);
        if (createdXicData)
        {
            // 复制外部XIC数据到内部创建的XIC
            createdXicData->setData(xicData->getData());
            createdXicData->setTic_event_id(xicEventId);

            // 每个曲线使用自己的随机颜色，不需要颜色同步

            // 添加XIC到基类的数据管理（这会创建LxChartLegend并建立交互连接）
            AddLxChartData(createdXicData);

            // 添加到XIC映射（使用XIC的UniqueID作为键，确保每个XIC都有唯一键）
            m_xicDataMap[createdXicData->getUniqueID()] = createdXicData;

            qDebug() << "LxTicXicChart::addTicXicPair: 添加XIC到TIC内部管理，XIC ID:" << xicId.toString() << "，XIC标题:" << createdXicData->getTitle();
        }
    }

    emit ticXicPairAdded(ticEventId);

    qDebug() << "LxTicXicChart::addTicXicPair: 完成TIC/XIC数据对添加，事件ID:" << ticEventId;
}

bool LxTicXicChart::removeTicXicPairByEventId(int eventId)
{
    bool removed = false;

    // 遍历所有TIC数据，找到匹配的事件ID
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end();)
    {
        TicChartData *ticData = it.value();
        if (ticData && ticData->getEventNum() == eventId)
        {
            QString compositeKey = it.key();
            RemoveLxChartDataByUniqueID(ticData->getUniqueID());
            it = m_ticDataMap.erase(it);
            removed = true;

            // 不再需要颜色映射管理
        }
        else
        {
            ++it;
        }
    }

    // 遍历所有XIC数据，找到匹配的事件ID
    for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end();)
    {
        XicChartData *xicData = it.value();
        if (xicData && xicData->getTic_event_id() == eventId)
        {
            QString compositeKey = it.key();
            RemoveLxChartDataByUniqueID(xicData->getUniqueID());
            it = m_xicDataMap.erase(it);
            removed = true;

            // 不再需要颜色映射管理
        }
        else
        {
            ++it;
        }
    }

    if (removed)
    {
        emit ticXicPairRemoved(eventId);
        qDebug() << "LxTicXicChart::removeTicXicPairByEventId: 移除事件ID" << eventId << "的数据";
    }

    return removed;
}

bool LxTicXicChart::removeTicDataByEventId(int eventId)
{
    bool removed = false;

    // 遍历所有TIC数据，找到匹配的事件ID
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end();)
    {
        TicChartData *ticData = it.value();
        if (ticData && ticData->getEventNum() == eventId)
        {
            QString paramPath = ticData->getParamPath();
            QString uniqueID = ticData->getUniqueID();

            // 🎯 修复：直接调用基类删除逻辑，避免递归调用
            LxChart::RemoveLxChartDataByUniqueID(uniqueID);

            // 发出TIC删除信号，外部需要联动删除MASS和XIC
            emit ticDataRemoved(eventId, paramPath);

            // 🎯 关键修复：最后从map中移除（确保在所有操作完成后）
            it = m_ticDataMap.erase(it);
            removed = true;

            qDebug() << "LxTicXicChart::removeTicDataByEventId: 移除TIC数据，事件ID:" << eventId << "，路径:" << paramPath;
        }
        else
        {
            ++it;
        }
    }

    // 🎯 新增：如果删除了TIC且没有剩余TIC，重置扫描模式限制
    if (removed && m_ticDataMap.isEmpty())
    {
        qDebug() << "LxTicXicChart::removeTicDataByEventId: 所有TIC已删除，重置扫描模式限制";
        resetScanModeRestriction();
    }

    return removed;
}

bool LxTicXicChart::removeXicDataByEventId(int eventId)
{
    qDebug() << "LxTicXicChart::removeXicDataByEventId: 开始清理事件ID:" << eventId << "的XIC数据";
    bool removed = false;

    // 收集要删除的XIC数据，避免在遍历时修改容器
    QVector<QString> keysToRemove;
    QVector<XicChartData *> dataToRemove;

    for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
    {
        XicChartData *xicData = it.value();
        if (xicData && xicData->getTic_event_id() == eventId)
        {
            keysToRemove.append(it.key());
            dataToRemove.append(xicData);
            qDebug() << "LxTicXicChart::removeXicDataByEventId: 找到要删除的XIC，UniqueID:" << xicData->getUniqueID();
        }
    }

    // 安全删除收集到的XIC数据
    for (int i = 0; i < keysToRemove.size(); ++i)
    {
        QString key = keysToRemove[i];
        XicChartData *xicData = dataToRemove[i];

        if (m_xicDataMap.contains(key) && m_xicDataMap[key] == xicData)
        {
            QString paramPath = xicData->getParamPath();
            QString uniqueID = xicData->getUniqueID();

            qDebug() << "LxTicXicChart::removeXicDataByEventId: 安全删除XIC数据，UniqueID:" << uniqueID;

            // 直接从基类删除，不调用RemoveLxChartDataByUniqueID避免递归
            LxChart::RemoveLxChartDataByUniqueID(uniqueID);

            // 从映射中移除
            m_xicDataMap.remove(key);
            removed = true;

            // 发出XIC删除信号，不影响TIC和MASS
            emit xicDataRemoved(eventId, paramPath);

            qDebug() << "LxTicXicChart::removeXicDataByEventId: 成功移除XIC数据，事件ID:" << eventId << "，路径:" << paramPath;
        }
        else
        {
            qDebug() << "LxTicXicChart::removeXicDataByEventId: 警告：XIC数据已经被删除，跳过";
        }
    }

    qDebug() << "LxTicXicChart::removeXicDataByEventId: 完成，删除了" << keysToRemove.size() << "个XIC数据";
    return removed;
}

void LxTicXicChart::clearAllTicXicData()
{
    // 清除基类数据
    ClearAllLxChartData();

    // 清除映射
    m_ticDataMap.clear();
    m_xicDataMap.clear();

    // 🎯 新增：重置扫描模式限制
    resetScanModeRestriction();

    qDebug() << "LxTicXicChart::clearAllTicXicData: 清除所有TIC/XIC数据";
}

QList<TicChartData *> LxTicXicChart::getTicDataList() const
{
    return m_ticDataMap.values();
}

QList<XicChartData *> LxTicXicChart::getXicDataList() const
{
    return m_xicDataMap.values();
}

TicChartData *LxTicXicChart::getTicDataByEventId(int eventId) const
{
    // 遍历所有TIC数据，找到匹配的事件ID（返回第一个匹配的）
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        TicChartData *ticData = it.value();
        if (ticData && ticData->getEventNum() == eventId)
        {
            return ticData;
        }
    }
    return nullptr;
}

XicChartData *LxTicXicChart::getXicDataByEventId(int eventId) const
{
    // 遍历所有XIC数据，找到匹配的事件ID（返回第一个匹配的）
    for (auto it = m_xicDataMap.begin(); it != m_xicDataMap.end(); ++it)
    {
        XicChartData *xicData = it.value();
        if (xicData && xicData->getTic_event_id() == eventId)
        {
            return xicData;
        }
    }
    return nullptr;
}

// 颜色同步相关方法已移除，每个曲线使用自己的随机颜色

bool LxTicXicChart::RemoveLxChartDataByUniqueID(QString UniqueID)
{
    qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 开始删除，UniqueID:" << UniqueID;
    qDebug() << "   当前m_ticDataMap大小:" << m_ticDataMap.size();

    // 🎯 调试：列出所有TIC数据
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        TicChartData *ticData = it.value();
        qDebug() << "   TIC数据 - 复合键:" << it.key()
                 << "，UniqueID:" << ticData->getUniqueID()
                 << "，文件路径:" << ticData->getParamPath()
                 << "，事件ID:" << ticData->getEventNum()
                 << "，对象地址:" << (void *)ticData;
    }

    // 首先检查是否是TIC数据
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        if (it.value()->getUniqueID() == UniqueID)
        {
            qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 在m_ticDataMap中找到TIC，执行子类删除逻辑";
            QString compositeKey = it.key();
            TicChartData *ticData = it.value();
            int eventId = ticData->getEventNum();
            QString paramPath = ticData->getParamPath();

            // 删除TIC时，删除所有关联的XIC数据（适配一对多关系）
            QVector<QString> xicKeysToRemove;
            for (auto xicIt = m_xicDataMap.begin(); xicIt != m_xicDataMap.end(); ++xicIt)
            {
                XicChartData *xicData = xicIt.value();
                if (xicData && xicData->getTic_event_id() == eventId && xicData->getParamPath() == paramPath)
                {
                    xicKeysToRemove.append(xicIt.key());
                }
            }

            // 移除所有关联的XIC数据
            for (const QString &xicKey : xicKeysToRemove)
            {
                XicChartData *xicData = m_xicDataMap[xicKey];
                qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 同时删除关联的XIC数据，UniqueID:" << xicData->getUniqueID();

                // 先删除XIC的LxChartLegend，避免在对象析构时删除
                if (xicData->legend)
                {
                    qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 删除XIC的LxChartLegend";
                    removeLegendWidget(xicData->getUniqueID());
                    xicData->legend = nullptr; // 设置为nullptr，避免重复删除
                }

                // 从图表中移除XIC的QLineSeries
                auto chartIt = m_chartDataVec.begin();
                while (chartIt != m_chartDataVec.end())
                {
                    if ((*chartIt)->getUniqueID() == xicData->getUniqueID())
                    {
                        QLineSeries *series = getSeries(xicData->getUniqueID());
                        if (series && m_chart)
                        {
                            m_chart->removeSeries(series);
                            delete series;
                        }
                        chartIt = m_chartDataVec.erase(chartIt);
                        break;
                    }
                    else
                    {
                        ++chartIt;
                    }
                }

                // 从映射中移除XIC对象引用（不删除对象，由TIC管理生命周期）
                m_xicDataMap.remove(xicKey);
                qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 移除XIC对象引用，UniqueID:" << xicData->getUniqueID() << "（对象生命周期由TIC管理）";
            }

            // 🎯 修复：先从TIC映射中移除（避免基类删除时影响map）
            qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 准备从m_ticDataMap移除TIC，复合键:" << compositeKey;
            qDebug() << "   移除前TIC数量:" << m_ticDataMap.size();

            // 🎯 调试：列出移除前的所有TIC
            for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
            {
                qDebug() << "   移除前TIC - 复合键:" << it.key() << "，UniqueID:" << it.value()->getUniqueID();
            }

            m_ticDataMap.remove(compositeKey);
            qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 从m_ticDataMap移除TIC，复合键:" << compositeKey;
            qDebug() << "   移除后剩余TIC数量:" << m_ticDataMap.size();

            // 🎯 调试：列出移除后的所有TIC
            for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
            {
                qDebug() << "   移除后TIC - 复合键:" << it.key() << "，UniqueID:" << it.value()->getUniqueID();
            }

            // 发出TIC删除信号，让外部删除MASS数据
            emit ticDataRemoved(eventId, paramPath);

            // 最后调用基类删除逻辑
            LxChart::RemoveLxChartDataByUniqueID(UniqueID);

            // 🎯 新增：检查是否还有剩余的TIC，如果没有则重置扫描模式限制
            if (m_ticDataMap.isEmpty())
            {
                qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 所有TIC已删除，重置扫描模式限制";
                resetScanModeRestriction();
            }

            qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 移除TIC数据及其所有关联XIC，复合键:" << compositeKey;
            return true;
        }
    }

    // 然后检查是否是XIC数据（现在XIC映射的键是UniqueID）
    if (m_xicDataMap.contains(UniqueID))
    {
        XicChartData *xicData = m_xicDataMap[UniqueID];
        int eventId = xicData->getTic_event_id();
        QString paramPath = xicData->getParamPath();

        // 直接处理XIC删除，不调用基类方法避免发射sg_removeData信号
        // 1. 先删除LxChartLegend，避免在对象析构时删除
        if (xicData->legend)
        {
            qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 删除XIC的LxChartLegend";
            removeLegendWidget(UniqueID);
            xicData->legend = nullptr; // 设置为nullptr，避免重复删除
        }

        // 2. 从图表数据数组中移除
        for (int i = 0; i < m_chartDataVec.size(); i++)
        {
            if (m_chartDataVec.at(i)->getUniqueID() == UniqueID)
            {
                // 找到对应的曲线并移除
                QString seriesName = m_chartDataVec[i]->getUniqueID();
                for (int j = 0; j < m_chart->series().size(); j++)
                {
                    const auto abstractSeries = m_chart->series().at(j);
                    if (abstractSeries->name() == seriesName)
                    {
                        m_chart->removeSeries(abstractSeries);
                        break;
                    }
                }

                // 清除该曲线的峰标记
                clearPeaksForChartData(m_chartDataVec[i]);

                // 🎯 如果是MRM数据，清除MRM标签
                if (m_chartDataVec[i]->isMrmData())
                {
                    qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 清除MRM标签";
                    clearMrmLabels();
                }

                // 从颜色映射表中移除
                if (m_colorMap.contains(UniqueID))
                {
                    freeColorStack.push(m_colorMap.value(UniqueID));
                    m_colorMap.remove(UniqueID);
                }

                // 从管理数组中移除
                m_chartDataVec.remove(i);
                break;
            }
        }

        // 2. 从XIC映射中移除
        m_xicDataMap.remove(UniqueID);

        // 3. 更新全局范围
        UpdateGlobalRange();

        // 发出XIC删除信号，不影响TIC和MASS
        emit xicDataRemoved(eventId, paramPath);

        qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 移除XIC数据，UniqueID:" << UniqueID;
        return true;
    }

    // 如果不是TIC/XIC数据，调用基类方法
    qDebug() << "LxTicXicChart::RemoveLxChartDataByUniqueID: 在m_ticDataMap中未找到TIC，调用基类方法";
    return LxChart::RemoveLxChartDataByUniqueID(UniqueID);
}

bool LxTicXicChart::eventFilter(QObject *obj, QEvent *event)
{
    // 处理双击事件
    if (obj == m_chartView->viewport() && event->type() == QEvent::MouseButtonDblClick)
    {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
        if (mouseEvent->button() == Qt::LeftButton)
        {
            QPointF clickPos = mouseEvent->pos();
            // qDebug() << "LxTicXicChart::eventFilter: 检测到左键双击，位置:" << clickPos;

            // 获取图表的绘图区域
            QRectF plotArea = m_chart->plotArea();
            // qDebug() << "LxTicXicChart::eventFilter: plotArea范围:" << plotArea;

            // 优先检查是否双击在坐标轴区域（重置功能）
            if (isInXAxisArea(clickPos))
            {
                // qDebug() << "LxTicXicChart::eventFilter: 双击X轴区域，重置X轴";
                resetXAxis();
                // 🎯 X轴重置后更新顶点标签位置
                updateVertexLabelsPosition();
                return true; // 阻止事件继续传播
            }

            if (isInYAxisArea(clickPos))
            {
                // qDebug() << "LxTicXicChart::eventFilter: 双击Y轴区域，重置Y轴";
                resetYAxis();
                // 🎯 Y轴重置后更新顶点标签位置
                updateVertexLabelsPosition();
                return true; // 阻止事件继续传播
            }

            // 🎯 无论双击在哪里，都尝试处理TIC双击（使用近点查找）
            // qDebug() << "LxTicXicChart::eventFilter: 处理TIC双击（无论位置）";
            handleTicDoubleClick(mouseEvent);
            return true; // 阻止事件继续传播
        }

        // 右键双击交给基类处理（其他功能）
        if (mouseEvent->button() == Qt::RightButton)
        {
            // qDebug() << "LxTicXicChart::eventFilter: 右键双击，交给基类处理";
            return LxChart::eventFilter(obj, event);
        }

        // 其他双击按钮不处理
        // qDebug() << "LxTicXicChart::eventFilter: 其他按钮双击，忽略";
        return true;
    }

    // 对于非双击事件，正常调用基类处理
    return LxChart::eventFilter(obj, event);
}

void LxTicXicChart::handleTicDoubleClick(QMouseEvent *mouseEvent)
{
    if (!mouseEvent)
    {
        qDebug() << "LxTicXicChart::handleTicDoubleClick: mouseEvent为空";
        return;
    }

    QPointF pos = mouseEvent->pos();

    // 🎯 无论双击在哪里，都使用近点查找算法找到最近的TIC
    TicChartData *ticData = getTicDataAtPosition(pos);

    // 🎯 如果没有找到TIC数据，尝试强制查找第一个可见的TIC
    if (!ticData)
    {
        // qDebug() << "LxTicXicChart::handleTicDoubleClick: 未找到最近TIC，尝试查找第一个可见TIC";
        for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
        {
            TicChartData *currentTicData = it.value();
            if (currentTicData && currentTicData->legend && currentTicData->legend->bool_curveVisible())
            {
                ticData = currentTicData;
                // qDebug() << "LxTicXicChart::handleTicDoubleClick: 使用第一个可见TIC，事件ID:" << ticData->getEventNum();
                break;
            }
        }
    }

    if (ticData)
    {
        // 检查TIC数据是否有效且可见
        if (ticData->legend && ticData->legend->bool_curveVisible())
        {
            // 将屏幕坐标转换为数据坐标
            if (!defaultSeries)
            {
                defaultSeries = getDefaultSeries();
            }

            if (defaultSeries)
            {
                QPointF dataPos = m_chart->mapToValue(pos, defaultSeries);
                // 调用专门的TIC双击处理方法，传递数据坐标
                showMassChartForTic(ticData, dataPos);
                // qDebug() << "LxTicXicChart::handleTicDoubleClick: 双击处理完成，事件ID:" << ticData->getEventNum() << "，坐标:" << dataPos;
            }
            else
            {
                qDebug() << "LxTicXicChart::handleTicDoubleClick: 无法获取默认曲线进行坐标转换";
            }
        }
        else
        {
            qDebug() << "LxTicXicChart::handleTicDoubleClick: TIC曲线不可见，忽略双击";
        }
    }
    else
    {
        qDebug() << "LxTicXicChart::handleTicDoubleClick: 没有找到任何可见的TIC曲线";
    }
}

TicChartData *LxTicXicChart::getTicDataAtPosition(const QPointF &pos)
{
    // 检查图表和视图是否有效
    if (!m_chart || !m_chartView)
    {
        qDebug() << "LxTicXicChart::getTicDataAtPosition: 图表或视图无效";
        return nullptr;
    }

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || m_chart->series().isEmpty())
    {
        qDebug() << "LxTicXicChart::getTicDataAtPosition: 图表无数据系列";
        return nullptr;
    }

    // 获取默认曲线用于坐标转换
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "LxTicXicChart::getTicDataAtPosition: 无法获取默认曲线";
            return nullptr;
        }
    }

    // 将屏幕坐标转换为数据坐标
    QPointF dataPos = m_chart->mapToValue(pos, defaultSeries);

    // 🎯 使用findIndex简化近点查找，遍历所有可见TIC找到最近的
    TicChartData *closestTicData = nullptr;
    double minDistance = std::numeric_limits<double>::max();

    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        TicChartData *ticData = it.value();
        if (!ticData || !ticData->legend || !ticData->legend->bool_curveVisible())
            continue;

        // 🎯 直接使用findIndex找到最近点，无需手动遍历
        QVector<double> xData = ticData->getDataX();
        if (xData.isEmpty())
            continue;

        int nearestIndex = findIndex(xData, dataPos.x(), false);
        if (nearestIndex >= 0 && nearestIndex < xData.size())
        {
            double xDistance = qAbs(xData[nearestIndex] - dataPos.x());

            // qDebug() << "LxTicXicChart::getTicDataAtPosition: TIC" << ticData->getEventNum()
            //          << "X轴距离:" << xDistance << "点击时间:" << dataPos.x();

            if (xDistance < minDistance)
            {
                minDistance = xDistance;
                closestTicData = ticData;
            }
        }
    }

    if (closestTicData)
    {
        // qDebug() << "LxTicXicChart::getTicDataAtPosition: 选择TIC" << closestTicData->getEventNum() << "最小X距离:" << minDistance;
    }
    else
    {
        qDebug() << "LxTicXicChart::getTicDataAtPosition: 未找到合适的TIC曲线";

        // 🎯 如果没有找到最近的TIC，尝试返回第一个可见的TIC
        for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
        {
            TicChartData *ticData = it.value();
            if (ticData && ticData->legend && ticData->legend->bool_curveVisible())
            {
                closestTicData = ticData;
                // qDebug() << "LxTicXicChart::getTicDataAtPosition: 使用第一个可见TIC作为备选，事件ID:" << ticData->getEventNum();
                break;
            }
        }
    }

    return closestTicData;
}

void LxTicXicChart::showMassChartForTic(TicChartData *ticData, const QPointF &dataPos)
{
    if (!ticData)
    {
        // qDebug() << "LxTicXicChart::showMassChartForTic: ticData为空";
        return;
    }

    // 设置MASS加载状态标志
    s_isLoadingMass = true;
    // qDebug() << "LxTicXicChart::showMassChartForTic: 开始加载MASS数据，设置加载标志";

    QVector<std::tuple<QString, QPointF, int>> vecPair;

    // 🎯 无论什么模式，都加载所有可见TIC的MASS数据
    // 检查当前是否为SIM或MRM模式（用于日志显示）
    bool isMultiMassMode = ((m_allowedScanMode == GlobalEnums::ScanMode::SIM || m_allowedScanMode == GlobalEnums::ScanMode::MRM) && m_scanModeSet);

    // 🎯 总是加载所有TIC的MASS，不区分模式
    if (true)
    {
        QString modeStr = isMultiMassMode ? ((m_allowedScanMode == GlobalEnums::ScanMode::SIM) ? "SIM" : "MRM") : "FullScan";
        qDebug() << "LxTicXicChart::showMassChartForTic: 检测到" << modeStr << "模式，将加载所有可见TIC的MASS数据";

        // 🎯 所有模式：遍历所有可见的TIC曲线，为每个TIC在相同时间点生成MASS数据
        for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
        {
            TicChartData *currentTicData = it.value();
            if (!currentTicData || (currentTicData->legend && !currentTicData->legend->bool_curveVisible()))
            {
                // qDebug() << "LxTicXicChart::showMassChartForTic:" << modeStr
                //          << "TIC不可见，跳过，事件ID:" << (currentTicData ? currentTicData->getEventNum() : -1);
                continue;
            }

            // 获取当前TIC的数据点
            QVector<QPointF> currentDataPoints = currentTicData->getData();
            if (currentDataPoints.isEmpty())
            {
                // qDebug() << "LxTicXicChart::showMassChartForTic:" << modeStr << "TIC数据为空，跳过，事件ID:" << currentTicData->getEventNum();
                continue;
            }

            QVector<double> currentXData;
            for (int j = 0; j < currentDataPoints.size(); j++)
            {
                currentXData.append(currentDataPoints.at(j).x());
            }

            // 使用被双击TIC的时间点，在当前TIC中找到对应的数据点
            int currentIndex = findIndex(currentXData, dataPos.x(), false);
            if (currentIndex >= 0)
            {
                // 如果是被双击的TIC，更新其lastXDataIndex
                if (currentTicData == ticData)
                {
                    currentTicData->setLastXDataIndex(currentIndex);
                    // qDebug() << "LxTicXicChart::showMassChartForTic: 更新被双击TIC的lastXDataIndex，事件ID:" << currentTicData->getEventNum() << "，索引:" <<
                    // currentIndex;
                }

                QPointF currentPoint = currentDataPoints[currentIndex];
                vecPair.push_back(std::make_tuple(currentTicData->getParamPath(), currentPoint, currentTicData->getEventNum()));
                // qDebug() << "LxTicXicChart::showMassChartForTic: 添加" << modeStr << "TIC MASS数据，事件ID:" << currentTicData->getEventNum() << "，时间点:"
                // << currentPoint.x();
            }
        }
    }

    if (vecPair.isEmpty())
    {
        qDebug() << "LxTicXicChart::showMassChartForTic: 没有找到任何有效的TIC数据点";
        return;
    }

    // qDebug() << "发送MASS请求，共" << vecPair.size() << "条TIC曲线";

    // 检查平均质谱状态，决定是否可以显示MASS数据
    switch (AvgMassManager::getAvgMassStatus())
    {
    case GlobalEnums::AvgMassStatus::Calculating:
        QMessageBox::warning(this, tr("警告"), tr("等待平均质谱计算完成后重试"));
        break;
    case GlobalEnums::AvgMassStatus::Ready:
        // 平均质谱已准备好，可以显示MASS数据（会自动减去平均质谱）
        emit sg_showMassChart(vecPair);
        break;
    case GlobalEnums::AvgMassStatus::Stop:
        // 没有平均质谱或计算停止，直接显示原始MASS数据
        emit sg_showMassChart(vecPair);
        break;
    }
}

// 旧的TIC可见性管理方法已移除，现在每个曲线有独立的LxChartLegend管理可见性

// 旧的XIC可见性管理方法已移除，现在每个曲线有独立的LxChartLegend管理可见性

bool LxTicXicChart::internalRemoveTicData(const QString &compositeKey)
{
    if (!m_ticDataMap.contains(compositeKey))
    {
        return false;
    }

    TicChartData *ticData = m_ticDataMap[compositeKey];

    // 从基类移除TIC数据（不调用重写的方法，避免递归）
    LxChart::RemoveLxChartDataByUniqueID(ticData->getUniqueID());
    m_ticDataMap.remove(compositeKey);

    qDebug() << "LxTicXicChart::internalRemoveTicData: 内部移除TIC数据，复合键:" << compositeKey;
    qDebug() << "   移除后剩余TIC数量:" << m_ticDataMap.size();

    // 🎯 调试：列出剩余的TIC
    if (!m_ticDataMap.isEmpty())
    {
        qDebug() << "   剩余的TIC复合键:";
        for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
        {
            qDebug() << "     " << it.key();
        }
    }

    // 🎯 新增：检查是否还有剩余的TIC，如果没有则重置扫描模式限制
    if (m_ticDataMap.isEmpty())
    {
        qDebug() << "LxTicXicChart::internalRemoveTicData: ✅ 所有TIC已删除，重置扫描模式限制";
        resetScanModeRestriction();
    }
    else
    {
        qDebug() << "LxTicXicChart::internalRemoveTicData: ⚠️ 还有" << m_ticDataMap.size() << "个TIC存在，不重置扫描模式";
    }
    return true;
}

bool LxTicXicChart::internalRemoveXicData(const QString &uniqueID)
{
    if (!m_xicDataMap.contains(uniqueID))
    {
        return false;
    }

    XicChartData *xicData = m_xicDataMap[uniqueID];

    // 从基类移除XIC数据（不调用重写的方法，避免递归）
    LxChart::RemoveLxChartDataByUniqueID(xicData->getUniqueID());
    m_xicDataMap.remove(uniqueID);

    // XIC对象的生命周期由TIC管理，LxTicXicChart只管理引用
    qDebug() << "LxTicXicChart::internalRemoveXicData: 移除XIC引用，UniqueID:" << xicData->getUniqueID() << "（对象生命周期由TIC管理）";

    qDebug() << "LxTicXicChart::internalRemoveXicData: 内部移除XIC数据，UniqueID:" << uniqueID;
    return true;
}

void LxTicXicChart::UpdateGlobalRange()
{
    // qDebug() << "LxTicXicChart::UpdateGlobalRange: 开始更新全局范围，考虑TIC/XIC可见性";

    // 初始化全局范围
    m_globalMinX = std::numeric_limits<qreal>::max();
    m_globalMaxX = std::numeric_limits<qreal>::lowest();
    m_globalMinY = std::numeric_limits<qreal>::max();
    m_globalMaxY = std::numeric_limits<qreal>::lowest();

    bool hasVisibleData = false;

    // 遍历所有数据，根据TIC/XIC的实际可见性计算范围
    for (int i = 0; i < m_chartDataVec.size(); i++)
    {
        const auto chartData = m_chartDataVec.at(i);
        bool isVisible = false;

        // 检查是否是TIC数据
        TicChartData *ticData = qobject_cast<TicChartData *>(chartData);
        if (ticData)
        {
            // TIC数据的可见性由legend的TIC按钮控制
            if (ticData->legend)
            {
                isVisible = ticData->legend->bool_curveVisible();
            }
        }
        else
        {
            // 检查是否是XIC数据
            XicChartData *xicData = qobject_cast<XicChartData *>(chartData);
            if (xicData)
            {
                // 检查XIC对应的QLineSeries的实际可见性
                QLineSeries *series = getSeries(xicData->getUniqueID());
                if (series)
                {
                    isVisible = series->isVisible();
                    qDebug() << "LxTicXicChart::UpdateGlobalRange: XIC数据可见性:" << isVisible << "，UniqueID:" << chartData->getUniqueID();
                }
                else
                {
                    isVisible = false;
                    qDebug() << "LxTicXicChart::UpdateGlobalRange: XIC数据找不到对应series，UniqueID:" << chartData->getUniqueID();
                }
            }
            else
            {
                // 其他类型的数据，使用默认的可见性判断
                if (chartData->legend)
                {
                    isVisible = chartData->legend->bool_curveVisible();
                }
                else
                {
                    isVisible = true; // 没有图例的数据默认可见
                }
            }
        }

        if (!isVisible)
        {
            qDebug() << "LxTicXicChart::UpdateGlobalRange: 跳过不可见的数据，UniqueID:" << chartData->getUniqueID();
            continue;
        }

        // 检查数据范围是否有效
        if (!chartData->hasValidRange())
        {
            qDebug() << "LxTicXicChart::UpdateGlobalRange: 跳过无效范围的数据，UniqueID:" << chartData->getUniqueID() << "，范围: X[" << chartData->getMinX()
                     << "," << chartData->getMaxX() << "]"
                     << "，Y[" << chartData->getMinY() << "," << chartData->getMaxY() << "]";
            continue;
        }

        // 更新全局范围
        m_globalMinX = qMin(m_globalMinX, chartData->getMinX());
        m_globalMaxX = qMax(m_globalMaxX, chartData->getMaxX());
        m_globalMinY = qMin(m_globalMinY, chartData->getMinY());
        m_globalMaxY = qMax(m_globalMaxY, chartData->getMaxY());
        hasVisibleData = true;

        // qDebug() << "LxTicXicChart::UpdateGlobalRange: 包含数据，UniqueID:" << chartData->getUniqueID()
        //          << "，类型:" << (ticData ? "TIC" : (qobject_cast<XicChartData *>(chartData) ? "XIC" : "其他"));
    }

    if (!hasVisibleData)
    {
        qDebug() << "LxTicXicChart::UpdateGlobalRange: 没有可见数据，使用默认范围";
        m_globalMinX = 0;
        m_globalMaxX = 100;
        m_globalMinY = 0;
        m_globalMaxY = 100;
    }

    // qDebug() << "LxTicXicChart::UpdateGlobalRange: 更新全局范围:";
    // qDebug() << "  X轴: " << m_globalMinX << " 到 " << m_globalMaxX;
    // qDebug() << "  Y轴: " << m_globalMinY << " 到 " << m_globalMaxY;

    // 调用基类的SetAxisScale来应用新的范围
    SetAxisScale();
}

void LxTicXicChart::AddLxChartData(LxChartData *chartData)
{
    if (!chartData)
    {
        qDebug() << "LxTicXicChart::AddLxChartData: chartData为空";
        return;
    }

    // 🎯 新增：在添加数据前检查并清理状态不一致的问题
    qDebug() << "LxTicXicChart::AddLxChartData: 开始状态检查";
    qDebug() << "   当前TIC数量:" << m_ticDataMap.size();
    qDebug() << "   扫描模式已设置:" << m_scanModeSet;

    // 检查是否有不可见的TIC数据残留
    int visibleTicCount = 0;
    QStringList invisibleKeys;
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        TicChartData *ticData = it.value();
        if (ticData)
        {
            // 🎯 修复：通过legend检查TIC可见性
            bool isVisible = false;
            if (ticData->legend)
            {
                isVisible = ticData->legend->bool_curveVisible();
            }

            if (isVisible)
            {
                visibleTicCount++;
            }
            else
            {
                invisibleKeys.append(it.key());
                qDebug() << "   发现不可见TIC数据:" << it.key();
            }
        }
    }

    qDebug() << "   可见TIC数量:" << visibleTicCount;
    qDebug() << "   不可见TIC数量:" << invisibleKeys.size();

    // 🎯 修复：只重置扫描模式，不删除隐藏的TIC数据
    // 隐藏的TIC数据仍然是有效的，只是暂时不可见，不应该被删除
    if (visibleTicCount == 0 && m_scanModeSet)
    {
        qDebug() << "   🔧 没有可见TIC，重置扫描模式限制（保留隐藏的TIC数据）";

        // 只重置扫描模式，不删除TIC数据
        resetScanModeRestriction();

        qDebug() << "   扫描模式已重置，TIC数量保持:" << m_ticDataMap.size();
    }

    GlobalEnums::TrackType dataType = chartData->getTrackType();

    // 检查轨迹类型，只允许TIC和XIC
    if (dataType != GlobalEnums::TrackType::TIC && dataType != GlobalEnums::TrackType::XIC)
    {
        qDebug() << "LxTicXicChart::AddLxChartData: 不支持的轨迹类型，只支持TIC和XIC";
        return;
    }

    // 🎯 新增：检查扫描模式兼容性（对TIC和XIC数据都检查）
    GlobalEnums::ScanMode scanMode = chartData->getScanMode();

    // 检查扫描模式是否兼容
    if (!isScanModeCompatible(scanMode))
    {
        QString dataTypeStr = (dataType == GlobalEnums::TrackType::TIC) ? "TIC" : "XIC";
        qDebug() << "LxTicXicChart::AddLxChartData: 扫描模式不兼容，拒绝添加" << dataTypeStr << "数据";
        showScanModeWarning(m_allowedScanMode, scanMode);
        return;
    }

    // 如果是第一个数据（TIC或XIC），设置允许的扫描模式
    if (!m_scanModeSet)
    {
        setAllowedScanMode(scanMode);
    }

    // 对于XIC和TIC数据，都调用基类方法，会创建图例和建立交互连接
    LxChart::AddLxChartData(chartData);

    qDebug() << "LxTicXicChart::AddLxChartData: 成功添加" << (dataType == GlobalEnums::TrackType::TIC ? "TIC" : "XIC")
             << "数据，UniqueID:" << chartData->getUniqueID();
}

void LxTicXicChart::addXicDataWithoutLegend(XicChartData *xicData)
{
    if (!xicData)
    {
        qDebug() << "LxTicXicChart::addXicDataWithoutLegend: xicData为空";
        return;
    }

    // 检查轨迹类型是否被允许
    if (!isTrackTypeAllowed(xicData->getTrackType()))
    {
        qDebug() << "轨迹类型不被当前图表允许！无法添加XIC";
        return;
    }

    // 移除默认图例（如果存在）
    if (!m_bool_isRemoveDefaultLegend)
    {
        if (defaultSeries)
        {
            m_chart->removeSeries(defaultSeries);
            defaultSeries = nullptr;
        }
        m_bool_isRemoveDefaultLegend = true;
    }

    // 为XIC设置随机颜色
    int eventId = xicData->getTic_event_id();
    QString paramPath = xicData->getParamPath();
    QString compositeKey = generateCompositeKey(paramPath, eventId);

    // 生成随机颜色
    QColor curveColor = generateRandomColor();

    // 设置XIC曲线颜色
    xicData->setLineColor(curveColor);
    xicData->setOriginalColor(curveColor);

    // 不创建图例，XIC使用TIC的图例管理
    xicData->legend = nullptr;

    // 添加到数据管理数组
    m_chartDataVec.append(xicData);

    // 使用ChartData中的QLineSeries指针
    QString seriesName = xicData->getUniqueID();
    QLineSeries *series = qobject_cast<QLineSeries *>(xicData->getSeries());

    // 如果ChartData中没有QLineSeries，检查图表中是否已存在
    if (!series)
    {
        // 先查找是否已存在同名曲线
        for (int i = 0; i < m_chart->series().size(); i++)
        {
            const auto existingSeries = m_chart->series().at(i);
            QLineSeries *lineSeries = qobject_cast<QLineSeries *>(existingSeries);
            if (lineSeries && lineSeries->name() == seriesName)
            {
                series = lineSeries;
                xicData->setSeries(series); // 将找到的系列设置到ChartData中
                qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 找到已存在的曲线系列:" << seriesName;
                break;
            }
        }
    }

    // 如果不存在，创建新的曲线系列
    if (!series)
    {
        try
        {
            // 使用ChartData的createSeries方法创建
            series = qobject_cast<QLineSeries *>(xicData->createSeries(this, seriesName));

            // 检查图表和坐标轴的有效性
            if (!m_chart)
            {
                qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 错误：m_chart为空";
                xicData->deleteSeries();
                return;
            }

            m_chart->addSeries(series);

            // 安全地设置坐标轴
            auto horizontalAxes = m_chart->axes(Qt::Horizontal);
            auto verticalAxes = m_chart->axes(Qt::Vertical);

            if (!horizontalAxes.isEmpty() && !verticalAxes.isEmpty())
            {
                series->attachAxis(horizontalAxes.first());
                series->attachAxis(verticalAxes.first());
            }
            else
            {
                qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 警告：坐标轴不可用";
            }

            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 创建新的曲线系列:" << seriesName;
        }
        catch (const std::exception &e)
        {
            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 创建曲线系列失败，异常:" << e.what();
            return;
        }
        catch (...)
        {
            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 创建曲线系列失败，未知异常";
            return;
        }
    }

    if (series)
    {
        qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 准备设置曲线数据，数据点数:" << xicData->getData().size();

        // 检查数据有效性
        if (xicData->getData().isEmpty())
        {
            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 警告：XIC数据为空";
        }

        try
        {
            // 优化：暂时禁用图表动画以提高性能
            bool animationEnabled = m_chart->animationOptions() != QChart::NoAnimation;
            if (animationEnabled)
            {
                m_chart->setAnimationOptions(QChart::NoAnimation);
            }

            // 设置曲线数据
            series->replace(xicData->getData());
            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 曲线数据设置成功";

            // 设置曲线样式
            QPen pen(curveColor);
            pen.setWidth(1);
            series->setPen(pen);
            xicData->setDefaultPen(pen);

            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: XIC曲线添加成功，UniqueID:" << xicData->getUniqueID();

            // 根据批量模式决定是否立即更新
            if (!m_batchAddMode)
            {
                // 非批量模式：异步更新全局范围和恢复设置
                QTimer::singleShot(0, this, [this, animationEnabled]()
                                   {
                    UpdateGlobalRange();

                    // 恢复动画设置
                    if (animationEnabled) {
                        m_chart->setAnimationOptions(QChart::SeriesAnimations);
                    }

                    // 强制刷新图表
                    m_chart->update();
                    m_chartView->viewport()->update(); });
            }
            else
            {
                // 批量模式：只恢复动画设置，跳过范围更新
                if (animationEnabled)
                {
                    QTimer::singleShot(0, this, [this, animationEnabled]()
                                       { m_chart->setAnimationOptions(QChart::SeriesAnimations); });
                }
                qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 批量模式，跳过立即更新图表范围";
            }

            // 临时禁用鼠标跟踪，防止在数据刚添加时出现问题
            if (m_chartView)
            {
                m_chartView->setMouseTracking(false);
                qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 临时禁用鼠标跟踪";

                // 100ms后重新启用鼠标跟踪（减少延迟）
                QTimer::singleShot(100, this, [this]()
                                   {
                    if (m_chartView) {
                        m_chartView->setMouseTracking(true);
                        qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 重新启用鼠标跟踪";
                    } });
            }
        }
        catch (const std::exception &e)
        {
            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 设置曲线数据失败，异常:" << e.what();
            return;
        }
        catch (...)
        {
            qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 设置曲线数据失败，未知异常";
            return;
        }
    }
    else
    {
        qDebug() << "LxTicXicChart::addXicDataWithoutLegend: 创建曲线系列失败";
    }
}

QString LxTicXicChart::generateCompositeKey(const QString &paramPath, int eventId) const
{
    return paramPath + "_" + QString::number(eventId);
}

void LxTicXicChart::beginBatchAdd()
{
    qDebug() << "LxTicXicChart::beginBatchAdd: 开始批量添加模式";
    m_batchAddMode = true;
    m_batchUpdateTimer->stop(); // 停止之前的定时器
}

void LxTicXicChart::endBatchAdd()
{
    qDebug() << "LxTicXicChart::endBatchAdd: 结束批量添加模式，启动延迟更新";
    if (m_batchAddMode)
    {
        // 启动延迟更新定时器
        m_batchUpdateTimer->start();
    }
}

QVector<std::tuple<QString, int, QVector<double>>> LxTicXicChart::getBgMassPointVec()
{
    qDebug() << "LxTicXicChart::getBgMassPointVec: 开始计算背景区域内的TIC数据点";

    if (!AvgMassManager::isRefExist)
    {
        AvgMassManager::isRefExist = true;
    }

    // 当调用getBgMassPointVec时，说明背景区域发生了变化
    // 需要清除现有的平均质谱数据并重新计算所有TIC
    qDebug() << "LxTicXicChart::getBgMassPointVec: 背景区域设置触发，这将是全量计算模式";
    qDebug() << "现有avgMassMap大小:" << AvgMassManager::avgMassMap.size();

    // 背景区域变化时，必须清除现有的平均质谱数据
    if (!AvgMassManager::avgMassMap.isEmpty())
    {
        qDebug() << "LxTicXicChart::getBgMassPointVec: 背景区域变化，清除现有平均质谱数据";
        AvgMassManager::clearAvgMassMap();
        AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
    }

    bgMassPointVec.clear();
    QVector<std::tuple<QString, int, QVector<double>>> vec;

    // 获取背景区域左右范围
    double left = m_backgroundAreaRange.first;
    double right = m_backgroundAreaRange.second;

    // 检查范围有效性
    if (left >= right)
    {
        qDebug() << "LxTicXicChart::getBgMassPointVec: 背景区域范围无效: [" << left << ", " << right << "]";
        return vec;
    }

    qDebug() << "LxTicXicChart::getBgMassPointVec: 背景区域范围: [" << left << ", " << right << "]";

    // 只遍历TIC数据，过滤掉XIC数据
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        TicChartData *ticData = it.value();
        if (!ticData)
        {
            continue;
        }

        // 检查TIC数据是否可见
        if (ticData->legend && !ticData->legend->bool_curveVisible())
        {
            qDebug() << "LxTicXicChart::getBgMassPointVec: TIC曲线不可见，跳过，事件ID:" << ticData->getEventNum();
            continue;
        }

        QVector<QPointF> dataPoints = ticData->getData();
        if (dataPoints.isEmpty())
        {
            qDebug() << "LxTicXicChart::getBgMassPointVec: TIC曲线数据为空，跳过，事件ID:" << ticData->getEventNum();
            continue;
        }

        QVector<double> bgPoints;

        // 遍历数据点，找到在背景区域范围内的点
        for (const QPointF &point : dataPoints)
        {
            if (point.x() >= left && point.x() <= right)
            {
                bgPoints.append(point.x());
            }
        }

        if (!bgPoints.isEmpty())
        {
            vec.append(std::make_tuple(ticData->getParamPath(), ticData->getEventNum(), bgPoints));
            qDebug() << "LxTicXicChart::getBgMassPointVec: 添加TIC数据，路径:" << ticData->getParamPath() << "，事件ID:" << ticData->getEventNum()
                     << "，背景点数:" << bgPoints.size();
        }
        else
        {
            qDebug() << "LxTicXicChart::getBgMassPointVec: TIC曲线在背景区域内无数据点，事件ID:" << ticData->getEventNum();
        }
    }

    // 将结果保存到成员变量
    bgMassPointVec = vec;

    if (!vec.isEmpty())
    {
        qDebug() << "LxTicXicChart::getBgMassPointVec: 发送sg_calAvgMass信号，共" << vec.size() << "条TIC曲线";
        emit sg_calAvgMass(vec);
    }
    else
    {
        qDebug() << "LxTicXicChart::getBgMassPointVec: 没有有效的背景区域数据点";
    }

    return vec;
}

void LxTicXicChart::checkAndCalculateAvgMassForNewTic(TicChartData *ticData, int retryCount)
{
    if (!ticData)
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: ticData为空";
        return;
    }

    // 限制重试次数，避免无限递归
    if (retryCount > 5)
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 重试次数超限(" << retryCount << ")，放弃计算平均质谱";
        qDebug() << "   - TIC事件ID:" << ticData->getEventNum();
        qDebug() << "   - 数据点数量:" << ticData->getData().size();
        return;
    }

    if (retryCount > 0)
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 第" << retryCount << "次重试检查TIC数据完整性";
    }

    // 检查是否已设置背景区域
    if (m_backgroundAreaRange.first >= m_backgroundAreaRange.second)
    {
        // qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 未设置有效的背景区域，跳过平均质谱计算";
        return;
    }

    // 检查当前平均质谱状态
    GlobalEnums::AvgMassStatus currentStatus = AvgMassManager::getAvgMassStatus();
    if (currentStatus == GlobalEnums::AvgMassStatus::Calculating)
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 平均质谱正在计算中，跳过新TIC的计算";
        return;
    }

    qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 检测到背景区域已设置，为新TIC计算平均质谱";
    qDebug() << "背景区域范围: [" << m_backgroundAreaRange.first << ", " << m_backgroundAreaRange.second << "]";
    qDebug() << "新TIC事件ID:" << ticData->getEventNum() << "，路径:" << ticData->getParamPath();

    // 检查TIC数据是否可见
    if (ticData->legend && !ticData->legend->bool_curveVisible())
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 新TIC曲线不可见，跳过平均质谱计算";
        return;
    }

    // 严格检查TIC数据是否完全加载
    QVector<QPointF> dataPoints = ticData->getData();
    if (dataPoints.isEmpty())
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 新TIC曲线数据为空，延迟计算平均质谱";

        // 延迟检查，给TIC数据加载更多时间
        QTimer::singleShot(100, this, [this, ticData, retryCount]()
                           {
            qDebug() << "LxTicXicChart: 延迟检查TIC数据完整性";
            checkAndCalculateAvgMassForNewTic(ticData, retryCount + 1); });
        return;
    }

    qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: TIC数据读取完成";
    qDebug() << "   - 数据点数量:" << dataPoints.size();

    // 获取TIC时间范围和背景区域范围
    double minX = ticData->getMinX();
    double maxX = ticData->getMaxX();
    double bgLeft = m_backgroundAreaRange.first;
    double bgRight = m_backgroundAreaRange.second;

    qDebug() << "   - TIC时间范围: [" << minX << ", " << maxX << "]";
    qDebug() << "   - 背景区域范围: [" << bgLeft << ", " << bgRight << "]";

    // 检查TIC是否与背景区域有重叠
    if (maxX < bgLeft || minX > bgRight)
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: TIC与背景区域无重叠，跳过平均质谱计算";
        return;
    }

    qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: TIC与背景区域有重叠，继续计算平均质谱";

    // 收集背景区域内的数据点
    QVector<double> bgPoints;
    double left = m_backgroundAreaRange.first;
    double right = m_backgroundAreaRange.second;

    for (const QPointF &point : dataPoints)
    {
        if (point.x() >= left && point.x() <= right)
        {
            bgPoints.append(point.x());
        }
    }

    if (bgPoints.isEmpty())
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 新TIC在背景区域内无数据点，跳过平均质谱计算";
        return;
    }

    qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 新TIC在背景区域内有" << bgPoints.size() << "个数据点";

    // 检查该TIC是否已经有平均质谱数据
    QString filePath = ticData->getParamPath();
    int eventId = ticData->getEventNum();

    bool hasExistingAvgMass = false;
    GlobalEnums::AvgMassStatus currentAvgStatus = AvgMassManager::getAvgMassStatus();

    qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 当前平均质谱状态:" << static_cast<int>(currentAvgStatus)
             << "(0=Calculating, 1=Ready, 2=Stop)";

    // 只有在Ready状态下才检查是否已存在平均质谱数据
    if (currentAvgStatus == GlobalEnums::AvgMassStatus::Ready)
    {
        if (AvgMassManager::containsAvgMass(filePath, eventId))
        {
            if (true) // 已经通过containsAvgMass检查过了
            {
                hasExistingAvgMass = true;
                qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: TIC已存在平均质谱数据，跳过计算";
                qDebug() << "文件:" << filePath << "，事件ID:" << eventId;
            }
        }
    }
    else if (currentAvgStatus == GlobalEnums::AvgMassStatus::Calculating)
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 平均质谱正在计算中，跳过新TIC计算";
        return;
    }
    else
    {
        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 平均质谱状态为Stop，可以计算新TIC";
    }

    if (!hasExistingAvgMass)
    {
        // 只为新TIC计算平均质谱
        QVector<std::tuple<QString, int, QVector<double>>> vec;
        vec.append(std::make_tuple(filePath, eventId, bgPoints));

        qDebug() << "LxTicXicChart::checkAndCalculateAvgMassForNewTic: 为新TIC发送平均质谱计算请求";
        qDebug() << "文件:" << filePath << "，事件ID:" << eventId << "，背景点数:" << bgPoints.size();

        // 发送信号计算新TIC的平均质谱
        emit sg_calAvgMass(vec);
    }
}

void LxTicXicChart::onBackgroundAreaChanged()
{
    qDebug() << "LxTicXicChart::onBackgroundAreaChanged: 背景区域发生变化，清除现有平均质谱数据";

    // 清除现有的平均质谱数据，因为背景区域变化了
    AvgMassManager::clearAvgMassMap();
    AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);

    // 检查是否有有效的背景区域
    if (m_backgroundAreaRange.first >= m_backgroundAreaRange.second)
    {
        qDebug() << "LxTicXicChart::onBackgroundAreaChanged: 背景区域无效，不进行平均质谱计算";
        return;
    }

    qDebug() << "LxTicXicChart::onBackgroundAreaChanged: 重新计算所有TIC的平均质谱";
    qDebug() << "新背景区域范围: [" << m_backgroundAreaRange.first << ", " << m_backgroundAreaRange.second << "]";

    // 重新计算所有TIC的平均质谱
    getBgMassPointVec();
}

// ==================== TIC/XIC积分功能实现 ====================

void LxTicXicChart::connectPeakFindSignals()
{
    // 连接PeakFind的寻峰完成信号
    connect(&PeakFind::getInstance(), &PeakFind::sg_searchPeaksSuccess, this, &LxTicXicChart::onTicXicPeakFindCompleted, Qt::QueuedConnection);
}

void LxTicXicChart::onTicXicPeakFindCompleted(const QString &windowId)
{
    if (windowId == this->getWindowId())
    {
        qDebug() << "LxTicXicChart::onTicXicPeakFindCompleted: TIC/XIC寻峰完成";

        // 检查每条曲线的峰数量
        int totalPeaks = 0;
        for (LxChartData *data : m_chartDataVec)
        {
            if (data)
            {
                int peakCount = data->peakVec.size();
                totalPeaks += peakCount;
                qDebug() << "曲线" << data->getUniqueID() << "找到" << peakCount << "个峰";

                // 输出前几个峰的信息
                for (int i = 0; i < qMin(3, peakCount); i++)
                {
                    const Peak &peak = data->peakVec[i];
                    qDebug() << QString("  峰%1: 位置(%2, %3) 高度=%4 FWHM=%5 SNR=%6")
                                    .arg(i + 1)
                                    .arg(peak.pTop.x(), 0, 'f', 2)
                                    .arg(peak.pTop.y(), 0, 'f', 2)
                                    .arg(peak.height, 0, 'f', 2)
                                    .arg(peak.fwhm, 0, 'f', 2)
                                    .arg(peak.snr, 0, 'f', 2);
                }
            }
        }

        qDebug() << "总共找到" << totalPeaks << "个峰";

        // 显示峰标签（但不显示峰点）
        showPeaks();

        // 发送积分完成信号
        emit ticXicIntegrationCompleted();

        qDebug() << "TIC/XIC积分计算完成，共处理" << m_chartDataVec.size() << "条曲线";
    }
}

// ==================== 自动积分功能实现 ====================

void LxTicXicChart::performAutoIntegrationForNewCurve(LxChartData *chartData)
{
    if (!chartData)
    {
        qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: chartData为空";
        return;
    }

    // qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 开始对新曲线进行自动积分，UniqueID:" << chartData->getUniqueID();

    // 检查曲线是否已经进行过寻峰
    if (chartData->getHasFindPeak())
    {
        qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 曲线已进行过寻峰，跳过，UniqueID:" << chartData->getUniqueID();
        return;
    }

    // 检查数据完整性
    QVector<double> dataX = chartData->getDataX();
    QVector<double> dataY = chartData->getDataY();

    // qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 数据检查";
    // qDebug() << "  X数据大小:" << dataX.size();
    // qDebug() << "  Y数据大小:" << dataY.size();

    if (dataY.size() < 10)
    {
        qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 数据点太少，跳过寻峰";
        return;
    }

    if (!dataY.isEmpty())
    {
        double minY = *std::min_element(dataY.begin(), dataY.end());
        double maxY = *std::max_element(dataY.begin(), dataY.end());
        // qDebug() << "  Y数据范围:" << minY << "到" << maxY;
        // qDebug() << "  前5个Y值:" << dataY.mid(0, qMin(5, dataY.size()));
    }

    // 直接对单个曲线进行寻峰，不修改m_chartDataVec
    // qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 调用PeakFind对单个曲线进行寻峰";

    // 在工作线程中执行寻峰
    TaskManager::instance()->run([=]()
                                 {
        // qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 工作线程开始处理单个曲线";

        // 获取校正后的数据用于寻峰和SNR计算
        std::vector<double> originalData = chartData->getDataY().toStdVector();
        std::vector<double> correctedData = cubicBaselineCorrection(movingAverage(originalData, 2));

        // qDebug() << "工作线程: 原始数据大小:" << originalData.size();
        // qDebug() << "工作线程: 校正数据大小:" << correctedData.size();

        // 使用默认参数进行寻峰（带X轴数据）
        std::vector<double> xDataStd = chartData->getDataX().toStdVector();
        qDebug() << "🚀 LxTicXicChart自动积分寻峰，数据点数:" << originalData.size() << ", X轴数据点数:" << xDataStd.size();
        chartData->peakVec = searchPeaksWithXData(originalData, xDataStd);

        // qDebug() << "工作线程: 寻峰完成，找到" << chartData->peakVec.size() << "个峰";

        // 设置峰的坐标点
        for (int i = 0; i < chartData->peakVec.size(); i++) {
            Peak &p = chartData->peakVec[i];
            p.pTop = QPointF(chartData->getDataX().at(p.top), chartData->getDataY().at(p.top));
            p.pStart = QPointF(chartData->getDataX().at(p.start), chartData->getDataY().at(p.start));
            p.pEnd = QPointF(chartData->getDataX().at(p.end), chartData->getDataY().at(p.end));
        }

        // 计算SNR
        if (!chartData->peakVec.empty()) {
            std::vector<double> snrList = CalcSNRofPeakList(correctedData, chartData->peakVec, 10);
            for (int i = 0; i < chartData->peakVec.size() && i < snrList.size(); i++) {
                chartData->peakVec[i].snr = snrList[i];
            }
        }

        // 标记曲线为已寻峰
        chartData->setHasFindPeak(true);

        // 在主线程中更新UI
        QMetaObject::invokeMethod(
            this,
            [=]() {
                // qDebug() << "主线程: 单个曲线寻峰完成，更新UI";

                // 显示峰标签
                showPeaks();

                // 发送积分完成信号
                emit ticXicIntegrationCompleted();

                // qDebug() << "主线程: 单个曲线UI更新完成";
            },
            Qt::QueuedConnection); });

    // qDebug() << "LxTicXicChart::performAutoIntegrationForNewCurve: 工作线程任务已提交";
}

/**
 * @brief 处理图例点击事件，实现只能有一个图例被选中
 * @param uniqueId 被点击的图例对应的曲线唯一ID
 */
void LxTicXicChart::handleLegendClicked(const QString &uniqueId)
{
    qDebug() << "LxTicXicChart::handleLegendClicked: 图例被点击，UniqueID:" << uniqueId;

    // 查找被点击的图例对应的曲线数据
    LxChartData *clickedChartData = nullptr;
    LxChartLegend *clickedLegend = nullptr;

    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->getUniqueID() == uniqueId)
        {
            clickedChartData = chartData;
            clickedLegend = chartData->legend;
            break;
        }
    }

    if (!clickedChartData || !clickedLegend)
    {
        qDebug() << "LxTicXicChart::handleLegendClicked: 未找到对应的曲线数据或图例";
        return;
    }

    // 如果点击的是当前已选中的图例，则取消选中
    if (m_currentSelectedLegend == clickedLegend)
    {
        // 取消选中
        m_currentSelectedLegend->setChecked(false);
        m_currentSelectedLegend = nullptr;

        // 🎯 恢复曲线颜色（支持MRM多系列）
        if (lastSelectedChartData == clickedChartData)
        {
            QColor originalColor = lastSelectedChartData->getOriginalColor();
            QPen defaultPen = lastSelectedChartData->getDefaultPen();
            defaultPen.setColor(originalColor);

            // 🎯 如果是MRM数据，恢复所有棒子系列的颜色
            if (lastSelectedChartData->isMrmData() && !lastSelectedChartData->getMrmBarSeries().isEmpty())
            {
                for (QAbstractSeries *barSeries : lastSelectedChartData->getMrmBarSeries())
                {
                    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries);
                    if (lineSeries)
                    {
                        lineSeries->setPen(defaultPen);
                        lineSeries->setColor(originalColor);
                    }
                }
                qDebug() << "LxTicXicChart::handleLegendClicked: 恢复MRM数据原始颜色:" << originalColor.name() << "，系列数量:" << lastSelectedChartData->getMrmBarSeries().size();
            }
            else
            {
                // 普通数据：恢复单个系列
                for (int i = 0; i < m_chart->series().size(); i++)
                {
                    QAbstractSeries *abstractSeries = m_chart->series().at(i);
                    if (abstractSeries->name() == lastSelectedChartData->getUniqueID())
                    {
                        QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                        if (lineSeries)
                        {
                            lineSeries->setPen(defaultPen);
                            lineSeries->setColor(originalColor);
                        }
                        break;
                    }
                }
            }
            lastSelectedChartData = nullptr;
        }

        qDebug() << "LxTicXicChart::handleLegendClicked: 取消选中图例，UniqueID:" << uniqueId;
        return;
    }

    // 取消之前选中的图例
    if (m_currentSelectedLegend)
    {
        m_currentSelectedLegend->setChecked(false);
    }

    // 设置新的选中图例
    m_currentSelectedLegend = clickedLegend;
    m_currentSelectedLegend->setChecked(true);

    // 选中对应的曲线（调用基类的setSelectColor方法）
    LxChart::setSelectColor(clickedChartData);

    qDebug() << "LxTicXicChart::handleLegendClicked: 选中图例，UniqueID:" << uniqueId;
}

/**
 * @brief 重写图例同步方法，实现图例和曲线的双向联动
 * @param chartData 曲线数据
 * @param isSelected 是否选中
 */
void LxTicXicChart::syncLegendSelection(LxChartData *chartData, bool isSelected)
{
    if (!chartData || !chartData->legend)
        return;

    if (isSelected)
    {
        // 选中操作：取消之前选中的图例，设置新的选中图例
        if (m_currentSelectedLegend && m_currentSelectedLegend != chartData->legend)
        {
            m_currentSelectedLegend->setChecked(false);
        }

        m_currentSelectedLegend = chartData->legend;
        m_currentSelectedLegend->setChecked(true);

        qDebug() << "LxTicXicChart::syncLegendSelection: 选中图例，UniqueID:" << chartData->getUniqueID();
    }
    else
    {
        // 取消选中操作：如果是当前选中的图例，则取消选中
        if (m_currentSelectedLegend == chartData->legend)
        {
            m_currentSelectedLegend->setChecked(false);
            m_currentSelectedLegend = nullptr;
            qDebug() << "LxTicXicChart::syncLegendSelection: 取消选中图例，UniqueID:" << chartData->getUniqueID();
        }
    }
}

/**
 * @brief 向前浏览（--lastXDataIndex）
 */
void LxTicXicChart::browsePrevious()
{
    // 检查是否正在加载MASS数据
    if (s_isLoadingMass)
    {
        qDebug() << "LxTicXicChart::browsePrevious: 正在加载MASS数据，忽略移动浏览请求";
        return;
    }

    // 检查是否有选中的TIC
    if (!m_currentSelectedLegend)
    {
        qDebug() << "LxTicXicChart::browsePrevious: 没有选中任何TIC，直接返回";
        return;
    }

    // 查找选中的TIC对应的TicChartData
    TicChartData *selectedTicData = nullptr;
    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->legend == m_currentSelectedLegend)
        {
            selectedTicData = qobject_cast<TicChartData *>(chartData);
            break;
        }
    }

    if (!selectedTicData)
    {
        qDebug() << "LxTicXicChart::browsePrevious: 未找到选中的TIC数据";
        return;
    }

    // 检查是否有有效的lastXDataIndex
    if (!selectedTicData->hasValidLastXDataIndex())
    {
        qDebug() << "LxTicXicChart::browsePrevious: 选中的TIC没有有效的lastXDataIndex，请先双击TIC加载MASS";
        return;
    }

    int currentIndex = selectedTicData->getLastXDataIndex();
    int newIndex = currentIndex - 1;

    // 检查是否越界
    QVector<QPointF> dataPoints = selectedTicData->getData();
    if (newIndex < 0)
    {
        qDebug() << "LxTicXicChart::browsePrevious: 索引越界，当前索引:" << currentIndex << "，无法向前浏览";
        return;
    }

    // 更新lastXDataIndex
    selectedTicData->setLastXDataIndex(newIndex);

    // 使用新索引加载MASS数据
    QPointF dataPos = dataPoints[newIndex];
    showMassChartForTic(selectedTicData, dataPos);

    // 🎯 更新时间标签显示当前加载的X轴时间
    emit currentTimeChanged(dataPos.x());
    qDebug() << "LxTicXicChart::browsePrevious: 发射时间更新信号" << dataPos.x();

    // qDebug() << "LxTicXicChart::browsePrevious: 向前浏览，事件ID:" << selectedTicData->getEventNum()
    //          << "，索引从" << currentIndex << "更新为" << newIndex << "，数据坐标:" << dataPos;
}

/**
 * @brief 向后浏览（++lastXDataIndex）
 */
void LxTicXicChart::browseNext()
{
    // 检查是否正在加载MASS数据
    if (s_isLoadingMass)
    {
        qDebug() << "LxTicXicChart::browseNext: 正在加载MASS数据，忽略移动浏览请求";
        return;
    }

    // 检查是否有选中的TIC
    if (!m_currentSelectedLegend)
    {
        qDebug() << "LxTicXicChart::browseNext: 没有选中任何TIC，直接返回";
        return;
    }

    // qDebug() << "LxTicXicChart::browseNext: 当前选中的图例UniqueID:" << m_currentSelectedLegend->getUniqueID();

    // 调试：显示所有TIC的lastXDataIndex状态
    // qDebug() << "LxTicXicChart::browseNext: === 所有TIC的lastXDataIndex状态 ===";
    // for (LxChartData *chartData : m_chartDataVec)
    // {
    //     if (chartData && chartData->getTrackType() == GlobalEnums::TrackType::TIC)
    //     {
    //         TicChartData *ticData = qobject_cast<TicChartData *>(chartData);
    //         if (ticData)
    //         {
    //             qDebug() << "TIC事件ID:" << ticData->getEventNum() << "，UniqueID:" << ticData->getUniqueID()
    //                      << "，lastXDataIndex:" << ticData->getLastXDataIndex() << "，是否选中:" << (ticData->legend == m_currentSelectedLegend);
    //         }
    //     }
    // }

    // 查找选中的TIC对应的TicChartData
    TicChartData *selectedTicData = nullptr;
    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->legend == m_currentSelectedLegend)
        {
            selectedTicData = qobject_cast<TicChartData *>(chartData);
            // qDebug() << "LxTicXicChart::browseNext: 找到选中的TIC，事件ID:" << selectedTicData->getEventNum();
            break;
        }
    }

    if (!selectedTicData)
    {
        qDebug() << "LxTicXicChart::browseNext: 未找到选中的TIC数据";
        return;
    }

    // 检查是否有有效的lastXDataIndex
    // qDebug() << "LxTicXicChart::browseNext: 检查lastXDataIndex，当前值:" << selectedTicData->getLastXDataIndex();
    if (!selectedTicData->hasValidLastXDataIndex())
    {
        qDebug() << "LxTicXicChart::browseNext: 选中的TIC没有有效的lastXDataIndex，请先双击TIC加载MASS";
        // qDebug() << "LxTicXicChart::browseNext: 事件ID:" << selectedTicData->getEventNum() << "，lastXDataIndex:" << selectedTicData->getLastXDataIndex();
        return;
    }

    int currentIndex = selectedTicData->getLastXDataIndex();
    int newIndex = currentIndex + 1;

    // 检查是否越界
    QVector<QPointF> dataPoints = selectedTicData->getData();
    if (newIndex >= dataPoints.size())
    {
        qDebug() << "LxTicXicChart::browseNext: 索引越界，当前索引:" << currentIndex << "，数据点总数:" << dataPoints.size() << "，无法向后浏览";
        return;
    }

    // 更新lastXDataIndex
    selectedTicData->setLastXDataIndex(newIndex);

    // 使用新索引加载MASS数据
    QPointF dataPos = dataPoints[newIndex];
    showMassChartForTic(selectedTicData, dataPos);

    // 🎯 更新时间标签显示当前加载的X轴时间
    emit currentTimeChanged(dataPos.x());
    qDebug() << "LxTicXicChart::browseNext: 发射时间更新信号" << dataPos.x();

    // qDebug() << "LxTicXicChart::browseNext: 向后浏览，事件ID:" << selectedTicData->getEventNum()
    //          << "，索引从" << currentIndex << "更新为" << newIndex << "，数据坐标:" << dataPos;
}

/**
 * @brief 清除MASS加载状态标志（供外部调用）
 */
void LxTicXicChart::clearMassLoadingFlag()
{
    s_isLoadingMass = false;
    // qDebug() << "LxTicXicChart::clearMassLoadingFlag: MASS加载标志已清除";
}

/**
 * @brief 检查扫描模式是否兼容
 * @param scanMode 要检查的扫描模式
 * @return true表示兼容，false表示不兼容
 */
bool LxTicXicChart::isScanModeCompatible(GlobalEnums::ScanMode scanMode) const
{
    // 🎯 调试：输出当前扫描模式状态
    QString scanModeStr;
    switch (scanMode)
    {
    case GlobalEnums::ScanMode::FullScan:
        scanModeStr = "FullScan";
        break;
    case GlobalEnums::ScanMode::SIM:
        scanModeStr = "SIM";
        break;
    case GlobalEnums::ScanMode::MRM:
        scanModeStr = "MRM";
        break;
    default:
        scanModeStr = "Unknown";
        break;
    }

    QString allowedModeStr;
    switch (m_allowedScanMode)
    {
    case GlobalEnums::ScanMode::FullScan:
        allowedModeStr = "FullScan";
        break;
    case GlobalEnums::ScanMode::SIM:
        allowedModeStr = "SIM";
        break;
    case GlobalEnums::ScanMode::MRM:
        allowedModeStr = "MRM";
        break;
    default:
        allowedModeStr = "Unknown";
        break;
    }

    qDebug() << "LxTicXicChart::isScanModeCompatible: 检查扫描模式兼容性";
    qDebug() << "   尝试添加的扫描模式:" << scanModeStr;
    qDebug() << "   当前允许的扫描模式:" << allowedModeStr;
    qDebug() << "   扫描模式是否已设置:" << m_scanModeSet;
    qDebug() << "   当前TIC数量:" << m_ticDataMap.size();

    // 🎯 修复：检查TIC数据的实际可见性状态
    int visibleTicCount = 0;
    for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
    {
        TicChartData *ticData = it.value();
        if (ticData)
        {
            // 通过legend检查TIC可见性
            bool isVisible = false;
            if (ticData->legend)
            {
                isVisible = ticData->legend->bool_curveVisible();
            }

            if (isVisible)
            {
                visibleTicCount++;
            }
        }
    }
    qDebug() << "   实际可见TIC数量:" << visibleTicCount;

    // 🎯 强化修复：如果没有可见的TIC但扫描模式仍然设置，强制重置
    if (visibleTicCount == 0 && m_scanModeSet)
    {
        qDebug() << "   ⚠️ 检测到异常状态：没有可见TIC但扫描模式仍然设置，强制重置";
        const_cast<LxTicXicChart *>(this)->resetScanModeRestriction();
        // 同时清理不可见的TIC数据
        QStringList keysToRemove;
        for (auto it = m_ticDataMap.begin(); it != m_ticDataMap.end(); ++it)
        {
            TicChartData *ticData = it.value();
            if (ticData)
            {
                // 通过legend检查TIC可见性
                bool isVisible = false;
                if (ticData->legend)
                {
                    isVisible = ticData->legend->bool_curveVisible();
                }

                if (!isVisible)
                {
                    qDebug() << "   清理不可见TIC数据:" << it.key();
                    keysToRemove.append(it.key());
                }
            }
        }
        // 🎯 修复：不能在const方法中修改map，移除const_cast的清理操作
        // 这里只记录需要清理的键，实际清理在非const方法中进行
        if (!keysToRemove.isEmpty())
        {
            qDebug() << "   发现" << keysToRemove.size() << "个不可见TIC需要清理，但在const方法中无法清理";
        }
    }

    // 原有的简单检查保留作为备用
    if (m_ticDataMap.isEmpty() && m_scanModeSet)
    {
        qDebug() << "   ⚠️ 检测到异常状态：TIC数量为0但扫描模式仍然设置，强制重置";
        const_cast<LxTicXicChart *>(this)->resetScanModeRestriction();
    }

    // 如果还没有设置扫描模式，任何模式都兼容（第一个TIC）
    if (!m_scanModeSet)
    {
        qDebug() << "   结果: 兼容（扫描模式未设置）";
        return true;
    }

    // 检查是否与当前允许的扫描模式一致
    bool compatible = (scanMode == m_allowedScanMode);
    qDebug() << "   结果:" << (compatible ? "兼容" : "不兼容");
    return compatible;
}

/**
 * @brief 设置允许的扫描模式（仅在第一个TIC时调用）
 * @param scanMode 扫描模式
 */
void LxTicXicChart::setAllowedScanMode(GlobalEnums::ScanMode scanMode)
{
    if (!m_scanModeSet)
    {
        m_allowedScanMode = scanMode;
        m_scanModeSet = true;

        QString scanModeStr;
        switch (scanMode)
        {
        case GlobalEnums::ScanMode::FullScan:
            scanModeStr = "FullScan";
            break;
        case GlobalEnums::ScanMode::SIM:
            scanModeStr = "SIM";
            break;
        case GlobalEnums::ScanMode::MRM:
            scanModeStr = "MRM";
            break;
        default:
            scanModeStr = "Unknown";
            break;
        }

        qDebug() << "LxTicXicChart::setAllowedScanMode: 设置图表允许的扫描模式为:" << scanModeStr;
    }
}

/**
 * @brief 显示扫描模式不兼容的警告对话框
 * @param currentMode 当前图表的扫描模式
 * @param newMode 尝试添加的扫描模式
 */
void LxTicXicChart::showScanModeWarning(GlobalEnums::ScanMode currentMode, GlobalEnums::ScanMode newMode) const
{
    QString currentModeStr, newModeStr;

    // 转换当前模式为字符串
    switch (currentMode)
    {
    case GlobalEnums::ScanMode::FullScan:
        currentModeStr = "全扫描(FullScan)";
        break;
    case GlobalEnums::ScanMode::SIM:
        currentModeStr = "选择离子监测(SIM)";
        break;
    case GlobalEnums::ScanMode::MRM:
        currentModeStr = "多反应监测(MRM)";
        break;
    default:
        currentModeStr = "未知模式";
        break;
    }

    // 转换新模式为字符串
    switch (newMode)
    {
    case GlobalEnums::ScanMode::FullScan:
        newModeStr = "全扫描(FullScan)";
        break;
    case GlobalEnums::ScanMode::SIM:
        newModeStr = "选择离子监测(SIM)";
        break;
    case GlobalEnums::ScanMode::MRM:
        newModeStr = "多反应监测(MRM)";
        break;
    default:
        newModeStr = "未知模式";
        break;
    }

    QString message = QString("扫描模式不兼容！\n\n"
                              "当前图表扫描模式：%1\n"
                              "尝试添加的扫描模式：%2\n\n"
                              "一个TIC/XIC图表只能显示同一种扫描模式的数据。\n"
                              "请使用新的图表窗口来显示不同扫描模式的数据。")
                          .arg(currentModeStr)
                          .arg(newModeStr);

    QMessageBox::warning(const_cast<LxTicXicChart *>(this), "扫描模式不兼容", message);
}

/**
 * @brief 重置扫描模式限制（清空图表时调用）
 */
void LxTicXicChart::resetScanModeRestriction()
{
    qDebug() << "LxTicXicChart::resetScanModeRestriction: 开始重置扫描模式限制";
    qDebug() << "   重置前: m_scanModeSet =" << m_scanModeSet << ", TIC数量 =" << m_ticDataMap.size();

    m_scanModeSet = false;
    m_allowedScanMode = GlobalEnums::ScanMode::FullScan; // 重置为默认值

    qDebug() << "   重置后: m_scanModeSet =" << m_scanModeSet;
    qDebug() << "LxTicXicChart::resetScanModeRestriction: ✅ 扫描模式限制已重置";
}
