#include "lxchart.h"
#include "ui_lxchart.h"
#include "masschartdata.h"
#include "Config/PeakFindingConfigs.h"
#include "FileData/avgmassmanager.h"
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QKeyEvent>
#include <QApplication>
#include <QDebug>
#include <QGridLayout>
#include <limits>
#include <QtCharts/QChartView>
#include <QtCharts/QValueAxis>
#include <QtCharts/QBarCategoryAxis>
#include <QtCharts/QCategoryAxis>
#include <QElapsedTimer>
#include <QTime>
#include <QInputDialog>
#include <QMetaObject>
#include <QFile>
#include <QTimer>
#include <QTextStream>
#include <QApplication>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include "xlsxdocument.h"
#include "xlsxworksheet.h"
#include <QStringList>
#include <QTimer>
#include <QMutex>
#include <QMutexLocker>
#include <QThread>
#include "lxchartdata.h"

// QT_CHARTS_USE_NAMESPACE

// 静态成员变量初始化
QList<QString> LxChart::s_splitWindowIds;
// 静态映射表，用于记录哪些曲线已经被拆分
QMap<QString, bool> LxChart::s_seriesSplitStatus;

LxChart::LxChart(GlobalEnums::TrackType type, QWidget *parent)
    : QWidget(parent), currentCurveColor(QColor(255, 0, 0)), selectCurveColor(QColor(255, 0, 0)), ui(new Ui::LxChart), m_chartView(nullptr),
      m_isSplitWindow(false), m_isProcessingYAxisChange(false), m_isSelectingRegion(false), m_startLine(nullptr), m_endLine(nullptr), m_regionRect(nullptr),
      m_isZooming(false), m_zoomRect(nullptr), m_isSettingBackgroundArea(false), m_bgStartLine(nullptr), m_bgEndLine(nullptr), m_bgRect(nullptr),
      m_isPercentMode(false), m_maxYValue(0.0), m_showCrosshair(false), m_crosshairHLine(nullptr), m_crosshairVLine(nullptr), m_coordinateText(nullptr),
      m_hideCursorWithCrosshair(true), m_interactionMode(GlobalEnums::InteractionMode::Mode_None), m_enablePointSelection(false), m_highlightPoint(nullptr),
      m_maxChartDataCount(-1), allowedTrackTypes({type}), m_isCreatingCustomRange(false), m_customRangeStartLine(nullptr), m_customRangeEndLine(nullptr),
      m_customRangeRect(nullptr)
{
    ui->setupUi(this);

    checkChartTypeForHideControls(type);

    // 加载LxChart专用样式
    loadLxChartStyles();

    // The ID of the window
    m_windowId = QUuid::createUuid().toString(QUuid::WithoutBraces);

    // 创建QChartView并添加到gridlayout_chartview
    m_chartView = new CustomChartView(this);
    m_chartView->setRenderHint(QPainter::Antialiasing, true);
    m_chartView->setRenderHint(QPainter::TextAntialiasing, true);
    m_chartView->setMouseTracking(true);

    // 初始化自定义区域的颜色列表
    m_customRangeColors.append(QColor(173, 216, 230)); // 浅蓝
    // m_customRangeColors.append(QColor(144, 238, 144)); // 浅绿
    // m_customRangeColors.append(QColor(255, 255, 153)); // 浅黄
    // m_customRangeColors.append(QColor(211, 211, 211)); // 浅灰
    ui->gridLayout_customChartView->addWidget(m_chartView);
    initChart();

    // 🎯 初始化自定义滑块
    initCustomSlider();

    // 手动连接移动浏览按钮的信号槽（确保连接正常）
    connect(ui->btn_lastExperiment, &QPushButton::clicked, this, &LxChart::on_btn_lastExperiment_clicked);
    connect(ui->btn_nextExperiment, &QPushButton::clicked, this, &LxChart::on_btn_nextExperiment_clicked);

    // 🎯 连接时间标签更新信号
    connect(this, &LxChart::currentTimeChanged, this, [this](double timeValue)
            {
        if (ui && ui->label_currentLoadTimeValue) {
            ui->label_currentLoadTimeValue->setText(QString::number(timeValue, 'f', 2));
            qDebug() << "LxChart: 时间标签更新为" << timeValue;
        } });

    // 不再手动连接信号和槽，避免重复连接
    // Qt的自动连接机制会通过on_objectName_signalName命名约定自动连接
}

LxChart::LxChart(const QList<GlobalEnums::TrackType> &allowedTypes, QWidget *parent)
    : QWidget(parent), currentCurveColor(QColor(255, 0, 0)), selectCurveColor(QColor(255, 0, 0)), ui(new Ui::LxChart), m_chartView(nullptr),
      m_isSplitWindow(false), m_isProcessingYAxisChange(false), m_isSelectingRegion(false), m_startLine(nullptr), m_endLine(nullptr), m_regionRect(nullptr),
      m_isZooming(false), m_zoomRect(nullptr), m_isSettingBackgroundArea(false), m_bgStartLine(nullptr), m_bgEndLine(nullptr), m_bgRect(nullptr),
      m_isPercentMode(false), m_maxYValue(0.0), m_showCrosshair(false), m_crosshairHLine(nullptr), m_crosshairVLine(nullptr), m_coordinateText(nullptr),
      m_hideCursorWithCrosshair(true), m_interactionMode(GlobalEnums::InteractionMode::Mode_None), m_enablePointSelection(false), m_highlightPoint(nullptr),
      m_maxChartDataCount(-1), allowedTrackTypes(allowedTypes), m_isCreatingCustomRange(false), m_customRangeStartLine(nullptr), m_customRangeEndLine(nullptr),
      m_customRangeRect(nullptr)
{
    ui->setupUi(this);

    // 加载LxChart专用样式
    loadLxChartStyles();

    // The ID of the window
    m_windowId = QString::number(reinterpret_cast<quintptr>(this));

    // 创建自定义图表视图
    m_chartView = new CustomChartView(this);
    m_chartView->setRenderHint(QPainter::Antialiasing);

    // 初始化自定义区域的颜色列表
    m_customRangeColors.append(QColor(173, 216, 230)); // 浅蓝
    m_customRangeColors.append(QColor(144, 238, 144)); // 浅绿
    m_customRangeColors.append(QColor(255, 255, 153)); // 浅黄
    m_customRangeColors.append(QColor(255, 182, 193)); // 浅粉
    m_customRangeColors.append(QColor(211, 211, 211)); // 浅灰
    ui->gridLayout_customChartView->addWidget(m_chartView);
    initChart();

    // 🎯 初始化自定义滑块
    initCustomSlider();

    // 手动连接移动浏览按钮的信号槽（确保连接正常）
    connect(ui->btn_lastExperiment, &QPushButton::clicked, this, &LxChart::on_btn_lastExperiment_clicked);
    connect(ui->btn_nextExperiment, &QPushButton::clicked, this, &LxChart::on_btn_nextExperiment_clicked);

    // 🎯 连接时间标签更新信号
    connect(this, &LxChart::currentTimeChanged, this, [this](double timeValue)
            {
        if (ui && ui->label_currentLoadTimeValue) {
            ui->label_currentLoadTimeValue->setText(QString::number(timeValue, 'f', 2));
            qDebug() << "LxChart: 时间标签更新为" << timeValue;
        } });

    // 不再手动连接信号和槽，避免重复连接
    // Qt的自动连接机制会通过on_objectName_signalName命名约定自动连接
}

LxChart::~LxChart()
{
    qDebug() << "LxChart析构开始, ID:" << m_windowId << ", 标题:" << windowTitle();

    // 清理图表资源
    clearRegionSelection();
    clearBackgroundAreaSelection();
    clearCrosshair();
    clearCustomRangeSelection(-1); // 清理所有自定义区域

    // 清理阈值线
    if (m_thresholdLine)
    {
        if (m_thresholdLine->scene())
        {
            m_chart->scene()->removeItem(m_thresholdLine);
        }
        delete m_thresholdLine;
        m_thresholdLine = nullptr;
    }

    // 如果是拆分窗口，从静态列表中移除ID，解除曲线拆分状态
    if (m_isSplitWindow)
    {
        // 获取窗口标题，用于标记曲线拆分状态
        QString seriesName = windowTitle();

        // 标记该曲线已不再拆分显示
        s_seriesSplitStatus[seriesName] = false;

        // 从拆分窗口列表中移除ID
        s_splitWindowIds.removeOne(m_windowId);

        qDebug() << "析构中移除拆分状态, 标题:" << seriesName << ", ID:" << m_windowId;
    }

    // 清除所有标签和标记点
    foreach (CustomLabelWidget *widget, vecLabelWidget)
    {
        if (widget->getMarker())
        {
            m_chart->scene()->removeItem(widget->getMarker());
            delete widget->getMarker();
        }
        delete widget;
    }
    vecLabelWidget.clear();

    delete ui;
    qDebug() << "LxChart析构完成, ID:" << m_windowId;
}

void LxChart::initChart()
{
    // 创建图表
    m_chart = new QChart();

    // 根据允许的轨迹类型设置图表标题
    if (allowedTrackTypes.size() == 1)
    {
        // 单一类型
        GlobalEnums::TrackType singleType = *allowedTrackTypes.begin();
        switch (singleType)
        {
        case GlobalEnums::TrackType::MS:
            m_chart->setTitle(tr("质谱图"));
            break;
        case GlobalEnums::TrackType::XIC:
            m_chart->setTitle(tr("离子流图"));
            break;
        case GlobalEnums::TrackType::TIC:
            m_chart->setTitle(tr("Tic图"));
            break;
        case GlobalEnums::TrackType::DAD:
            m_chart->setTitle(tr("DAD图"));
            break;
        case GlobalEnums::TrackType::XWC:
            m_chart->setTitle(tr("XWC图"));
            break;
        case GlobalEnums::TrackType::TWC:
            m_chart->setTitle(tr("TWC图"));
            break;
        default:
            m_chart->setTitle(tr("图表"));
            break;
        }
    }
    else if (allowedTrackTypes.contains(GlobalEnums::TrackType::TIC) && allowedTrackTypes.contains(GlobalEnums::TrackType::XIC))
    {
        // TIC/XIC组合类型
        m_chart->setTitle(tr("TIC/XIC 组合图"));
    }
    else
    {
        // 其他组合类型
        m_chart->setTitle(tr("组合图表"));
    }

    m_chart->legend()->hide();

    // 设置默认曲线名称
    m_defaultSeriesName = "";

    // 创建坐标轴
    m_chart->createDefaultAxes();

    // 设置图表到视图
    m_chartView->setChart(m_chart);

    // 启用十字准星显示
    m_showCrosshair = true;
    m_crosshairLength = 13;           // 默认13像素
    m_crosshairStyle = Qt::SolidLine; // 默认实线
    m_hideCursorWithCrosshair = true; // 默认隐藏鼠标
    initDefaultSeries();

    // 安装事件过滤器
    m_chartView->viewport()->installEventFilter(this);
    m_chartView->setMouseTracking(true);

    customLegendWidget = ui->customLegendWidget;
    customLegendWidget->setVisible(false);
    customLegendWidget->resize(customLegendWidget->width(), m_chartView->height() / 6);

    // connect(customLegendWidget, &CustomLegendWidget::sg_expand, [=](bool isExpand)
    //         {
    //     int height = isExpand ? customLegendWidget->height() * 2.5 : customLegendWidget->height() / 2.5;
    //     qDebug() << "height" << height;
    //     if (isExpand) {
    //         qDebug() << "放大";
    //     } else {
    //         qDebug() << "缩小";
    //     }
    //     customLegendWidget->raise();
    //     customLegendWidget->resize(customLegendWidget->width(), height); });

    ui->btn_setBackgroundArea->setEnabled(false);
}

// 获取默认数据曲线
QAbstractSeries *LxChart::getDefaultSeries()
{
    // 🎯 检查当前defaultSeries是否还有效
    if (defaultSeries)
    {
        // 检查是否还在图表中
        if (m_chart && m_chart->series().contains(defaultSeries))
        {
            return defaultSeries;
        }
        else
        {
            // 已失效，清空引用
            defaultSeries = nullptr;
            qDebug() << "LxChart::getDefaultSeries: 当前默认系列已失效，重新选择";
        }
    }

    // 🎯 检查基本条件
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        return nullptr;
    }

    // 🎯 安全地选择新的默认系列
    QList<QAbstractSeries *> allSeries = m_chart->series();
    if (!allSeries.isEmpty())
    {
        // 优先选择最后添加的系列
        defaultSeries = allSeries.last();
        qDebug() << "LxChart::getDefaultSeries: 选择新的默认系列:" << defaultSeries->name();
        return defaultSeries;
    }

    return nullptr;
}

// 计算所有曲线的数据范围
void LxChart::calculateDataRange()
{
    // 获取所有数据系列
    QList<QAbstractSeries *> seriesList = m_chart->series();

    if (seriesList.isEmpty())
    {
        return;
    }

    // 初始化最小最大值
    qreal minX = std::numeric_limits<qreal>::max();
    qreal maxX = std::numeric_limits<qreal>::lowest();
    qreal minY = std::numeric_limits<qreal>::max();
    qreal maxY = std::numeric_limits<qreal>::lowest();

    // 遍历所有系列，找出整体的最小最大值
    for (int i = 0; i < seriesList.size(); i++)
    {
        const auto abstractSeries = seriesList.at(i);
        QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
        QScatterSeries *scatterSeries = qobject_cast<QScatterSeries *>(abstractSeries);

        if (lineSeries)
        { // 如果是线系列
            for (int j = 0; j < lineSeries->count(); ++j)
            {
                QPointF point = lineSeries->at(j);
                minX = qMin(minX, point.x());
                maxX = qMax(maxX, point.x());
                minY = qMin(minY, point.y());
                maxY = qMax(maxY, point.y());
            }
        }
        else if (scatterSeries)
        { // 如果是散点系列
            for (int j = 0; j < scatterSeries->count(); ++j)
            {
                QPointF point = scatterSeries->at(j);
                minX = qMin(minX, point.x());
                maxX = qMax(maxX, point.x());
                minY = qMin(minY, point.y());
                maxY = qMax(maxY, point.y());
            }
        }
    }

    // 保存最大Y值和最小Y值用于百分比模式
    m_maxYValue = maxY;
    m_minYValue = minY;

    qDebug() << "数据范围计算: minX=" << minX << "maxX=" << maxX << "minY=" << minY << "maxY=" << maxY;

    // 增加边距比例，确保曲线完全显示
    qreal xMargin = (maxX - minX) * 0.1; // 增加到10%
    qreal yMargin = (maxY - minY) * 0.1; // 增加到10%

    // 设置坐标轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    // 转换为具体的轴类型
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);

    if (valueAxisX && valueAxisY)
    {
        valueAxisX->setRange(minX - xMargin, maxX + xMargin);

        valueAxisY->setRange(minY - yMargin, maxY + yMargin);
        valueAxisY->setLabelFormat("%g");
        valueAxisY->setTitleText("强度");

        qDebug() << "设置初始坐标轴范围:" << minX - xMargin << maxX + xMargin << minY - yMargin << maxY + yMargin;
    }
}

// 处理缩放操作
void LxChart::handleZoom(const QRectF &zoomRect)
{
    if (zoomRect.width() < 2 || zoomRect.height() < 2)
    {
        return;
    }

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "LxChart::handleZoom: 图表无数据系列，跳过缩放";
        return;
    }

    // 保存当前视图范围
    m_zoomStack.append(m_chart->plotArea());

    // 获取默认曲线
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "handleZoom失败：defaultSeries不存在";
            return;
        }
    }

    // 计算缩放区域
    QPointF topLeft = m_chart->mapToValue(zoomRect.topLeft(), defaultSeries);
    QPointF bottomRight = m_chart->mapToValue(zoomRect.bottomRight(), defaultSeries);

    // 设置坐标轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);

    // 转换为具体的轴类型
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);

    if (valueAxisX && valueAxisY)
    {
        // 设置X轴范围
        valueAxisX->setRange(topLeft.x(), bottomRight.x());

        // 普通模式直接设置范围
        valueAxisY->setRange(bottomRight.y(), topLeft.y());
        valueAxisY->setLabelFormat("%g");
        valueAxisY->setTitleText("强度");
        if (m_isPercentMode)
        {
            // 计算百分比模式所需的参数
            m_chartView->diff = m_globalMaxY - m_globalMinY;
            m_chartView->minY = m_globalMinY;
            m_chartView->m_bool_enableUpdate = true;
            m_chartView->viewport()->update();
            valueAxisY->setTitleText("相对强度%");
        }

        // 先保存当前坐标轴范围，以便后续更新区域
        qreal minX = valueAxisX->min();
        qreal maxX = valueAxisX->max();
        qreal minY = valueAxisY->min();
        qreal maxY = valueAxisY->max();

        // 如果存在普通区域选择，需要重新绘制以适应新的坐标轴范围
        if (m_regionRect && m_regionStartX != m_regionEndX)
        {
            qDebug() << "handleZoom中重绘普通区域：" << m_regionStartX << "到" << m_regionEndX;

            // 确保区域范围在当前坐标轴范围内
            qreal startX = qMax(m_regionStartX, minX);
            qreal endX = qMin(m_regionEndX, maxX);

            // 重新绘制普通区域选择
            handleRegionSelection(startX, endX);
        }

        // 如果存在背景区域，需要重新绘制背景区域以适应新的坐标轴范围
        if (m_backgroundAreaRange.first != m_backgroundAreaRange.second)
        {
            qDebug() << "handleZoom中重绘背景区域：" << m_backgroundAreaRange.first << "到" << m_backgroundAreaRange.second;

            // 确保背景区域范围在当前坐标轴范围内
            qreal startX = qMax(m_backgroundAreaRange.first, minX);
            qreal endX = qMin(m_backgroundAreaRange.second, maxX);

            // 直接更新背景区域的显示，保留原始范围
            if (startX < endX)
            { // 确保范围有效
                // 如果背景区域不存在，则创建它
                if (!m_bgRect)
                {
                    m_bgRect = new QGraphicsRectItem(m_chart);
                    m_bgRect->setPen(Qt::NoPen);
                    m_bgRect->setBrush(QBrush(QColor(255, 192, 203, 100))); // 粉红色
                    m_bgRect->setZValue(4);
                }

                if (!m_bgStartLine)
                {
                    m_bgStartLine = new QGraphicsLineItem(m_chart);
                    m_bgStartLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
                    m_bgStartLine->setZValue(5);
                }

                if (!m_bgEndLine)
                {
                    m_bgEndLine = new QGraphicsLineItem(m_chart);
                    m_bgEndLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
                    m_bgEndLine->setZValue(5);
                }

                updateTempBackgroundArea();
            }
        }

        // 更新自定义区域以适应新的坐标轴范围
        for (int i = 0; i < vecCustomRange.size(); i++)
        {
            GlobalDefine::CustomRange &customRange = vecCustomRange[i];

            // 确保自定义区域范围在当前坐标轴范围内
            qreal startX = qMax(customRange.range.first, minX);
            qreal endX = qMin(customRange.range.second, maxX);

            // 确保边界有效
            if (startX >= endX)
            {
                continue; // 跳过无效区域
            }

            // 如果边界线或矩形未创建，则创建它们
            if (!customRange.m_StartLine)
            {
                customRange.m_StartLine = new QGraphicsLineItem(m_chart);
                customRange.m_StartLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
                customRange.m_StartLine->setZValue(5);
            }

            if (!customRange.m_EndLine)
            {
                customRange.m_EndLine = new QGraphicsLineItem(m_chart);
                customRange.m_EndLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
                customRange.m_EndLine->setZValue(5);
            }

            if (!customRange.m_Rect)
            {
                customRange.m_Rect = new QGraphicsRectItem(m_chart);
                customRange.m_Rect->setPen(Qt::NoPen);
                customRange.m_Rect->setBrush(QBrush(customRange.color));
                customRange.m_Rect->setOpacity(0.3);
                customRange.m_Rect->setZValue(4);
            }

            // 更新左边界线
            QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
            QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
            customRange.m_StartLine->setLine(QLineF(startTop, startBottom));

            // 更新右边界线
            QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
            QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
            customRange.m_EndLine->setLine(QLineF(endTop, endBottom));

            // 更新区域矩形
            QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
            QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
            customRange.m_Rect->setRect(QRectF(topLeft, bottomRight).normalized());
        }
    }

    // 如果有高亮点，先移除当前的高亮点显示，然后使用原来的数据坐标重新绘制
    if (m_highlightPoint)
    {
        // 移除当前的高亮点显示
        m_chart->scene()->removeItem(m_highlightPoint);
        delete m_highlightPoint;
        m_highlightPoint = nullptr;

        // 使用保存的数据坐标重新绘制高亮点
        highlightPoint(m_currentHighlightPoint);
    }

    // 强制刷新图表
    m_chart->update();
    m_chartView->viewport()->update();

    // 更新自定义标签位置
    refreashLabelWidgetPos();

    // 在handleZoom函数末尾添加
    // 更新标记点的可见性与标签相反
    foreach (CustomLabelWidget *widget, vecLabelWidget)
    {
        if (widget->getMarker())
        {
            widget->getMarker()->setVisible(!widget->isVisible());
        }
    }

    // 更新峰标记点位置
    updatePeaksPos();

    // 🎯 缩放后更新自定义滑块位置，确保滑块始终对齐阈值线
    updateCustomSliderPosition();

    // 🎯 缩放后延迟更新顶点标签位置，确保坐标轴更新完成
    QTimer::singleShot(10, this, [this]()
                       {
        // 🎯 更新智能顶点标签位置
        updateVertexLabelsPosition(); });

    qDebug() << "LxChart::handleZoom: 缩放完成，滑块位置已更新";
}

void LxChart::resetZoom()
{
    // 清除缩放堆栈
    m_zoomStack.clear();

    // 重置图表
    m_chart->zoomReset();

    // 重新获取默认曲线
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
    }

    // 使用已保存的全局最值重新设置坐标轴范围
    SetAxisScale();

    // 如果存在背景区域，重新绘制
    if (m_backgroundAreaRange.first != m_backgroundAreaRange.second)
    {
        qreal startX = m_backgroundAreaRange.first;
        qreal endX = m_backgroundAreaRange.second;

        // 获取当前坐标轴范围
        QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
        QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
        qreal minX = valueAxisX->min();
        qreal maxX = valueAxisX->max();

        // 确保背景区域范围在当前坐标轴范围内
        startX = qMax(startX, minX);
        endX = qMin(endX, maxX);

        if (startX < endX)
        {
            // 如果背景区域不存在，则创建它
            if (!m_bgRect)
            {
                m_bgRect = new QGraphicsRectItem(m_chart);
                m_bgRect->setPen(Qt::NoPen);
                m_bgRect->setBrush(QBrush(QColor(255, 192, 203, 100))); // 粉红色
                m_bgRect->setZValue(4);
            }

            if (!m_bgStartLine)
            {
                m_bgStartLine = new QGraphicsLineItem(m_chart);
                m_bgStartLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
                m_bgStartLine->setZValue(5);
            }

            if (!m_bgEndLine)
            {
                m_bgEndLine = new QGraphicsLineItem(m_chart);
                m_bgEndLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
                m_bgEndLine->setZValue(5);
            }

            updateTempBackgroundArea();
        }
    }

    // 如果有高亮点，先移除当前的高亮点显示，然后使用原来的数据坐标重新绘制
    if (m_highlightPoint)
    {
        // 移除当前的高亮点显示
        m_chart->scene()->removeItem(m_highlightPoint);
        delete m_highlightPoint;
        m_highlightPoint = nullptr;

        // 使用保存的数据坐标重新绘制高亮点
        highlightPoint(m_currentHighlightPoint);
    }

    // 强制刷新图表
    m_chart->update();
    m_chartView->viewport()->update();

    // 更新自定义标签位置
    refreashLabelWidgetPos();

    // 在resetZoom函数末尾添加
    // 更新标记点的可见性与标签相反
    foreach (CustomLabelWidget *widget, vecLabelWidget)
    {
        if (widget->getMarker())
        {
            widget->getMarker()->setVisible(!widget->isVisible());
        }
    }

    // 在resetZoom函数末尾添加
    // 重新创建背景区域
    updateTempBackgroundArea();

    // 如果存在普通区域选择，重新绘制
    if (m_regionRect && m_regionStartX != m_regionEndX)
    {
        handleRegionSelection(m_regionStartX, m_regionEndX);
    }

    // 更新自定义区域
    updateCustomArea();

    // 更新峰标记点位置
    updatePeaksPos();

    // 🎯 重置缩放后更新自定义滑块位置，确保滑块始终对齐阈值线
    updateCustomSliderPosition();

    // 🎯 重置缩放后延迟更新智能顶点标签位置，确保坐标轴更新完成
    QTimer::singleShot(10, this, [this]()
                       { updateVertexLabelsPosition(); });

    qDebug() << "LxChart::resetZoom: 重置缩放完成，滑块位置已更新";
}

void LxChart::resetXAxis()
{
    if (m_chartDataVec.isEmpty())
    {
        qDebug() << "LxChart::resetXAxis: 图表数据为空，跳过X轴重置";
        return;
    }

    // 检查坐标轴是否存在
    if (m_chart->axes(Qt::Horizontal).isEmpty())
    {
        qDebug() << "LxChart::resetXAxis: X轴未初始化，跳过X轴重置";
        return;
    }

    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    if (!axisX)
    {
        qDebug() << "LxChart::resetXAxis: X轴指针为空，跳过X轴重置";
        return;
    }

    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    if (!valueAxisX)
    {
        qDebug() << "LxChart::resetXAxis: X轴类型转换失败，跳过X轴重置";
        return;
    }

    // 重置X轴到全局范围
    qDebug() << "LxChart::resetXAxis: 重置X轴范围到[" << m_globalMinX << "," << m_globalMaxX << "]";
    valueAxisX->setRange(m_globalMinX, m_globalMaxX);

    // 更新相关显示元素
    updateTempBackgroundArea();
    updateCustomArea();
    refreashLabelWidgetPos();
    updatePeaksPos();

    // 🎯 X轴重置后更新自定义滑块位置（虽然X轴变化不影响滑块，但为了保持一致性）
    updateCustomSliderPosition();

    // 🎯 X轴重置后更新顶点标签位置
    updateVertexLabelsPosition();

    // 强制刷新图表
    m_chart->update();
    m_chartView->viewport()->update();

    qDebug() << "LxChart::resetXAxis: X轴重置完成，滑块位置已更新";
}

void LxChart::resetYAxis()
{
    if (m_chartDataVec.isEmpty())
    {
        qDebug() << "LxChart::resetYAxis: 图表数据为空，跳过Y轴重置";
        return;
    }

    // 检查坐标轴是否存在
    if (m_chart->axes(Qt::Vertical).isEmpty())
    {
        qDebug() << "LxChart::resetYAxis: Y轴未初始化，跳过Y轴重置";
        return;
    }

    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    if (!axisY)
    {
        qDebug() << "LxChart::resetYAxis: Y轴指针为空，跳过Y轴重置";
        return;
    }

    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    if (!valueAxisY)
    {
        qDebug() << "LxChart::resetYAxis: Y轴类型转换失败，跳过Y轴重置";
        return;
    }

    // 重置Y轴到全局范围
    qDebug() << "LxChart::resetYAxis: 重置Y轴范围到[" << m_globalMinY << "," << m_globalMaxY << "]";
    valueAxisY->setRange(m_globalMinY, m_globalMaxY);

    // 设置Y轴格式和标题
    valueAxisY->setLabelFormat("%g");
    if (m_isPercentMode)
    {
        // 计算百分比模式所需的参数
        m_chartView->diff = m_globalMaxY - m_globalMinY;
        m_chartView->minY = m_globalMinY;
        m_chartView->m_bool_enableUpdate = true;
        m_chartView->viewport()->update();
        valueAxisY->setTitleText("相对强度%");
    }
    else
    {
        valueAxisY->setTitleText("强度");
    }

    // 更新相关显示元素
    updateTempBackgroundArea();
    updateCustomArea();
    refreashLabelWidgetPos();
    updatePeaksPos();

    // 🎯 Y轴重置后更新自定义滑块位置，确保滑块始终对齐阈值线
    updateDataRange(); // Y轴变化需要重新计算数据范围
    updateCustomSliderPosition();

    // 🎯 Y轴重置后更新顶点标签位置
    updateVertexLabelsPosition();

    // 强制刷新图表
    m_chart->update();
    m_chartView->viewport()->update();

    qDebug() << "LxChart::resetYAxis: Y轴重置完成，滑块位置已更新";
}

void LxChart::handleRegionSelection(qreal startX, qreal endX)
{
    // 确保起点在终点前
    if (startX > endX)
    {
        qSwap(startX, endX);
    }

    // 获取默认曲线
    if (!defaultSeries)
    {
        return;
    }
    // 记录区域范围
    m_regionStartX = startX;
    m_regionEndX = endX;

    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    qreal minY = 0;
    qreal maxY = 0;
    if (valueAxisY)
    {
        minY = valueAxisY->min();
        maxY = valueAxisY->max();
    }

    // 创建或更新区域标记线条
    if (!m_startLine)
    {
        m_startLine = new QGraphicsLineItem(m_chart);
        m_startLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
        m_startLine->setZValue(5);
    }

    if (!m_endLine)
    {
        m_endLine = new QGraphicsLineItem(m_chart);
        m_endLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
        m_endLine->setZValue(5);
    }

    QPointF startTop = m_chart->mapToPosition(QPointF(m_regionStartX, maxY), defaultSeries);
    QPointF startBottom = m_chart->mapToPosition(QPointF(m_regionStartX, minY), defaultSeries);
    m_startLine->setLine(QLineF(startTop, startBottom));

    qDebug() << "区域起点设置为:" << m_regionStartX;

    QPointF endTop = m_chart->mapToPosition(QPointF(m_regionEndX, maxY), defaultSeries);
    QPointF endBottom = m_chart->mapToPosition(QPointF(m_regionEndX, minY), defaultSeries);
    m_endLine->setLine(QLineF(endTop, endBottom));

    qDebug() << "区域终点设置为:" << m_regionEndX;

    // 创建或更新区域矩形
    if (!m_regionRect)
    {
        m_regionRect = new QGraphicsRectItem(m_chart);
        m_regionRect->setPen(Qt::NoPen);
        m_regionRect->setBrush(QBrush(QColor(173, 216, 230, 100))); // 浅蓝色填充，透明度约40%
        m_regionRect->setZValue(4);
    }

    QRectF rect(startTop, endBottom);
    m_regionRect->setRect(rect.normalized());

    qDebug() << "区域选择完成:" << m_regionStartX << "到" << m_regionEndX;
}

void LxChart::clearRegionSelection()
{
    if (m_startLine)
    {
        m_chart->scene()->removeItem(m_startLine);
        delete m_startLine;
        m_startLine = nullptr;
    }

    if (m_endLine)
    {
        m_chart->scene()->removeItem(m_endLine);
        delete m_endLine;
        m_endLine = nullptr;
    }

    if (m_regionRect)
    {
        m_chart->scene()->removeItem(m_regionRect);
        delete m_regionRect;
        m_regionRect = nullptr;
    }

    clearCustomRangeSelection(-1);
    // 注意：不清除背景区域！背景区域是用于平均质谱计算的持久性区域，
    // 与临时的区域选择是不同的功能，不应该在这里被清除
    // clearBackgroundAreaSelection();
}

bool LxChart::isInXAxisArea(const QPointF &pos)
{
    // 获取图表的绘图区域
    QRectF plotArea = m_chart->plotArea();

    // 获取整个图表视图的区域
    QRectF chartViewRect = m_chartView->viewport()->rect();

    // X轴区域定义为：X坐标在plotArea范围内，Y坐标在plotArea下方
    bool xInRange = (pos.x() >= plotArea.left() && pos.x() <= plotArea.right());
    bool yBelowPlotArea = (pos.y() > plotArea.bottom() && pos.y() <= chartViewRect.bottom());

    bool result = xInRange && yBelowPlotArea;

    // qDebug() << "LxChart::isInXAxisArea: pos=" << pos << ", plotArea=" << plotArea << ", chartViewRect=" << chartViewRect << ", xInRange=" << xInRange
    //          << ", yBelowPlotArea=" << yBelowPlotArea << ", result=" << result;

    return result;
}

bool LxChart::isInYAxisArea(const QPointF &pos)
{
    // 获取图表的绘图区域
    QRectF plotArea = m_chart->plotArea();

    // 获取整个图表视图的区域
    QRectF chartViewRect = m_chartView->viewport()->rect();

    // Y轴区域定义为：Y坐标在plotArea范围内，X坐标在plotArea左侧
    bool yInRange = (pos.y() >= plotArea.top() && pos.y() <= plotArea.bottom());
    bool xLeftOfPlotArea = (pos.x() >= chartViewRect.left() && pos.x() < plotArea.left());

    bool result = yInRange && xLeftOfPlotArea;

    // qDebug() << "LxChart::isInYAxisArea: pos=" << pos << ", plotArea=" << plotArea << ", chartViewRect=" << chartViewRect << ", yInRange=" << yInRange
    //          << ", xLeftOfPlotArea=" << xLeftOfPlotArea << ", result=" << result;

    return result;
}

bool LxChart::eventFilter(QObject *obj, QEvent *event)
{
    // 🎯 处理自定义滑块的鼠标事件
    if (obj == m_customSlider && m_customSlider)
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->button() == Qt::LeftButton)
            {
                m_isDraggingSlider = true;
                m_sliderDragStartPos = mouseEvent->globalPos();
                qDebug() << "LxChart::eventFilter: 开始拖拽自定义滑块";
                return true;
            }
        }
        else if (event->type() == QEvent::MouseMove && m_isDraggingSlider)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

            // 🎯 获取当前鼠标在滑块控件中的位置
            QPointF localPos = m_customSlider->mapFromGlobal(mouseEvent->globalPos());

            // 🎯 获取Y轴范围
            if (m_chart->axes(Qt::Vertical).isEmpty())
                return true;

            QValueAxis *yAxis = qobject_cast<QValueAxis *>(m_chart->axes(Qt::Vertical).first());
            if (!yAxis)
                return true;

            double yAxisMin = yAxis->min();
            double yAxisMax = yAxis->max();

            // 🎯 获取图表绘图区域
            QRectF plotArea = m_chart->plotArea();

            // 🎯 计算鼠标在chartView中的位置
            QPointF mousePos = m_chartView->mapFromGlobal(mouseEvent->globalPos());

            // 🎯 限制鼠标Y位置在绘图区域内
            // 让QLabel右侧中心点能够到达plotArea的top和bottom
            double constrainedY = qMax(plotArea.top(), qMin(plotArea.bottom(), mousePos.y()));

            // 🎯 计算滑块在Y轴范围内的百分比位置（0.0-1.0）
            // constrainedY是QLabel右侧中心点的目标Y坐标
            // QLabel右侧中心点的可移动范围：从 plotArea.top() 到 plotArea.bottom()
            double topLimit = plotArea.top();
            double bottomLimit = plotArea.bottom();
            double movableHeight = bottomLimit - topLimit;

            double yPercent = 0.0;
            if (movableHeight > 0)
            {
                yPercent = (bottomLimit - constrainedY) / movableHeight;
            }
            yPercent = qMax(0.0, qMin(1.0, yPercent)); // 确保在0-1范围内

            qDebug() << "拖拽调试: mousePos.y=" << mousePos.y() << "constrainedY=" << constrainedY << "topLimit=" << topLimit << "bottomLimit=" << bottomLimit
                     << "movableHeight=" << movableHeight << "yPercent=" << yPercent << "Y轴范围[" << yAxisMin << "," << yAxisMax << "]";

            // 🎯 根据百分比计算实际阈值
            double newThreshold = yAxisMin + yPercent * (yAxisMax - yAxisMin);

            // 🎯 标记阈值被用户手动设置
            m_thresholdManuallySet = true;

            // 🎯 更新阈值
            setThresholdValue(newThreshold);
            enableThreshold(newThreshold > yAxisMin);

            // 🎯 更新滑块位置
            updateCustomSliderPosition();

            qDebug() << "LxChart::滑块拖拽: Y百分比=" << yPercent << "阈值=" << newThreshold << "Y轴范围[" << yAxisMin << "," << yAxisMax << "]";

            return true;
        }
        else if (event->type() == QEvent::MouseButtonRelease)
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->button() == Qt::LeftButton && m_isDraggingSlider)
            {
                m_isDraggingSlider = false;
                qDebug() << "LxChart::eventFilter: 结束拖拽自定义滑块，最终阈值:" << m_thresholdValue;
                return true;
            }
        }
    }

    if (obj == m_chartView->viewport())
    {
        // qDebug() << "z" << m_chart->zValue();
        switch (event->type())
        {
        case QEvent::Resize:
        {
            customLegendWidget->resetExpandStatus();
            refreashLabelWidgetPos();
            // qDebug() << "Resize detected";
            m_bool_isResize = true;

            // 当窗口调整大小时，更新背景区域
            if (m_backgroundAreaRange.first != m_backgroundAreaRange.second && m_bgRect)
            {
                updateTempBackgroundArea();
            }

            // 当窗口调整大小时，更新普通区域选择
            if (m_regionRect && m_regionStartX != m_regionEndX)
            {
                handleRegionSelection(m_regionStartX, m_regionEndX);
            }

            // 当窗口调整大小时，更新自定义区域
            if (!vecCustomRange.isEmpty())
            {
                updateCustomArea();
            }

            // 当窗口调整大小时，更新峰标记点位置
            updatePeaksPos();

            // 🎯 当窗口调整大小时，更新自定义滑块的数据范围和位置
            updateDataRange();
            updateCustomSliderPosition();

            // 🎯 同时更新阈值线位置，确保与滑块同步
            updateThresholdLinePosition();

            // 🎯 延迟更新滑块位置，避免resize事件连续触发时的问题
            QTimer::singleShot(10, this, [this]()
                               {
                if (m_customSlider) {
                    updateCustomSliderPosition();
                    m_customSlider->update();
                    m_customSlider->repaint();
                    // qDebug() << "LxChart::resize延迟更新: 滑块位置已更新";
                }

                // 🎯 更新智能顶点标签位置
                updateVertexLabelsPosition(); });

            break;
        }
        case QEvent::MouseButtonPress:
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);

            // 获取默认曲线
            if (!defaultSeries)
            {
                break;
            }

            // 检查是否在自定义区域边缘
            if (mouseEvent->button() == Qt::LeftButton && !m_isZooming && !m_isSelectingRegion && !m_isSettingBackgroundArea)
            {
                QPointF clickPos = mouseEvent->pos();

                int rangeIndex = -1;
                bool isLeftEdge = false;
                if (isNearCustomRangeEdge(clickPos, rangeIndex, isLeftEdge))
                {
                    if (isLeftEdge)
                    {
                        m_isDraggingCustomRangeLeft = true;
                        m_isDraggingCustomRangeRight = false;
                    }
                    else
                    {
                        m_isDraggingCustomRangeLeft = false;
                        m_isDraggingCustomRangeRight = true;
                    }

                    m_draggingCustomRangeIndex = rangeIndex;
                    m_customRangeDragStartPoint = clickPos;

                    // 记录拖动开始时的X坐标值
                    if (m_isDraggingCustomRangeLeft)
                    {
                        m_customRangeDragStartX = vecCustomRange[rangeIndex].range.first;
                    }
                    else
                    {
                        m_customRangeDragStartX = vecCustomRange[rangeIndex].range.second;
                    }

                    // 设置鼠标形状为水平调整
                    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
                    qDebug() << "自定义区域" << rangeIndex + 1 << (isLeftEdge ? "左" : "右") << "侧边界拖动开始";
                    return true;
                }
            }

            // 新增: 检查是否在背景区域边缘
            if (mouseEvent->button() == Qt::LeftButton && !m_isZooming && !m_isSelectingRegion && !m_isSettingBackgroundArea)
            {
                QPointF clickPos = mouseEvent->pos();

                if (isNearBgLeftEdge(clickPos))
                {
                    m_isDraggingBgLeft = true;
                    m_bgDragStartPoint = clickPos;
                    m_bgDragStartX = m_backgroundAreaRange.first;
                    m_tempBackgroundAreaRange = m_backgroundAreaRange;
                    // 设置鼠标形状为水平调整
                    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
                    qDebug() << "左侧边界拖动开始";
                    return true;
                }
                else if (isNearBgRightEdge(clickPos))
                {
                    m_isDraggingBgRight = true;
                    m_bgDragStartPoint = clickPos;
                    m_bgDragStartX = m_backgroundAreaRange.second;
                    m_tempBackgroundAreaRange = m_backgroundAreaRange;
                    // 设置鼠标形状为水平调整
                    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
                    qDebug() << "右侧边界拖动开始";
                    return true;
                }
            }

            // 如果是简单的左键点击（没有缩放或区域选择），检查是否点击了数据点和显示当前选中曲线
            if (mouseEvent->button() == Qt::LeftButton && !(QApplication::keyboardModifiers() & Qt::AltModifier) && !m_isZooming && !m_isSelectingRegion &&
                !m_isSettingBackgroundArea)
            {
                QPointF clickPos = mouseEvent->pos();

                // 处理点击交互：包括TIC和XIC的点击变红
                if (lastHighlightedChartData)
                {
                    setSelectColor(lastHighlightedChartData);

                    // 如果点击的是XIC曲线，只进行变红处理，不进行其他操作
                    if (lastHighlightedChartData->getTrackType() == GlobalEnums::TrackType::XIC)
                    {
                        qDebug() << "XIC曲线点击变红，UniqueID:" << lastHighlightedChartData->getUniqueID();
                        return true; // 阻止后续处理
                    }
                }

                // 查找最近的数据点（主要用于TIC曲线）
                QPointF nearestPoint;
                qreal minDistance = std::numeric_limits<qreal>::max();

                // 遍历所有系列，寻找最近的点
                QList<QAbstractSeries *> seriesList = m_chart->series();
                for (int i = 0; i < seriesList.size(); i++)
                {
                    const auto abstractSeries = seriesList.at(i);
                    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                    if (lineSeries)
                    {
                        for (int j = 0; j < lineSeries->count(); ++j)
                        {
                            QPointF point = lineSeries->at(j);
                            QPointF screenPoint = m_chart->mapToPosition(point, lineSeries);
                            qreal distance = QLineF(clickPos, screenPoint).length();

                            if (distance < minDistance && distance < 20)
                            { // 20像素的阈值
                                minDistance = distance;
                                nearestPoint = point;
                            }
                        }
                    }
                }
            }

            // 如果正在设置背景区域模式
            if (m_isSettingBackgroundArea && mouseEvent->button() == Qt::LeftButton)
            {
                QPointF clickPos = mouseEvent->pos();
                QPointF chartPos = m_chart->mapToValue(clickPos, defaultSeries);

                // 创建第一条垂直线
                if (!m_bgStartLine)
                {
                    m_bgStartLine = new QGraphicsLineItem(m_chart);
                    m_bgStartLine->setPen(QPen(Qt::blue, 1, Qt::DashLine));
                    m_bgStartLine->setZValue(10);

                    // 获取Y轴范围
                    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
                    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
                    qreal minY = 0;
                    qreal maxY = 0;
                    if (valueAxisY)
                    {
                        minY = valueAxisY->min();
                        maxY = valueAxisY->max();
                    }

                    QPointF top = m_chart->mapToPosition(QPointF(chartPos.x(), maxY), defaultSeries);
                    QPointF bottom = m_chart->mapToPosition(QPointF(chartPos.x(), minY), defaultSeries);
                    m_bgStartLine->setLine(QLineF(QPointF(clickPos.x(), top.y()), QPointF(clickPos.x(), bottom.y())));

                    m_regionStartX = chartPos.x();

                    return true;
                }
            }

            // 左键按下，开始缩放
            if (mouseEvent->button() == Qt::LeftButton && !(QApplication::keyboardModifiers() & Qt::AltModifier))
            {
                m_isZooming = true;
                m_zoomStartPoint = mouseEvent->pos();

                // 创建缩放矩形
                if (m_zoomRect)
                {
                    m_chart->scene()->removeItem(m_zoomRect);
                    delete m_zoomRect;
                }

                m_zoomRect = new QGraphicsRectItem(m_chart);
                m_zoomRect->setPen(QPen(Qt::black, 1, Qt::DashLine));
                m_zoomRect->setBrush(QBrush(QColor(0, 0, 255, 30)));
                m_zoomRect->setRect(QRectF(m_zoomStartPoint, QSizeF(0, 0)));
                m_zoomRect->setZValue(11);

                // 在开始缩放时清除已存在的十字准星
                if (m_showCrosshair)
                {
                    clearCrosshair();
                    // 立即创建新的十字准星在起始位置（仅在有数据时）
                    if (!m_chartDataVec.isEmpty() && m_chart && !m_chart->series().isEmpty())
                    {
                        updateCrosshair(m_zoomStartPoint);
                    }
                }

                return true;
            }
            // 右键点击，检测是否点击了背景区域或区域选择
            else if (mouseEvent->button() == Qt::RightButton && m_chart->plotArea().contains(mouseEvent->pos()))
            {
                QMenu contextMenu;
                bool hasMenuItems = true;

                QPoint pos = mouseEvent->pos();

                // 具体目标值坐标 - 获取在图表坐标系中的位置
                const double destX = m_chart->mapToValue(m_chartView->mapToScene(pos)).x();
                const double destY = m_chart->mapToValue(m_chartView->mapToScene(pos)).y();

                // 使用图表的默认曲线进行坐标转换
                QPointF position = m_chart->mapToPosition(QPointF(destX, destY), defaultSeries);

                // 检查是否点击在标记点上
                QPointF clickPoint(destX, destY);
                bool clickedOnMarker = false;
                CustomLabelWidget *targetLabel = nullptr;

                foreach (CustomLabelWidget *widget, vecLabelWidget)
                {
                    if (widget->getMarker() && widget->getMarker()->isVisible())
                    {
                        QPointF markerPos = widget->getPoint();
                        // 检查是否点击在标记点附近（5个像素的范围内）
                        QPointF markerScreenPos = m_chart->mapToPosition(markerPos, defaultSeries);
                        QPointF clickScreenPos = m_chart->mapToPosition(clickPoint, defaultSeries);
                        QPointF distance = markerScreenPos - clickScreenPos;

                        if (qSqrt(distance.x() * distance.x() + distance.y() * distance.y()) <= 10)
                        {
                            clickedOnMarker = true;
                            targetLabel = widget;
                            break;
                        }
                    }
                }

                if (clickedOnMarker && targetLabel)
                {
                    // 点击在标记点上，显示"显示标注"菜单
                    QAction *showLabelAction = contextMenu.addAction("显示标注");
                    connect(showLabelAction, &QAction::triggered, [this, targetLabel]()
                            { targetLabel->showLabel(); });
                }
                else
                {
                    // 普通区域点击，显示"新增标注"菜单
                    QAction *addLabel = contextMenu.addAction("新增标注");

                    connect(addLabel, &QAction::triggered, [this, pos, destX, destY]()
                            {
                        // 解构 tuple
                        QString text;
                        QFont font;
                        QColor color;
                        std::tie(text, font, color) = CustomLabelInputDialog::getContent();
                        if (text.isEmpty()) {
                            qDebug() << "标注内容不能为空";
                            return;
                        }

                        if (vecLabelWidget.size() > GlobalDefine::CUSTOM_LABEL_SIZE) {
                            return;
                        }

                        // 创建标注
                        CustomLabelWidget *labelwidget = new CustomLabelWidget(text, font, color);

                        // 使用图表的默认曲线进行坐标转换
                        QPointF position = m_chart->mapToPosition(QPointF(destX, destY), defaultSeries);

                        qDebug() << "全局像素:" << mapToGlobal(position.toPoint());
                        labelwidget->setParent(m_chartView);
                        labelwidget->setPoint(QPointF(destX, destY));
                        labelwidget->move(position.x(), position.y());
                        labelwidget->show();
                        labelwidget->raise();

                        // 连接labelMoved信号，更新标签的数据坐标
                        connect(labelwidget, &CustomLabelWidget::labelMoved, this, &LxChart::updateLabelPosition);
                        // 移除标签信号
                        connect(labelwidget, &CustomLabelWidget::deleteLabel, [=]() {
                            foreach (CustomLabelWidget *widget, vecLabelWidget) {
                                if (widget == labelwidget) {
                                    qDebug() << "移除：" << widget->m_qstr_content;
                                    vecLabelWidget.removeOne(widget);
                                    delete widget;
                                    widget = nullptr;
                                }
                            }
                        });

                        // 创建对应的标记点
                        QGraphicsEllipseItem *marker = createMarkerForLabel(labelwidget);
                        labelwidget->setMarker(marker);

                        // 标注显示时标记点隐藏
                        marker->setVisible(false);

                        vecLabelWidget.append(labelwidget); });
                }

                // 检查是否同时点击了背景区域和普通区域选择
                bool inBgArea = m_bgRect && m_bgRect->contains(mouseEvent->pos());
                bool inRegionArea = m_regionRect && m_regionRect->contains(mouseEvent->pos());

                // 检查是否点击了自定义区域
                QVector<int> tempVec = findCustomRangeAtPosition(mouseEvent->pos());
                bool inCustomRange = !tempVec.isEmpty();
                // 如果点击了背景区域
                if (inBgArea)
                {
                    QAction *deleteBgAction = contextMenu.addAction("删除背景区域");
                    QAction *calBgAction = contextMenu.addAction("计算背景");
                    connect(deleteBgAction, &QAction::triggered, [this]()
                            { clearBackgroundAreaSelection(); });
                    connect(calBgAction, &QAction::triggered, [this]()
                            { getBgMassPointVec(); });
                    hasMenuItems = true;
                }

                // 如果点击了区域选择
                if (inRegionArea)
                {
                    QAction *deleteRegionAction = contextMenu.addAction("删除区域选择");
                    connect(deleteRegionAction, &QAction::triggered, [this]()
                            { clearRegionSelection(); });
                    hasMenuItems = true;
                }

                // 如果点击了自定义区域
                if (inCustomRange)
                {
                    for (int customRangeIndex : tempVec)
                    {
                        // 创建带颜色的删除菜单项
                        QString menuText = QString("删除区域%1").arg(vecCustomRange[customRangeIndex].index);
                        QAction *deleteCustomRangeAction = contextMenu.addAction(menuText);

                        // 设置菜单项字体颜色
                        QColor color = vecCustomRange[customRangeIndex].color;
                        QFont font = deleteCustomRangeAction->font();
                        font.setBold(true);

                        deleteCustomRangeAction->setFont(font);

                        // 使用样式表设置菜单项字体颜色
                        QString colorName = color.name();
                        // deleteCustomRangeAction->setStyleSheet(QString("color: %1").arg(colorName));

                        connect(deleteCustomRangeAction, &QAction::triggered, [this, customRangeIndex]()
                                { clearCustomRangeSelection(customRangeIndex); });

                        hasMenuItems = true;
                    }
                }

                // 添加重置坐标轴菜单项（总是显示）
                if (hasMenuItems)
                {
                    contextMenu.addSeparator(); // 添加分隔线
                }
                QAction *resetZoomAction = contextMenu.addAction("重置坐标轴");
                connect(resetZoomAction, &QAction::triggered, [this]()
                        { resetZoom(); });
                hasMenuItems = true;

                // 如果有菜单项，显示菜单
                if (hasMenuItems)
                {
                    contextMenu.exec(QCursor::pos());
                    return true;
                }

                return true;
            }
            // Alt+左键按下，开始区域选择
            else if (mouseEvent->button() == Qt::LeftButton && (QApplication::keyboardModifiers() & Qt::AltModifier))
            {
                // 直接返回，暂时移除自定义区域功能
                return false;
                // 当自定义区域数量小于4时才允许创建新区域
                if (vecCustomRange.size() < GlobalDefine::CUSTOM_RANGE_SIZE && !m_chartDataVec.isEmpty())
                {
                    m_isCreatingCustomRange = true;

                    // 获取当前图表的数据坐标范围
                    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
                    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);

                    // 获取鼠标位置对应的数据坐标
                    QPointF pos = m_chart->mapToValue(mouseEvent->pos(), defaultSeries);

                    // 将X坐标限制在图表范围内
                    if (valueAxisX)
                    {
                        pos.setX(qMax(valueAxisX->min(), qMin(pos.x(), valueAxisX->max())));
                    }

                    m_customRangeStartX = pos.x();

                    // 创建临时显示的自定义区域
                    updateCustomRangeTemp(m_customRangeStartX, m_customRangeStartX);

                    return true;
                }
                else
                {
                    if (vecCustomRange.size() >= GlobalDefine::CUSTOM_RANGE_SIZE)
                    {
                        qDebug() << "自定义区域数量已达上限(1个)，无法创建更多区域";
                    }
                    else if (m_chartDataVec.isEmpty())
                    {
                        qDebug() << "请先添加轨迹后再添加区域";
                    }
                    return false;
                }
            }
            break;
        }
        case QEvent::MouseMove:
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            // 获取默认曲线
            if (!defaultSeries)
            {
                break;
            }

            QPointF pos = mouseEvent->pos();
            // 处理自定义区域边缘拖动
            if (m_isDraggingCustomRangeLeft || m_isDraggingCustomRangeRight)
            {
                handleCustomRangeEdgeDragging(pos);
                // m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor)); // 确保拖动时维持双箭头光标
                return true;
            }

            // 处理背景区域边缘拖动
            if (m_isDraggingBgLeft || m_isDraggingBgRight)
            {
                handleBgEdgeDragging(pos);
                // m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor)); // 确保拖动时维持双箭头光标
                return true;
            }

            // 处理自定义区域创建过程
            if (m_isCreatingCustomRange)
            {
                // 获取当前图表的数据坐标范围
                QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
                QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
                QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
                QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);

                // 获取鼠标位置对应的数据坐标，并确保其在图表范围内
                QPointF chartPos = m_chart->mapToValue(pos, defaultSeries);

                // 将X坐标限制在图表范围内
                if (valueAxisX)
                {
                    chartPos.setX(qMax(valueAxisX->min(), qMin(chartPos.x(), valueAxisX->max())));
                }

                m_customRangeEndX = chartPos.x();

                // 更新临时自定义区域
                updateCustomRangeTemp(m_customRangeStartX, m_customRangeEndX);

                return true;
            }

            // 检查是否鼠标靠近任何自定义区域边界
            int rangeIndex = -1;
            bool isLeftEdge = false;
            if (isNearCustomRangeEdge(pos, rangeIndex, isLeftEdge))
            {
                // 在边缘检测时不显示十字准星
                if (m_showCrosshair)
                {
                    clearCrosshair();
                }
                m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor)); // 设置为双箭头光标
                return true;
            }
            else
            {
                m_chart->unsetCursor();
            }

            // 获取图表的坐标区域
            QRectF plotArea = m_chart->plotArea();

            // 当背景区域存在且可拖动时，检查鼠标位置
            if (m_bgRect && m_bgAreaDraggable && !m_isZooming && !m_isSelectingRegion && !m_isSettingBackgroundArea)
            {
                if (isNearBgLeftEdge(pos) || isNearBgRightEdge(pos))
                {
                    // 在边缘检测时不显示十字准星
                    if (m_showCrosshair)
                    {
                        clearCrosshair();
                    }
                    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor)); // 维持双箭头光标
                    // 此时光标变为双箭头
                    return true;
                }
                else
                {
                    // 恢复默认鼠标形状
                    m_chart->unsetCursor();
                }
            }

            // 如果正在设置背景区域模式且已经有了起始线
            if (m_isSettingBackgroundArea && m_bgStartLine && mouseEvent->buttons() & Qt::LeftButton)
            {
                QPointF clickPos = mouseEvent->pos();
                QPointF chartPos = m_chart->mapToValue(clickPos, defaultSeries);

                // 创建或更新第二条垂直线
                if (!m_bgEndLine)
                {
                    m_bgEndLine = new QGraphicsLineItem(m_chart);
                    m_bgEndLine->setPen(QPen(Qt::blue, 1, Qt::DashLine));
                    m_bgEndLine->setZValue(10);
                }

                // 获取Y轴范围
                QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
                QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
                qreal minY = 0;
                qreal maxY = 0;
                if (valueAxisY)
                {
                    minY = valueAxisY->min();
                    maxY = valueAxisY->max();
                }

                QPointF top = m_chart->mapToPosition(QPointF(chartPos.x(), maxY), defaultSeries);
                QPointF bottom = m_chart->mapToPosition(QPointF(chartPos.x(), minY), defaultSeries);
                m_bgEndLine->setLine(QLineF(QPointF(clickPos.x(), top.y()), QPointF(clickPos.x(), bottom.y())));

                // 创建或更新背景区域矩形
                if (!m_bgRect)
                {
                    m_bgRect = new QGraphicsRectItem(m_chart);
                    m_bgRect->setPen(Qt::NoPen);
                    m_bgRect->setBrush(QBrush(QColor(0, 0, 255, 50)));
                    m_bgRect->setZValue(5);
                }

                QPointF topLeft = m_chart->mapToPosition(QPointF(m_regionStartX, maxY), defaultSeries);
                QPointF bottomRight = m_chart->mapToPosition(QPointF(chartPos.x(), minY), defaultSeries);

                m_bgRect->setRect(QRectF(topLeft, bottomRight).normalized());

                return true;
            }

            // 缩放过程中
            if (m_isZooming && m_zoomRect)
            {
                QRectF rect(m_zoomStartPoint, mouseEvent->pos());
                m_zoomRect->setRect(rect.normalized());

                // 在缩放过程中更新十字准星位置，跟随鼠标移动（仅在有数据时）
                if (m_showCrosshair && !m_chartDataVec.isEmpty() && m_chart && !m_chart->series().isEmpty())
                {
                    updateCrosshair(mouseEvent->pos());
                }

                return true;
            }
            // 区域选择过程中
            else if (m_isSelectingRegion && m_startLine)
            {
                QPointF pos = m_chart->mapToValue(mouseEvent->pos(), defaultSeries);

                // 清除旧的临时区域
                if (m_regionRect)
                {
                    m_chart->scene()->removeItem(m_regionRect);
                    delete m_regionRect;
                    m_regionRect = nullptr;
                }

                if (m_endLine)
                {
                    m_chart->scene()->removeItem(m_endLine);
                    delete m_endLine;
                    m_endLine = nullptr;
                }

                // 获取坐标轴范围
                QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
                QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
                qreal minY = 0;
                qreal maxY = 0;
                if (valueAxisY)
                {
                    minY = valueAxisY->min();
                    maxY = valueAxisY->max();
                }

                // 创建临时结束线
                m_endLine = new QGraphicsLineItem(m_chart);
                m_endLine->setPen(QPen(Qt::black, 1, Qt::DashLine));
                QPointF endTop = m_chart->mapToPosition(QPointF(pos.x(), maxY), defaultSeries);
                QPointF endBottom = m_chart->mapToPosition(QPointF(pos.x(), minY), defaultSeries);
                m_endLine->setLine(QLineF(endTop, endBottom));
                m_endLine->setZValue(5);

                // 创建临时区域矩形
                QPointF startPos = m_chart->mapToValue(m_startLine->line().p1(), defaultSeries);
                m_regionRect = new QGraphicsRectItem(m_chart);
                m_regionRect->setPen(Qt::NoPen);
                m_regionRect->setBrush(QBrush(QColor(173, 216, 230, 100))); // 浅蓝色填充，透明度约40%

                QPointF startTop = m_chart->mapToPosition(QPointF(startPos.x(), maxY), defaultSeries);
                QPointF startBottom = m_chart->mapToPosition(QPointF(startPos.x(), minY), defaultSeries);

                QRectF rect;
                if (startBottom.x() < endBottom.x())
                {
                    rect = QRectF(startBottom.x(), startTop.y(), endBottom.x() - startBottom.x(), startBottom.y() - startTop.y());
                }
                else
                {
                    rect = QRectF(endBottom.x(), endTop.y(), startBottom.x() - endBottom.x(), endBottom.y() - endTop.y());
                }

                m_regionRect->setRect(rect);
                m_regionRect->setZValue(4);

                return true;
            }

            // 检查图表是否有数据系列，如果没有则跳过所有鼠标交互
            if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
            {
                // 图表无数据时，清除十字准星并跳过后续处理
                if (m_showCrosshair)
                {
                    clearCrosshair();
                }
                // 恢复高亮曲线的原始粗细
                if (lastHighlightedChartData)
                {
                    restoreOriginalWidth(lastHighlightedChartData);
                    lastHighlightedChartData = nullptr;
                    m_chart->update();
                }
                break; // 跳过后续的鼠标交互处理
            }

            // 如果鼠标在坐标区域内且不在背景区域边缘，显示最近曲线和十字准星
            if (plotArea.contains(pos))
            {
                // 显示最近曲线
                showNearestCurve(mouseEvent);

                // 只有当不在背景区域边缘拖动时，才显示十字准星
                if (m_showCrosshair && !isNearBgLeftEdge(pos) && !isNearBgRightEdge(pos))
                {
                    updateCrosshair(pos);
                }
            }
            else
            {
                // 鼠标不在坐标区域内，清除十字准星
                if (m_showCrosshair)
                {
                    clearCrosshair();
                }

                // 鼠标不在坐标区域内，恢复高亮曲线的原始粗细
                if (lastHighlightedChartData)
                {
                    restoreOriginalWidth(lastHighlightedChartData);
                    lastHighlightedChartData = nullptr;
                    m_chart->update();
                }
            }

            break;
        }
        case QEvent::MouseButtonRelease:
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            // 获取默认曲线
            if (!defaultSeries)
            {
                qDebug() << "找不到默认曲线";
                break;
            }
            if (m_bool_isResize)
            {
                qDebug() << "Resize 后鼠标释放";
                m_bool_isResize = false;

                refreashLabelWidgetPos();
            }
            // 处理自定义区域边缘拖动释放
            if ((m_isDraggingCustomRangeLeft || m_isDraggingCustomRangeRight) && mouseEvent->button() == Qt::LeftButton)
            {
                // 记录拖动的区域索引，用于重新计算平均质谱
                int draggedRangeIndex = m_draggingCustomRangeIndex;

                // 重置拖动状态
                m_isDraggingCustomRangeLeft = false;
                m_isDraggingCustomRangeRight = false;
                m_draggingCustomRangeIndex = -1;

                // 清除坐标提示文本
                if (m_coordinateText)
                {
                    m_chart->scene()->removeItem(m_coordinateText);
                    delete m_coordinateText;
                    m_coordinateText = nullptr;
                }

                // 🎯 自定义区域拖动完成后，重新计算平均质谱（类似背景区域修改）
                if (draggedRangeIndex >= 0 && draggedRangeIndex < vecCustomRange.size())
                {
                    GlobalDefine::CustomRange &customRange = vecCustomRange[draggedRangeIndex];
                    if (customRange.hasAvgMass && customRange.avgMassData)
                    {
                        qDebug() << "LxChart: 自定义区域拖动完成，更新平均质谱数据，区域索引:" << draggedRangeIndex;

                        // 🎯 直接更新现有平均质谱数据，不重新创建对象
                        updateAvgMassForCustomRange(draggedRangeIndex);
                    }
                    else if (!customRange.hasAvgMass)
                    {
                        // 如果还没有平均质谱，则创建新的
                        qDebug() << "LxChart: 自定义区域拖动完成，创建新的平均质谱，区域索引:" << draggedRangeIndex;
                        createAvgMassForCustomRange(draggedRangeIndex);
                    }
                }

                // 根据当前鼠标位置设置适当的光标
                QPointF pos = mouseEvent->pos();
                int index = -1;
                bool isLeft = false;
                if (isNearCustomRangeEdge(pos, index, isLeft))
                {
                    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
                }
                else
                {
                    m_chart->unsetCursor();

                    // 如果启用了十字准星且鼠标在图表区域内，更新十字准星
                    if (m_showCrosshair && m_chart->plotArea().contains(pos) && !m_chartDataVec.isEmpty() && m_chart && !m_chart->series().isEmpty())
                    {
                        updateCrosshair(pos);
                    }
                }

                return true;
            }

            // 新增: 处理背景区域边缘拖动释放
            if ((m_isDraggingBgLeft || m_isDraggingBgRight) && mouseEvent->button() == Qt::LeftButton)
            {
                // 完成拖动，确保背景区域范围已更新
                qDebug() << "背景区域完成拖动";
                getBgMassPointVec();
                // m_backgroundAreaRange已在拖动过程中更新，不需要额外调用handleBackgroundAreaSelection

                // 重置拖动状态
                m_isDraggingBgLeft = false;
                m_isDraggingBgRight = false;

                // 根据当前鼠标位置设置适当的光标
                QPointF pos = mouseEvent->pos();
                if (isNearBgLeftEdge(pos) || isNearBgRightEdge(pos))
                {
                    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
                }
                else
                {
                    m_chart->unsetCursor();

                    // 如果启用了十字准星且鼠标在图表区域内，更新十字准星
                    if (m_showCrosshair && m_chart->plotArea().contains(pos) && !m_chartDataVec.isEmpty() && m_chart && !m_chart->series().isEmpty())
                    {
                        updateCrosshair(pos);
                    }
                }

                return true;
            }

            // 处理自定义区域创建完成
            if (m_isCreatingCustomRange && mouseEvent->button() == Qt::LeftButton)
            {
                m_isCreatingCustomRange = false;

                // 确保起始点和结束点在图表范围内
                QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
                QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);

                if (valueAxisX)
                {
                    m_customRangeStartX = qMax(valueAxisX->min(), qMin(m_customRangeStartX, valueAxisX->max()));
                    m_customRangeEndX = qMax(valueAxisX->min(), qMin(m_customRangeEndX, valueAxisX->max()));
                }

                // 清除临时自定义区域显示
                if (m_customRangeStartLine)
                {
                    m_chart->scene()->removeItem(m_customRangeStartLine);
                    delete m_customRangeStartLine;
                    m_customRangeStartLine = nullptr;
                }

                if (m_customRangeEndLine)
                {
                    m_chart->scene()->removeItem(m_customRangeEndLine);
                    delete m_customRangeEndLine;
                    m_customRangeEndLine = nullptr;
                }

                if (m_customRangeRect)
                {
                    m_chart->scene()->removeItem(m_customRangeRect);
                    delete m_customRangeRect;
                    m_customRangeRect = nullptr;
                }

                // 创建最终的自定义区域
                handleCustomRangeSelection(m_customRangeStartX, m_customRangeEndX);

                return true;
            }

            // 如果正在设置背景区域模式且有起始线和结束线
            if (m_isSettingBackgroundArea && m_bgStartLine && m_bgEndLine && mouseEvent->button() == Qt::LeftButton)
            {
                QPointF clickPos = mouseEvent->pos();
                QPointF chartPos = m_chart->mapToValue(clickPos, defaultSeries);

                m_regionEndX = chartPos.x();

                // 处理背景区域选择
                handleBackgroundAreaSelection(m_regionStartX, m_regionEndX);

                // 结束背景区域设置模式
                setBackgroundAreaMode(false);

                return true;
            }

            // 左键释放，完成缩放
            if (mouseEvent->button() == Qt::LeftButton && m_isZooming && m_zoomRect)
            {
                m_isZooming = false;
                QRectF rect = m_zoomRect->rect();

                // 清除缩放矩形
                m_chart->scene()->removeItem(m_zoomRect);
                delete m_zoomRect;
                m_zoomRect = nullptr;

                // 执行缩放
                handleZoom(rect);

                // 缩放完成后，在鼠标当前位置更新十字准星（仅在有数据时）
                if (m_showCrosshair)
                {
                    // 确保清除旧的十字准星
                    clearCrosshair();
                    // 创建新的十字准星在当前鼠标位置（仅在有数据时）
                    if (!m_chartDataVec.isEmpty() && m_chart && !m_chart->series().isEmpty())
                    {
                        updateCrosshair(mouseEvent->pos());
                    }
                }

                return true;
            }
            // 左键释放，完成区域选择
            else if (mouseEvent->button() == Qt::LeftButton && m_isSelectingRegion)
            {
                m_isSelectingRegion = false;

                if (m_startLine && m_endLine)
                {
                    QPointF startPos = m_chart->mapToValue(m_startLine->line().p1(), defaultSeries);
                    QPointF endPos = m_chart->mapToValue(m_endLine->line().p1(), defaultSeries);

                    // 完成区域选择
                    handleRegionSelection(startPos.x(), endPos.x());
                }

                return true;
            }
            break;
        }
        case QEvent::MouseButtonDblClick:
        {
            QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
            if (mouseEvent->button() == Qt::LeftButton)
            {
                QPointF clickPos = mouseEvent->pos();
                qDebug() << "LxChart::eventFilter: 检测到左键双击，位置:" << clickPos;

                // 获取图表的绘图区域
                QRectF plotArea = m_chart->plotArea();
                qDebug() << "LxChart::eventFilter: plotArea范围:" << plotArea;

                // 检查是否双击在X轴区域（plotArea下方）
                if (isInXAxisArea(clickPos))
                {
                    qDebug() << "LxChart::eventFilter: 双击X轴区域，重置X轴";
                    resetXAxis();
                    return true; // 阻止事件继续传播
                }

                // 检查是否双击在Y轴区域（plotArea左侧）
                if (isInYAxisArea(clickPos))
                {
                    qDebug() << "LxChart::eventFilter: 双击Y轴区域，重置Y轴";
                    resetYAxis();
                    return true; // 阻止事件继续传播
                }

                // 只有在plotArea内部才处理原有的双击逻辑
                if (!plotArea.contains(clickPos))
                {
                    qDebug() << "LxChart::eventFilter: 双击位置不在plotArea内部，忽略";
                    return true; // 阻止事件继续传播
                }

                qDebug() << "LxChart::eventFilter: 双击plotArea内部，处理原有双击逻辑";

                // 检查当前高亮的曲线类型，XIC曲线不响应双击
                if (lastHighlightedChartData && lastHighlightedChartData->getTrackType() == GlobalEnums::TrackType::XIC)
                {
                    qDebug() << "LxChart::eventFilter: XIC曲线不响应双击事件";
                    return true; // 阻止双击处理
                }

                if (allowedTrackTypes.contains(GlobalEnums::TrackType::TIC))
                {
                    showMassChart();
                    // switch (DataReader::getAvgMassStatus()) {
                    // case GlobalDefine::AvgMassStatus::Calculating:
                    //     QMessageBox::warning(this, tr("警告"), tr("等待平均质谱计算完成后重试"));
                    //     break;
                    // case GlobalDefine::AvgMassStatus::Ready:;
                    //     showMassChart();
                    //     break;
                    // case GlobalDefine::AvgMassStatus::Stop:;
                    //     QMessageBox::warning(this, tr("警告"), tr("重新设置背景区域或重新计算背景后重试"));
                    //     break;
                    // }
                    return true;
                }
                else if (allowedTrackTypes.contains(GlobalEnums::TrackType::MS))
                {
                    showMassHightPoint();
                    return true;
                }
            }

            // 右键双击功能已移除，改为使用右键菜单中的"重置坐标轴"选项
            // if (mouseEvent->button() == Qt::RightButton)
            // {
            //     // qDebug() << "Double right click detected, resetting zoom";
            //     resetZoom();
            //     return true;
            // }
            break;
        }
        case QEvent::KeyRelease:
        {
            QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);

            // 获取默认曲线
            if (!defaultSeries)
            {
                break;
            }
            // Alt键释放，取消区域选择
            if (keyEvent->key() == Qt::Key_Alt && m_isSelectingRegion)
            {
                m_isSelectingRegion = false;

                if (m_startLine && m_endLine)
                {
                    QPointF startPos = m_chart->mapToValue(m_startLine->line().p1(), defaultSeries);
                    QPointF endPos = m_chart->mapToValue(m_endLine->line().p1(), defaultSeries);

                    // 完成区域选择
                    handleRegionSelection(startPos.x(), endPos.x());
                }

                return true;
            }
            break;
        }
        case QEvent::Leave:
        {
            // 当鼠标离开图表区域时，隐藏十字准星
            if (m_showCrosshair)
            {
                clearCrosshair();
            }

            // 当鼠标离开整个QChartView时，恢复高亮曲线的原始粗细
            if (lastHighlightedChartData)
            {
                restoreOriginalWidth(lastHighlightedChartData);
                lastHighlightedChartData = nullptr;
                m_chart->update();
            }
            break;
        }
        default:
            break;
        }
    }

    return QWidget::eventFilter(obj, event);
}

// 切换Y轴显示模式（百分比/数值）
void LxChart::toggleYAxisMode()
{
    // 设置坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    // 转换为具体的轴类型
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    if (!isInitAxisLabelDefaultColor)
    {
        isInitAxisLabelDefaultColor = true;

        AxisLabelDefaultColor = valueAxisY->labelsColor();

        m_chartView->defaultColor = AxisLabelDefaultColor;
    }

    // 切换模式
    m_isPercentMode = !m_isPercentMode;
    qDebug() << "当前模式" << m_isPercentMode;

    if (m_isPercentMode)
    {
        // 计算百分比模式所需的参数
        m_chartView->diff = m_globalMaxY - m_globalMinY;
        m_chartView->minY = m_globalMinY;
        m_chartView->m_bool_enableUpdate = true;
        valueAxisY->setTitleText(tr("相对强度%"));
    }
    else
    {
        m_chartView->clearLables();
        valueAxisY->setTitleText(tr("强度"));
    }
    setOriginAxisModeStyle(m_isPercentMode);
    // 强制刷新图表
    m_chart->update();
    m_chartView->viewport()->update();
}

// 设置为拆分窗口
void LxChart::setSplitWindow(bool isSplit)
{
    m_isSplitWindow = isSplit;

    // 如果是拆分窗口，禁用拆分按钮
    if (m_isSplitWindow && ui->btn_splitCharts)
    {
        ui->btn_splitCharts->setEnabled(false);
        ui->btn_splitCharts->setToolTip("子窗口只有一条曲线，无法再拆分");
    }
}

// 拆分图表
// void LxChart::splitCharts()
// {
//     if (m_isSplitWindow) {
//         qDebug() << "This is already a split window, cannot split further";
//         return;
//     }

//     // 获取所有线系列，包括默认曲线
//     QList<QLineSeries *> lineSeries;

//     // 首先添加默认曲线
//     if (!defaultSeries) {
//         return;
//     }
//     lineSeries.append(defaultSeries);

//     // 然后添加其他曲线
//     for (int i = 0; i < m_chart->series().size(); i++) {
//         const auto series = m_chart->series().at(i);
//         QLineSeries *lineSeries_temp = qobject_cast<QLineSeries *>(series);
//         if (lineSeries_temp && series != defaultSeries) {
//             lineSeries.append(lineSeries_temp);
//         }
//     }

//     qDebug() << "找到" << lineSeries.size() << "条需要拆分的曲线，当前线宽:" << GlobalDefine::LINE_WIDTH;

//     // 遍历所有线系列，每个线系列创建一个窗口（如果还没有拆分）
//     for (QLineSeries *series : lineSeries) {
//         QString seriesName = series->name();

//         // 检查该系列是否已经被拆分，并且子窗口是否仍在显示
//         bool alreadySplit = s_seriesSplitStatus.value(seriesName, false);

//         qDebug() << "检查曲线" << seriesName << "的拆分状态:" << (alreadySplit ? "已拆分" : "未拆分");

//         if (alreadySplit) {
//             qDebug() << "曲线" << seriesName << "已经拆分，跳过";
//             continue;
//         }

//         // 创建新的LxChart窗口
//         LxChart *newLxChart = new LxChart(trackType);
//         newLxChart->setSplitWindow(true);
//         newLxChart->setWindowTitle(seriesName);
//         newLxChart->resize(this->size());

//         // 获取原曲线的颜色
//         QColor seriesColor = series->pen().color();

//         // 使用统一方法创建曲线，传递原曲线颜色
//         QLineSeries *newSeries = newLxChart->getSeries(seriesName, true, seriesColor);

//         if (newSeries == nullptr) {
//             return;
//         }

//         // 复制数据到新曲线
//         for (int i = 0; i < series->count(); ++i) {
//             newSeries->append(series->at(i));
//         }

//         // 设置图表标题
//         newLxChart->chart()->setTitle(seriesName);

//         qDebug() << "子窗口" << seriesName << "的曲线颜色设置为:" << seriesColor.name() << "，线宽:" << GlobalDefine::LINE_WIDTH;

//         // 复制主窗口的属性到新窗口
//         if (m_isPercentMode) {
//             // 如果主窗口是百分比模式，新窗口也设置为百分比模式
//             newLxChart->toggleYAxisMode();
//         }

//         // 设置与主窗口相同的鼠标隐藏设置
//         newLxChart->setHideCursorWithCrosshair(m_hideCursorWithCrosshair);

//         // 确认子窗口的曲线线宽设置
//         qDebug() << "子窗口" << seriesName << "的曲线线宽设置为:" << newSeries->pen().widthF();

//         // 强制刷新子图表并重新计算坐标轴范围
//         newLxChart->calculateDataRange();

//         // 显示新窗口之前，强制更新图表视图
//         newLxChart->chart()->update();
//         newLxChart->m_chartView->viewport()->update();

//         newLxChart->m_chartView->viewport()->update();
//         newLxChart->m_chartView->scene()->update();

//         // 标记该系列已经拆分
//         s_seriesSplitStatus[seriesName] = true;

//         // 记录子窗口ID
//         s_splitWindowIds.append(newLxChart->getWindowId());

//         qDebug() << "标记曲线" << seriesName << "为已拆分状态";
//         qDebug() << "子窗口ID:" << newLxChart->getWindowId() << "已添加到拆分窗口列表";

//         // 显示新窗口
//         newLxChart->show();

//         // 调整新窗口位置，避免完全重叠
//         newLxChart->move(this->pos() + QPoint(30 * s_splitWindowIds.size(), 30 * s_splitWindowIds.size()));

//         // 处理窗口事件，确保显示更新
//         QApplication::processEvents();
//     }
// }

// 设置背景区域模式
void LxChart::setBackgroundAreaMode(bool enabled)
{
    m_isSettingBackgroundArea = enabled;

    // 修改按钮状态和文字
    if (enabled)
    {
        // 禁用按钮并修改文字
        ui->btn_setBackgroundArea->setEnabled(false);
        // ui->btn_setBackgroundArea->setText("正在设置背景区域");

        // 进入背景区域模式时，清除现有的背景区域
        clearBackgroundAreaSelection();
    }
    else
    {
        // 恢复按钮状态和文字
        ui->btn_setBackgroundArea->setEnabled(true);

        // 清除临时线和矩形，但保留最终的背景区域矩形
        if (m_bgStartLine)
        {
            m_chart->scene()->removeItem(m_bgStartLine);
            delete m_bgStartLine;
            m_bgStartLine = nullptr;
        }

        if (m_bgEndLine)
        {
            m_chart->scene()->removeItem(m_bgEndLine);
            delete m_bgEndLine;
            m_bgEndLine = nullptr;
        }
    }
}

// 处理背景区域选择
void LxChart::handleBackgroundAreaSelection(qreal startX, qreal endX)
{
    // 确保起点在终点前
    if (startX > endX)
    {
        qSwap(startX, endX);
    }

    // 获取默认曲线
    if (!defaultSeries)
    {
        return;
    }

    // 🎯 修复：设置新背景区域前，先清除旧的平均质谱数据
    qDebug() << "背景区域发生变化，清除现有平均质谱数据";
    AvgMassManager::clearAvgMassMap();
    AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);

    // 保存背景区域范围到成员变量
    m_backgroundAreaRange = qMakePair(startX, endX);

    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    qreal minY = 0;
    qreal maxY = 0;
    if (valueAxisY)
    {
        minY = valueAxisY->min();
        maxY = valueAxisY->max();
    }

    // 获取X轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 先清除可能已存在的背景区域
    if (m_bgRect)
    {
        m_chart->scene()->removeItem(m_bgRect);
        delete m_bgRect;
        m_bgRect = nullptr;
    }

    // 清除可能存在的边界线
    if (m_bgStartLine)
    {
        m_chart->scene()->removeItem(m_bgStartLine);
        delete m_bgStartLine;
        m_bgStartLine = nullptr;
    }

    if (m_bgEndLine)
    {
        m_chart->scene()->removeItem(m_bgEndLine);
        delete m_bgEndLine;
        m_bgEndLine = nullptr;
    }

    // 创建背景区域矩形，使用粉红色
    m_bgRect = new QGraphicsRectItem(m_chart);
    m_bgRect->setPen(Qt::NoPen);
    m_bgRect->setBrush(QBrush(QColor(255, 192, 203, 100))); // 粉红色

    // 确保背景区域在X轴不超出坐标轴范围
    startX = qMax(startX, minX);
    endX = qMin(endX, maxX);

    QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);

    m_bgRect->setRect(QRectF(topLeft, bottomRight).normalized());
    m_bgRect->setZValue(4);

    // 创建左边界虚线，使用灰色
    m_bgStartLine = new QGraphicsLineItem(m_chart);
    m_bgStartLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
    m_bgStartLine->setZValue(5);
    QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
    m_bgStartLine->setLine(QLineF(startTop, startBottom));

    // 创建右边界虚线，使用灰色
    m_bgEndLine = new QGraphicsLineItem(m_chart);
    m_bgEndLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
    m_bgEndLine->setZValue(5);
    QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
    QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
    m_bgEndLine->setLine(QLineF(endTop, endBottom));

    // 触发自定义信号通知数据已更新
    qDebug() << "背景区域设置为:" << startX << "到" << endX << "（坐标轴范围:" << minX << "到" << maxX << ")";
    getBgMassPointVec();
}

// 清除背景区域选择
void LxChart::clearBackgroundAreaSelection()
{
    // 先移除m_bgRect
    if (m_bgRect)
    {
        m_chart->scene()->removeItem(m_bgRect);
        delete m_bgRect;
        m_bgRect = nullptr;
    }

    // 清除边界线
    if (m_bgStartLine)
    {
        m_chart->scene()->removeItem(m_bgStartLine);
        delete m_bgStartLine;
        m_bgStartLine = nullptr;
    }

    if (m_bgEndLine)
    {
        m_chart->scene()->removeItem(m_bgEndLine);
        delete m_bgEndLine;
        m_bgEndLine = nullptr;
    }

    // 清除保存的背景区域范围
    m_backgroundAreaRange = qMakePair(0.0, 0.0);

    qDebug() << "背景区域已彻底清除，同时清除所有平均质谱数据";

    // 🎯 修复：清除背景区域时，必须清除所有平均质谱数据
    AvgMassManager::clearAvgMassMap();
    AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
    AvgMassManager::isRefExist = false;
}

// 按钮槽函数实现
void LxChart::on_btn_YAxsixChange_clicked()
{
    static QElapsedTimer debounceTimer;

    // 如果上次点击在300ms内，忽略此次点击（防抖动）
    if (debounceTimer.isValid() && debounceTimer.elapsed() < 300)
    {
        qDebug() << "忽略快速重复点击，间隔:" << debounceTimer.elapsed() << "ms";
        return;
    }

    // 防止重复点击
    if (m_isProcessingYAxisChange)
    {
        qDebug() << "正在处理Y轴切换，忽略重复点击";
        return;
    }

    // 重置防抖动计时器
    debounceTimer.start();

    // 设置处理标志
    m_isProcessingYAxisChange = true;
    qDebug() << "开始处理Y轴切换 at" << QTime::currentTime().toString("hh:mm:ss.zzz");

    // 切换Y轴模式
    toggleYAxisMode();

    // 清除处理标志
    m_isProcessingYAxisChange = false;
    qDebug() << "结束处理Y轴切换 at" << QTime::currentTime().toString("hh:mm:ss.zzz");
}

void LxChart::on_btn_setBackgroundArea_clicked()
{
    static QElapsedTimer debounceTimer;

    // 如果上次点击在300ms内，忽略此次点击（防抖动）
    if (debounceTimer.isValid() && debounceTimer.elapsed() < 300)
    {
        qDebug() << "忽略设置背景区域的快速重复点击，间隔:" << debounceTimer.elapsed() << "ms";
        return;
    }

    // 重置防抖动计时器
    debounceTimer.start();

    qDebug() << "开始设置背景区域 at" << QTime::currentTime().toString("hh:mm:ss.zzz");
    setBackgroundAreaMode(true);
    qDebug() << "设置背景区域模式完成 at" << QTime::currentTime().toString("hh:mm:ss.zzz");
}

void LxChart::on_btn_showPeakArea_clicked()
{
    static QElapsedTimer debounceTimer;

    // 如果上次点击在300ms内，忽略此次点击（防抖动）
    if (debounceTimer.isValid() && debounceTimer.elapsed() < 300)
    {
        qDebug() << "忽略积分区域显示的快速重复点击，间隔:" << debounceTimer.elapsed() << "ms";
        return;
    }

    // 重置防抖动计时器
    debounceTimer.start();

    qDebug() << "切换积分区域显示状态 at" << QTime::currentTime().toString("hh:mm:ss.zzz");

    // 切换积分区域显示状态
    togglePeakAreas();

    // 更新按钮状态和样式
    if (m_peakAreasVisible)
    {
        ui->btn_showPeakArea->setToolTip("隐藏积分区域");
        // 设置激活状态的样式
        ui->btn_showPeakArea->setProperty("active", true);
        ui->btn_showPeakArea->setStyleSheet("QPushButton#btn_showPeakArea[active=\"true\"] {"
                                            "    background-color: #4CAF50;"
                                            "    border-color: #45a049;"
                                            "    color: white;"
                                            "}"
                                            "QPushButton#btn_showPeakArea[active=\"true\"]:hover {"
                                            "    background-color: #45a049;"
                                            "    border-color: #3d8b40;"
                                            "}");
    }
    else
    {
        ui->btn_showPeakArea->setToolTip("显示积分区域");
        // 恢复默认状态
        ui->btn_showPeakArea->setProperty("active", false);
        ui->btn_showPeakArea->setStyleSheet("");
    }

    // 刷新样式
    ui->btn_showPeakArea->style()->unpolish(ui->btn_showPeakArea);
    ui->btn_showPeakArea->style()->polish(ui->btn_showPeakArea);

    qDebug() << "积分区域显示状态切换完成，当前状态:" << (m_peakAreasVisible ? "显示" : "隐藏");
}

void LxChart::on_btn_splitCharts_clicked()
{
    return;
    static QElapsedTimer debounceTimer;

    // 如果上次点击在300ms内，忽略此次点击（防抖动）
    if (debounceTimer.isValid() && debounceTimer.elapsed() < 300)
    {
        qDebug() << "忽略拆分图层的快速重复点击，间隔:" << debounceTimer.elapsed() << "ms";
        return;
    }

    // 重置防抖动计时器
    debounceTimer.start();

    qDebug() << "开始拆分图层 at" << QTime::currentTime().toString("hh:mm:ss.zzz");
    // splitCharts();
    qDebug() << "拆分图层完成 at" << QTime::currentTime().toString("hh:mm:ss.zzz");
}

void LxChart::on_btn_TIC2XIC_clicked()
{
    // qDebug() << __FUNCTION__;
    if (!allowedTrackTypes.contains(GlobalEnums::TrackType::TIC))
    {
        return;
    }
    if (lastSelectedChartData)
    {
        // 暂时使用TIC原始数据的路径作为参数
        emit sg_TIC2XIC_Clicked(lastSelectedChartData->getParamPath());
    }
}

// 阈值滑块值变化槽函数
void LxChart::on_verticalSlider_valueChanged(int value)
{
    // 🔧 修复：检查图表数据是否为空，避免崩溃
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->axes(Qt::Vertical).isEmpty())
    {
        qDebug() << "LxChart::on_verticalSlider_valueChanged: 图表数据为空或Y轴不存在，跳过滑块处理";
        return;
    }

    QValueAxis *yAxis = qobject_cast<QValueAxis *>(m_chart->axes(Qt::Vertical).first());
    if (!yAxis)
    {
        qDebug() << "LxChart::on_verticalSlider_valueChanged: 无法获取Y轴，跳过滑块处理";
        return;
    }

    double yMin = yAxis->min();
    double yMax = yAxis->max();

    // 将滑块值(0-100)映射到Y轴范围
    double thresholdValue = yMin + (yMax - yMin) * (value / 100.0);

    // qDebug() << "阈值滑块值变化: 滑块值=" << value << "阈值=" << thresholdValue << "Y轴范围[" << yMin << "," << yMax << "]";

    // 设置阈值
    if (value > 0)
    {
        // 滑块值大于0时启用阈值
        enableThreshold(true);
        setThresholdValue(thresholdValue);
    }
    else
    {
        // 滑块值为0时禁用阈值
        enableThreshold(false);
    }
}

/**
 * @brief 向前浏览按钮点击事件
 */
void LxChart::on_btn_lastExperiment_clicked()
{
    // qDebug() << "LxChart::on_btn_lastExperiment_clicked: 向前浏览按钮被点击";
    browsePrevious();
}

/**
 * @brief 向后浏览按钮点击事件
 */
void LxChart::on_btn_nextExperiment_clicked()
{
    // qDebug() << "LxChart::on_btn_nextExperiment_clicked: 向后浏览按钮被点击";
    browseNext();
}

void LxChart::closeEvent(QCloseEvent *event)
{
    qDebug() << "LxChart::closeEvent 开始执行, ID:" << m_windowId << ", 标题:" << windowTitle() << ", 是拆分窗口:" << (m_isSplitWindow ? "是" : "否");

    // 清理资源
    clearRegionSelection();
    clearBackgroundAreaSelection();
    clearCrosshair();

    // 如果是拆分窗口，从拆分窗口列表中移除
    if (m_isSplitWindow)
    {
        // 获取窗口标题，用于标记曲线拆分状态
        QString seriesName = windowTitle();

        // 标记该曲线已不再拆分显示
        s_seriesSplitStatus[seriesName] = false;

        // 从拆分窗口列表中移除ID
        s_splitWindowIds.removeOne(m_windowId);

        qDebug() << "closeEvent移除拆分状态:"
                 << "\n  窗口标题:" << seriesName << "\n  窗口ID:" << m_windowId << "\n  当前拆分窗口数量:" << s_splitWindowIds.size()
                 << "\n  拆分状态列表内容:" << s_seriesSplitStatus.keys();
    }

    // 继续原有的关闭处理
    QWidget::closeEvent(event);

    qDebug() << "LxChart::closeEvent 执行完成, ID:" << m_windowId;
}

// 添加到拆分窗口列表
void LxChart::addToSplitWindows()
{
    if (!s_splitWindowIds.contains(m_windowId))
    {
        s_splitWindowIds.append(m_windowId);
        qDebug() << "添加到拆分窗口列表，当前拆分窗口数：" << s_splitWindowIds.size();
    }
}

// 从拆分窗口列表中移除
void LxChart::removeFromSplitWindows()
{
    if (s_splitWindowIds.contains(m_windowId))
    {
        // 获取窗口标题，用于标记曲线拆分状态
        QString seriesName = windowTitle();

        // 标记该曲线已不再拆分显示
        s_seriesSplitStatus[seriesName] = false;

        // 从拆分窗口列表中移除ID
        s_splitWindowIds.removeOne(m_windowId);

        qDebug() << "从拆分窗口列表移除，窗口ID:" << m_windowId;
        qDebug() << "标记曲线" << seriesName << "为未拆分状态";
        qDebug() << "当前拆分窗口数量:" << s_splitWindowIds.size();
    }
}

// 清除十字准星
void LxChart::clearCrosshair(bool useDefalutCursor)
{
    if (m_crosshairHLine)
    {
        m_chart->scene()->removeItem(m_crosshairHLine);
        delete m_crosshairHLine;
        m_crosshairHLine = nullptr;
    }

    if (m_crosshairVLine)
    {
        m_chart->scene()->removeItem(m_crosshairVLine);
        delete m_crosshairVLine;
        m_crosshairVLine = nullptr;
    }

    if (m_coordinateText)
    {
        m_chart->scene()->removeItem(m_coordinateText);
        delete m_coordinateText;
        m_coordinateText = nullptr;
    }

    // 恢复鼠标光标
    if (m_hideCursorWithCrosshair && m_chartView)
    {
        if (useDefalutCursor)
        {
            m_chartView->viewport()->setCursor(Qt::ArrowCursor);
        }
        else
        {
            m_chart->unsetCursor();
        }
    }
}

// 显示/隐藏十字准星
void LxChart::setCrosshairVisible(bool visible)
{
    m_showCrosshair = visible;

    if (!visible)
    {
        clearCrosshair();
    }
}

// 设置十字准星线型样式
void LxChart::setCrosshairStyle(Qt::PenStyle style)
{
    // 更新样式
    m_crosshairStyle = style;

    // 如果十字准星已存在，则更新样式
    if (m_crosshairHLine)
    {
        QPen pen = m_crosshairHLine->pen();
        pen.setStyle(m_crosshairStyle);
        m_crosshairHLine->setPen(pen);
    }

    if (m_crosshairVLine)
    {
        QPen pen = m_crosshairVLine->pen();
        pen.setStyle(m_crosshairStyle);
        m_crosshairVLine->setPen(pen);
    }

    qDebug() << "十字准星线型样式已设置为:" << (m_crosshairStyle == Qt::DashLine ? "虚线" : "实线");
}

// 设置十字准星长度
void LxChart::setCrosshairLength(int length)
{
    if (length <= 0)
    {
        qDebug() << "警告: 十字准星长度必须大于0，使用默认值20";
        length = 20;
    }

    m_crosshairLength = length;

    // 如果十字准星已存在且可见，则更新
    if (m_crosshairHLine && m_crosshairVLine)
    {
        QPointF center(m_crosshairHLine->line().center());

        // 更新水平线
        m_crosshairHLine->setLine(center.x() - m_crosshairLength, center.y(), center.x() + m_crosshairLength, center.y());

        // 更新垂直线
        m_crosshairVLine->setLine(center.x(), center.y() - m_crosshairLength, center.x(), center.y() + m_crosshairLength);
    }

    qDebug() << "十字准星长度已设置为:" << m_crosshairLength << "像素";
}

// 更新十字准星位置
void LxChart::updateCrosshair(const QPointF &pos)
{
    if (!m_showCrosshair || m_isDraggingBgLeft || m_isDraggingBgRight)
    {
        // 如果十字准星被禁用或正在拖动背景区域，不显示十字准星
        clearCrosshair();
        return;
    }

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "LxChart::updateCrosshair: 图表无数据系列，清除十字准星";
        clearCrosshair();
        return;
    }

    // 获取默认曲线
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "LxChart::updateCrosshair: 无法获取默认系列，清除十字准星";
            clearCrosshair();
            return;
        }
    }
    // 获取图表的坐标区域
    QRectF plotArea = m_chart->plotArea();

    // 如果鼠标不在坐标区域内，隐藏十字准星并恢复光标
    if (!plotArea.contains(pos))
    {
        clearCrosshair();
        // 恢复鼠标光标
        if (m_hideCursorWithCrosshair && m_chartView)
        {
            m_chartView->viewport()->setCursor(Qt::ArrowCursor);
        }
        return;
    }

    // 如果设置了隐藏鼠标光标，在显示十字准星前隐藏光标
    if (m_hideCursorWithCrosshair && m_chartView && !m_isDraggingBgLeft && !m_isDraggingBgRight)
    {
        m_chartView->viewport()->setCursor(Qt::BlankCursor);
    }

    // 获取鼠标位置对应的数据坐标
    dataPoint = m_chart->mapToValue(pos, defaultSeries);

    // 创建或更新水平线
    if (!m_crosshairHLine)
    {
        m_crosshairHLine = new QGraphicsLineItem(m_chart);
        m_crosshairHLine->setPen(QPen(Qt::red, 1, Qt::DashLine)); // 设置为虚线
        m_crosshairHLine->setZValue(100);                         // 确保在最上层
    }
    else
    {
        m_crosshairHLine->setPen(QPen(Qt::red, 1, Qt::DashLine)); // 更新为虚线
    }

    // 创建或更新垂直线
    if (!m_crosshairVLine)
    {
        m_crosshairVLine = new QGraphicsLineItem(m_chart);
        m_crosshairVLine->setPen(QPen(Qt::red, 1, Qt::DashLine)); // 设置为虚线
        m_crosshairVLine->setZValue(100);
    }
    else
    {
        m_crosshairVLine->setPen(QPen(Qt::red, 1, Qt::DashLine)); // 更新为虚线
    }

    // 设置水平线跨越整个图表宽度
    m_crosshairHLine->setLine(plotArea.left(), pos.y(), plotArea.right(), pos.y());

    // 设置垂直线跨越整个图表高度
    m_crosshairVLine->setLine(pos.x(), plotArea.top(), pos.x(), plotArea.bottom());

    // 创建或更新坐标文本
    if (!m_coordinateText)
    {
        m_coordinateText = new QGraphicsTextItem(m_chart);
        m_coordinateText->setZValue(101); // 确保显示在十字准星之上

        // 设置文本样式
        QFont font = m_coordinateText->font();
        font.setBold(true);
        m_coordinateText->setFont(font);

        // 设置背景矩形
        QGraphicsRectItem *bg = new QGraphicsRectItem(m_coordinateText);
        bg->setBrush(QBrush(QColor(255, 255, 255, 200))); // 半透明白色背景
        bg->setPen(QPen(Qt::lightGray));
        bg->setZValue(-1); // 确保在文本底层
    }

    // 格式化坐标文本
    QString xValueStr, yValueStr;
    if (qAbs(dataPoint.x()) < 0.01)
    {
        xValueStr = "0";
    }
    else if (qAbs(dataPoint.x()) < 10)
    {
        xValueStr = QString::number(dataPoint.x(), 'f', 3);
    }
    else if (qAbs(dataPoint.x()) < 100)
    {
        xValueStr = QString::number(dataPoint.x(), 'f', 2);
    }
    else
    {
        xValueStr = QString::number(dataPoint.x(), 'f', 1);
    }

    // 在数值模式下，直接显示y坐标值
    if (m_isPercentMode)
    {
        double diff = m_globalMaxY - m_globalMinY;
        if (diff == 0)
        {
            diff = 1;
        }
        yValueStr = QString::number((dataPoint.y() - m_globalMinY) * 100 / diff, 'f', 4) + "%";
    }
    else
    {
        yValueStr = QString::number(dataPoint.y(), 'f', 4);
    }

    m_coordinateText->setHtml(QString("<div style='background: rgba(255,255,255,0.7); padding: 2px;'>"
                                      "<span style='color:black;'>X: %1</span><br/>"
                                      "<span style='color:black;'>Y: %2</span></div>")
                                  .arg(xValueStr)
                                  .arg(yValueStr));

    // 计算文本位置
    QPointF textPos = pos;

    // 如果是在背景区域拖动时，将气泡显示在右上方
    if (m_isDraggingBgLeft || m_isDraggingBgRight || isNearBgLeftEdge(pos) || isNearBgRightEdge(pos))
    {
        textPos.setX(textPos.x() + 15);                                             // 向右偏移15个像素
        textPos.setY(textPos.y() - 15 - m_coordinateText->boundingRect().height()); // 向上偏移文本高度+15像素
    }
    else
    {
        // 普通情况下放在鼠标位置的右上方
        textPos.setX(textPos.x() + 10);                                             // 向右偏移10个像素
        textPos.setY(textPos.y() - 10 - m_coordinateText->boundingRect().height()); // 向上偏移文本高度+10像素
    }

    // 确保文本框不超出图表区域
    if (textPos.x() + m_coordinateText->boundingRect().width() > plotArea.right())
    {
        // 如果超出右边界，放到左边
        textPos.setX(pos.x() - 10 - m_coordinateText->boundingRect().width());
    }

    if (textPos.y() < plotArea.top())
    {
        // 如果超出上边界，放到下方
        textPos.setY(pos.y() + 10);
    }

    m_coordinateText->setPos(textPos);

    // 调整背景矩形大小
    if (m_coordinateText->childItems().size() > 0)
    {
        QGraphicsRectItem *bg = qgraphicsitem_cast<QGraphicsRectItem *>(m_coordinateText->childItems().at(0));
        if (bg)
        {
            bg->setRect(m_coordinateText->boundingRect());
        }
    }
}

// 切换十字准星样式（在虚线和实线之间切换）
void LxChart::toggleCrosshairStyle()
{
    // 在虚线和实线之间切换
    if (m_crosshairStyle == Qt::DashLine)
    {
        setCrosshairStyle(Qt::SolidLine);
    }
    else
    {
        setCrosshairStyle(Qt::DashLine);
    }

    qDebug() << "十字准星样式已切换为:" << (m_crosshairStyle == Qt::DashLine ? "虚线" : "实线");
}

// 设置是否在显示十字准星时隐藏鼠标光标
void LxChart::setHideCursorWithCrosshair(bool hide)
{
    m_hideCursorWithCrosshair = hide;

    qDebug() << "设置十字准星显示时" << (hide ? "隐藏" : "显示") << "鼠标光标";

    if (m_chartView)
    {
        // 如果当前有显示十字准星，立即应用设置
        if (m_showCrosshair && m_crosshairHLine != nullptr)
        {
            m_chartView->viewport()->setCursor(hide ? Qt::BlankCursor : Qt::ArrowCursor);
            qDebug() << "立即应用了鼠标光标设置: " << (hide ? "隐藏" : "显示");
        }
        else
        {
            // 如果当前没有显示十字准星，恢复默认光标
            m_chartView->viewport()->setCursor(Qt::ArrowCursor);
            qDebug() << "没有显示十字准星，设置默认光标";
        }
    }
}

void LxChart::setOriginAxisModeStyle(bool isPercent)
{
    // 设置坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);

    // 转换为具体的轴类型
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    if (isPercent)
    {
        valueAxisY->setLabelsColor(Qt::white);
    }
    else
    {
        valueAxisY->setLabelsColor(AxisLabelDefaultColor);
    }
}

void LxChart::initDefaultSeries()
{
    if (defaultSeries != nullptr)
    {
        return;
    }

    // 创建新曲线
    defaultSeries = new QLineSeries();
    defaultSeries->setName("");

    // 添加到图表
    m_chart->addSeries(defaultSeries);

    // 确保曲线连接到坐标轴
    if (m_chart->axes().size() > 0)
    {
        defaultSeries->attachAxis(m_chart->axes(Qt::Horizontal).at(0));
        defaultSeries->attachAxis(m_chart->axes(Qt::Vertical).at(0));
    }
    else
    {
        // 如果没有坐标轴，创建默认的
        m_chart->createDefaultAxes();
    }
}

void LxChart::showNearestCurve(QMouseEvent *event)
{
    // 当前鼠标位置
    QPoint pos = event->pos();

    // 具体目标值坐标 - 获取在图表坐标系中的位置
    const double destX = m_chart->mapToValue(m_chartView->mapToScene(pos)).x();
    const double destY = m_chart->mapToValue(m_chartView->mapToScene(pos)).y();

    if (destX < 0 || destY < 0)
    {
        // 不在图表区域内的不进行查询，以免浪费资源
        return;
    }

    LxChartData *nearestChartData = nullptr;

    bool res = false;

    // 遍历全部曲线
    for (int i = 0; i < m_chartDataVec.size(); i++)
    {
        LxChartData *chartData = m_chartDataVec.at(i);

        // 安全检查：确保chartData有效
        if (!chartData)
        {
            qDebug() << "LxChart::showNearestCurve: 警告：chartData为空，索引:" << i;
            continue;
        }

        try
        {
            int index = findIndex(chartData->getDataX(), destX);

            if (index == -1)
            {
                continue;
            }

            // 检查曲线可见性：如果有legend则检查，如果没有legend则认为可见（如XIC数据）
            if (chartData->legend && !chartData->legend->bool_curveVisible())
            {
                continue;
            }

            const QVector<QPointF> &pf = chartData->getData();

            // 安全检查：确保数据有效
            if (pf.isEmpty())
            {
                continue;
            }

            // 所有曲线使用统一的检测方法
            res = isPointNearLine(QPointF(destX, destY), pf, 5, index);
        }
        catch (const std::exception &e)
        {
            qDebug() << "LxChart::showNearestCurve: 处理chartData时异常:" << e.what() << "，UniqueID:" << chartData->getUniqueID();
            continue;
        }
        catch (...)
        {
            qDebug() << "LxChart::showNearestCurve: 处理chartData时未知异常，UniqueID:" << chartData->getUniqueID();
            continue;
        }

        if (res)
        {
            nearestChartData = chartData;
            break;
        }
    }

    if (res)
    {
        // 如果当前曲线不是高亮状态，则设置为高亮
        if (lastHighlightedChartData != nearestChartData)
        {
            // 如果有上一个高亮曲线，恢复其粗细
            if (lastHighlightedChartData)
            {
                restoreOriginalWidth(lastHighlightedChartData);
            }

            // 高亮当前曲线（变粗）
            setHighlightWidth(nearestChartData);
            lastHighlightedChartData = nearestChartData;
            m_chart->update();
        }
    }
    else
    {
        // 如果没有找到足够近的点，恢复所有曲线粗细
        if (lastHighlightedChartData)
        {
            restoreOriginalWidth(lastHighlightedChartData);
            lastHighlightedChartData = nullptr;
            m_chart->update();
        }
    }
}

// 添加、更新或获取曲线，确保所有曲线都使用统一的线宽设置
QLineSeries *LxChart::getSeries(const QString &name, bool createIfNotExists, const QColor &color)
{
    Q_UNUSED(createIfNotExists);
    Q_UNUSED(color);
    // 先查找是否已存在同名曲线
    QLineSeries *existingSeries = nullptr;
    for (int i = 0; i < m_chart->series().size(); i++)
    {
        const auto series = m_chart->series().at(i);
        QLineSeries *lineSeries = qobject_cast<QLineSeries *>(series);
        if (lineSeries && lineSeries->name() == name)
        {
            existingSeries = lineSeries;
            break;
        }
    }

    // 如果找到了同名曲线，返回它
    if (existingSeries)
    {
        // 确保线宽正确
        QPen pen = existingSeries->pen();
        double currentLineWidth = OptionsDialogSettings::getSpectrumLineWidth();
        if (qAbs(pen.widthF() - currentLineWidth) > 0.01)
        {
            // 如果线宽不一致，更新它
            pen.setWidthF(currentLineWidth);
            existingSeries->setPen(pen);
            qDebug() << "更新已存在曲线" << name << "的线宽为" << currentLineWidth;
        }
        return existingSeries;
    }
    return nullptr;
}

// 静态方法：关闭所有子窗口
void LxChart::closeAllSplitWindows()
{
    // 创建一个临时列表，因为在关闭过程中s_splitWindowIds会被修改
    QList<QString> tempList = s_splitWindowIds;

    qDebug() << "开始关闭所有子窗口，数量:" << tempList.size();

    // 查找并关闭所有子窗口
    for (const QString &windowId : tempList)
    {
        // 查找所有顶级窗口
        QWidgetList allWidgets = QApplication::topLevelWidgets();

        for (int i = 0; i < allWidgets.size(); i++)
        {
            const auto widget = allWidgets.at(i);
            // 尝试转换为LxChart
            LxChart *lxChart = qobject_cast<LxChart *>(widget);

            // 如果是LxChart且ID匹配，则关闭
            if (lxChart && lxChart->getWindowId() == windowId)
            {
                qDebug() << "关闭子窗口:" << lxChart->windowTitle() << ", ID:" << windowId;

                // 解除拆分状态
                QString seriesName = lxChart->windowTitle();
                s_seriesSplitStatus[seriesName] = false;

                // 标记为不是拆分窗口，防止closeEvent中再次处理
                lxChart->setSplitWindow(false);

                // 强制立即关闭窗口，不使用事件循环
                lxChart->setParent(nullptr); // 解除父子关系
                lxChart->close();            // 关闭窗口

                // 立即删除窗口，不等待事件循环
                delete lxChart;

                break;
            }
        }
    }

    // 清空静态列表和拆分状态
    s_splitWindowIds.clear();
    s_seriesSplitStatus.clear();

    qDebug() << "所有子窗口已关闭，强制清空拆分窗口列表和状态";
    QApplication::processEvents(); // 确保处理完所有pending的delete事件
}

// 设置交互模式
void LxChart::setInteractionMode(GlobalEnums::InteractionMode mode)
{
    m_interactionMode = mode;
    qDebug() << "设置交互模式为: " << static_cast<int>(m_interactionMode);

    // 根据交互模式设置相应的属性
    switch (mode)
    {
    case GlobalEnums::InteractionMode::Mode_None:
        // 禁用所有交互
        m_chartView->setRubberBand(QChartView::NoRubberBand);
        break;
    case GlobalEnums::InteractionMode::Mode_Pan:
        // 启用拖拽模式
        m_chartView->setRubberBand(QChartView::VerticalRubberBand);
        break;
    case GlobalEnums::InteractionMode::Mode_Select:
        // 点选模式
        m_chartView->setRubberBand(QChartView::NoRubberBand);
        break;
    case GlobalEnums::InteractionMode::Mode_ZoomRect:
        // 框选缩放模式
        m_chartView->setRubberBand(QChartView::RectangleRubberBand);
        break;
    }
}

// 启用/禁用点选功能
void LxChart::enablePointSelection(bool enable)
{
    m_enablePointSelection = enable;
    qDebug() << "点选功能" << (enable ? "已启用" : "已禁用");
}

// 高亮显示选中的点
void LxChart::highlightPoint(const QPointF &point)
{
    // 保存当前高亮点的坐标
    m_currentHighlightPoint = point;

    // 先清除当前高亮点
    if (m_highlightPoint)
    {
        m_chart->scene()->removeItem(m_highlightPoint);
        delete m_highlightPoint;
        m_highlightPoint = nullptr;
    }

    // 创建新的高亮点
    QPen pen(Qt::red, 2);
    QBrush brush(Qt::red);

    // 将数据坐标转换为像素坐标
    QPointF scenePoint = m_chart->mapToPosition(point);

    // 创建椭圆项
    m_highlightPoint = new QGraphicsEllipseItem(scenePoint.x() - 5, scenePoint.y() - 5, 10, 10);
    m_highlightPoint->setPen(pen);
    m_highlightPoint->setBrush(brush);

    // 添加到场景
    m_chart->scene()->addItem(m_highlightPoint);

    // qDebug() << "高亮显示点 (" << point.x() << ", " << point.y() << ")";
}

// 处理鼠标按下事件
void LxChart::showMassChart()
{
    QPointF valuePos = dataPoint;

    QVector<std::tuple<QString, QPointF, int>> vecPair;

    // 遍历所有曲线的最小距离点
    for (int i = 0; i < m_chartDataVec.size(); i++)
    {
        const auto chartData = m_chartDataVec.at(i);
        QVector<QPointF> dataPoints = chartData->getData();
        QVector<double> xData;
        qDebug() << "遍历TIC" << i;

        // 只提取x坐标数据用于findIndex
        for (int j = 0; j < dataPoints.size(); j++)
        {
            const auto point = dataPoints.at(j);
            xData.append(point.x());
        }

        // 使用findIndex快速找到最近x坐标的索引
        int index = findIndex(xData, valuePos.x(), false);

        if (index >= 0)
        {
            // 直接使用findIndex找到的点
            QPointF point = dataPoints[index];
            vecPair.push_back(std::make_tuple(chartData->getParamPath(), point, chartData->getEventNum()));
        }
    }
    emit sg_showMassChart(vecPair);
}

CustomLabelWidget *LxChart::isHoverInCustomLabelWidget()
{
    foreach (CustomLabelWidget *widget, vecLabelWidget)
    {
        if (widget->m_bool_isHover)
        {
            return widget;
        }
    }
    return nullptr;
}

void LxChart::refreashLabelWidgetPos()
{
    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        // qDebug() << "图表无数据系列，跳过位置刷新";
        return;
    }

    updateTempBackgroundArea();

    // 同时更新自定义区域
    updateCustomArea();
    // 更新标峰
    updatePeaksPos();

    // 确保存在默认曲线用于坐标转换
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "刷新标签位置失败：没有默认曲线";
            return;
        }
    }

    foreach (CustomLabelWidget *widget, vecLabelWidget)
    {
        // 使用真实的数据坐标而不是像素坐标，确保在缩放时能正确更新位置
        QPointF dataPoint = widget->getPoint();
        QPoint position = m_chart->mapToPosition(dataPoint, defaultSeries).toPoint();
        widget->move(position.x(), position.y());
        if (widget->isLabelVisible())
        {
            widget->show();
        }

        // 更新对应标记点的位置
        if (widget->getMarker())
        {
            widget->getMarker()->setPos(position);
        }

        qDebug() << "更新标签位置 - 数据坐标:" << dataPoint << "屏幕坐标:" << position;
        updateLabelPosition(widget);
    }
}

// 处理鼠标释放事件
void LxChart::mouseReleaseEvent(QMouseEvent *event)
{
    QWidget::mouseReleaseEvent(event);
}

void LxChart::showMassHightPoint()
{
    QPointF valuePos = dataPoint;

    QPointF globalClosestPoint;
    LxChartData *globalClosestCurve = nullptr;
    double minXDistance = std::numeric_limits<double>::max();
    double minYDistance = std::numeric_limits<double>::max();

    // 遍历所有曲线的Peaks
    for (int i = 0; i < m_chartDataVec.size(); i++)
    {
        QAbstractSeries *abstractSeries = m_chart->series().at(i);
        if (!abstractSeries->isVisible())
        {
            continue;
        }
        const auto chartData = m_chartDataVec.at(i);
        std::vector<Peak> temmPeaksVec = chartData->peakVec;
        QVector<double> xData;

        // 只提取x坐标数据用于findIndex
        for (int j = 0; j < temmPeaksVec.size(); j++)
        {
            const auto point = temmPeaksVec.at(j).pTop;
            xData.append(point.x());
        }

        // 使用findIndex快速找到最近x坐标的索引
        int index = findIndex(xData, valuePos.x(), false);

        if (index >= 0)
        {
            // 直接使用findIndex找到的点
            QPointF point = temmPeaksVec[index].pTop;
            QPointF pixelP1 = m_chart->mapToPosition(point);
            QPointF pixelP2 = m_chart->mapToPosition(dataPoint);

            // 先计算x轴像素距离
            double xDistance = qAbs(pixelP1.x() - pixelP2.x());

            // 如果当前点的x距离更大，直接跳过
            if (xDistance > minXDistance)
            {
                continue;
            }

            // 如果x距离相等，则比较y距离
            if (qAbs(xDistance - minXDistance) < 0.01)
            {
                double yDistance = qAbs(pixelP1.y() - pixelP2.y());
                if (yDistance < minYDistance)
                {
                    minYDistance = yDistance;
                    globalClosestPoint = point;
                    globalClosestCurve = chartData;
                }
            }
            else
            {
                // x距离更小，更新所有记录
                minXDistance = xDistance;
                minYDistance = qAbs(pixelP1.y() - pixelP2.y());
                globalClosestPoint = point;
                globalClosestCurve = chartData;
            }
        }
    }

    // 如果找到了最近点，直接显示
    if (globalClosestCurve)
    {
        // 高亮显示该点
        highlightPoint(globalClosestPoint);

        // 旧的单个XIC信号已删除，现在使用LxMassChart的sg_showMultipleXicChart处理所有XIC加载
        // emit sg_showXicChart(globalClosestPoint.x()); // 已删除
        // 发送点击信号
        // emit sg_chartDoubleClicked(globalClosestCurve->getParamPath(), globalClosestPoint, trackType);
        return;
    }
}

// 添加LxChartData引用到管理数组
void LxChart::AddLxChartData(LxChartData *chartData)
{
    if (!m_bool_isRemoveDefaultLegend)
    {
        if (defaultSeries)
        {
            m_chart->removeSeries(defaultSeries);
            defaultSeries = nullptr;
        }
        m_bool_isRemoveDefaultLegend = true;
    }

    if (!chartData)
    {
        qDebug() << "错误: AddLxChartData 传入了空指针";
        return;
    }

    // 检查轨迹类型是否被允许
    if (!isTrackTypeAllowed(chartData->getTrackType()))
    {
        qDebug() << "轨迹类型不被当前图表允许！无法添加";
        return;
    }

    // 如果当前数量超过限制，移除多余的曲线
    if (m_maxChartDataCount != -1 && !(m_chartDataVec.size() < m_maxChartDataCount))
    {
        // 从最早添加的开始移除，直到满足限制
        while (!(m_chartDataVec.size() < m_maxChartDataCount))
        {
            // 获取最早添加的曲线的UniqueID
            QString oldestID = m_chartDataVec.first()->getUniqueID();
            // 移除该曲线
            RemoveLxChartDataByUniqueID(oldestID);
        }
    }

    // 遍历当前是否有来自同一条TIC曲线的MASS曲线，如果有则删除
    if (chartData->getTrackType() == GlobalEnums::TrackType::MS)
    {
        QStringList toRemove;
        foreach (LxChartData *data, m_chartDataVec)
        {
            if (static_cast<MassChartData *>(chartData)->getParamPath() == static_cast<MassChartData *>(data)->getParamPath() &&
                static_cast<MassChartData *>(chartData)->getTic_event_id() == static_cast<MassChartData *>(data)->getTic_event_id())
            {
                qDebug() << "LxChart::AddLxChartData: 发现重复MASS数据，准备移除:" << data->getUniqueID();
                toRemove.append(data->getUniqueID());
            }
        }

        // 🎯 批量删除并强制处理事件，确保图形项完全清理
        for (const QString &uniqueId : toRemove)
        {
            RemoveLxChartDataByUniqueID(uniqueId);
        }

        // 🎯 强制处理Qt事件队列，确保场景清理完成
        if (!toRemove.isEmpty())
        {
            qDebug() << "LxChart::AddLxChartData: 强制处理事件队列，确保图形项清理完成";
            QCoreApplication::processEvents();

            // 额外的场景清理检查
            if (m_chart && m_chart->scene())
            {
                QList<QGraphicsItem *> allItems = m_chart->scene()->items();
                int orphanedItems = 0;
                for (QGraphicsItem *item : allItems)
                {
                    // 检查是否有孤立的图形项（没有父对象且不是图表本身的项）
                    if (!item->parentItem() && item != m_chart)
                    {
                        orphanedItems++;
                    }
                }
                if (orphanedItems > 0)
                {
                    qDebug() << "LxChart::AddLxChartData: 检测到" << orphanedItems << "个可能的孤立图形项";
                }
            }
        }
    }
    if (allowedTrackTypes.contains(GlobalEnums::TrackType::TIC))
    {
        // qDebug() << "新增TIC曲线，将由LxTicXicChart自动处理平均质谱计算";
        // 注释掉这个重置逻辑，让LxTicXicChart的checkAndCalculateAvgMassForNewTic方法来处理
        // 这样可以避免打断正在进行的计算，并支持增量计算
        // DataReader::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
    }
    // 为曲线生成随机颜色或使用已有颜色
    QColor curveColor;
    QString colorHex;
    if (chartData->getTrackType() == GlobalEnums::TrackType::MS)
    {
        // MASS曲线使用对应TIC曲线的颜色
        curveColor = FileDataManager::getOriginColorByEvent(static_cast<MassChartData *>(chartData)->getParamPath(),
                                                            static_cast<MassChartData *>(chartData)->getTic_event_id());
        colorHex = curveColor.name();
    }
    else if (chartData->getTrackType() == GlobalEnums::TrackType::XIC)
    {
        // XIC曲线使用独立的随机颜色，不与TIC同步
        // 优先使用被释放的颜色
        if (!freeColorStack.isEmpty())
        {
            colorHex = freeColorStack.pop();
            curveColor = QColor(colorHex);
            qDebug() << "XIC使用释放的颜色" << colorHex;
        }
        else
        {
            // 如果没有被释放的颜色，则生成新的随机颜色
            curveColor = generateRandomColor();
            colorHex = curveColor.name();
            // qDebug() << "XIC生成新颜色" << colorHex;
        }
    }
    else
    {
        // 优先使用被释放的颜色
        if (!freeColorStack.isEmpty())
        {
            colorHex = freeColorStack.pop();
            curveColor = QColor(colorHex);
            // qDebug() << "使用释放的颜色" << colorHex;
        }
        else
        {
            // 如果没有被释放的颜色，则生成新的随机颜色
            curveColor = generateRandomColor();
            colorHex = curveColor.name();
            // qDebug() << "生成新颜色" << colorHex;
        }
    }
    // qDebug() << "数据点数:" << chartData->getData().size();
    // 设置曲线颜色
    chartData->setLineColor(curveColor);
    chartData->setOriginalColor(curveColor);
    if (chartData->legend == nullptr)
    {
        // qDebug() << "legend不存在，创建新的LxChartLegend";

        // 根据轨迹类型确定显示文本
        QString displayText;
        // qDebug() << "处理显示文本，类型:" << (int)chartData->getTrackType() << "，路径:" << chartData->getParamPath();

        if (chartData->getTrackType() == GlobalEnums::TrackType::TIC || chartData->getTrackType() == GlobalEnums::TrackType::MS)
        {
            // TIC和MASS显示文件路径
            displayText = chartData->getParamPath();
            // qDebug() << "TIC/MASS显示文本:" << displayText;
        }
        else if (chartData->getTrackType() == GlobalEnums::TrackType::XIC)
        {
            // XIC显示文件路径_索引格式
            // 从XIC的title中提取索引（title格式：复杂标题_索引）
            QString title = chartData->getTitle();
            int underscorePos = title.lastIndexOf('_');
            qDebug() << "LxChart::AddLxChartData: XIC title:" << title << "，下划线位置:" << underscorePos;
            if (underscorePos != -1)
            {
                QString indexStr = title.mid(underscorePos + 1);
                displayText = QString("%1_%2").arg(chartData->getParamPath()).arg(indexStr);
                qDebug() << "LxChart::AddLxChartData: XIC提取索引:" << indexStr << "，最终显示文本:" << displayText;
            }
            else
            {
                // 如果没有找到索引，使用默认格式
                displayText = QString("%1_0").arg(chartData->getParamPath());
                qDebug() << "LxChart::AddLxChartData: XIC未找到索引，使用默认格式:" << displayText;
            }
        }
        else
        {
            // 其他类型使用文件路径
            displayText = chartData->getParamPath();
            qDebug() << "LxChart::AddLxChartData: 其他类型显示文本:" << displayText;
        }

        // 为每个曲线创建独立的LxChartLegend，并设置正确的类型
        chartData->legend = new LxChartLegend(chartData->getParamPath(), displayText, "", chartData->getUniqueID(), "toolTip:" + chartData->getParamPath(),
                                              chartData->getTrackType() // 传递曲线类型
        );

        // 建立曲线与Legend的关联
        QUuid legendId = QUuid::createUuid();
        chartData->setLegendId(legendId);

        // qDebug() << "创建Legend，类型:" << (int)chartData->getTrackType() << "，ID:" << legendId.toString();
    }
    else
    {
        // qDebug() << "legend已存在，更新类型显示";
        chartData->legend->setTrackType(chartData->getTrackType());
    }
    chartData->legend->setColor(colorHex);

    if (!customLegendWidget->isVisible())
    {
        customLegendWidget->setVisible(true);
    }

    customLegendWidget->addLegendWidget(chartData->legend);

    connect(chartData->legend, &LxChartLegend::sg_deleteLegend, [=](QString id)
            {
        qDebug() << "发送方:" << chartData->legend->path() << chartData->legend->getUniqueID();
        if (m_bool_isDeleteData) {
            qDebug() << "当前正在删除数据，忽略重复删除请求，UniqueID:" << id;
            return;
        }

        // 🎯 修复：使用try-finally模式确保标志位一定会被重置
        m_bool_isDeleteData = true;
        qDebug() << "开始删除数据，UniqueID:" << id;

        try {
            RemoveLxChartDataByUniqueID(id);
            qDebug() << "删除数据完成，UniqueID:" << id;
        } catch (...) {
            qDebug() << "删除数据时发生异常，UniqueID:" << id;
        }

        // 🎯 关键：无论是否异常，都要重置标志位
        m_bool_isDeleteData = false; });

    connect(chartData->legend, &LxChartLegend::sg_changeLineWidth, [=](QString id, int width)
            {
        // 设置曲线线宽
        for (int i = 0; i < m_chartDataVec.size(); i++) {
            if (m_chartDataVec.at(i)->getUniqueID() == id) {
                LxChartData *targetData = m_chartDataVec.at(i);
                QPen pen = targetData->getDefaultPen();
                pen.setWidth(width);
                targetData->setDefaultPen(pen);

                // 🎯 如果是MRM数据，设置所有棒子系列的线宽
                if (targetData->isMrmData() && !targetData->getMrmBarSeries().isEmpty()) {
                    for (QAbstractSeries *barSeries : targetData->getMrmBarSeries()) {
                        QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries);
                        if (lineSeries) {
                            lineSeries->setPen(pen);
                        }
                    }
                } else {
                    // 普通数据：设置单个系列
                    QAbstractSeries *abstractSeries = targetData->getSeries();
                    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                    if (lineSeries) {
                        lineSeries->setPen(pen);
                    }
                }
                break;
            }
        } });

    // 连接图例点击信号
    connect(chartData->legend, &LxChartLegend::sg_legendClicked, this, &LxChart::handleLegendClicked);

    // 连接导出数据信号
    connect(chartData->legend, &LxChartLegend::sg_exportData, this, &LxChart::handleExportData);

    connect(chartData->legend, &LxChartLegend::sg_changeColor, [=](QString id, QColor color)
            {
        // 设置曲线颜色
        for (int i = 0; i < m_chartDataVec.size(); i++) {
            if (m_chartDataVec.at(i)->getUniqueID() == id) {
                LxChartData *targetData = m_chartDataVec.at(i);
                targetData->setOriginalColor(color);
                QPen pen = targetData->getDefaultPen();
                pen.setColor(color);
                targetData->setDefaultPen(pen);

                // 🎯 如果是MRM数据，设置所有棒子系列的颜色
                if (targetData->isMrmData() && !targetData->getMrmBarSeries().isEmpty()) {
                    for (QAbstractSeries *barSeries : targetData->getMrmBarSeries()) {
                        QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries);
                        if (lineSeries) {
                            lineSeries->setPen(pen);
                            lineSeries->setColor(color);
                        }
                    }
                } else {
                    // 普通数据：设置单个系列
                    QAbstractSeries *abstractSeries = targetData->getSeries();
                    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                    if (lineSeries) {
                        lineSeries->setPen(pen);
                        lineSeries->setColor(color);
                    }
                }
                break;
            }
        } });

    connect(chartData->legend, &LxChartLegend::sg_changeVisible, [=](QString id, bool visible)
            {
        // 设置曲线是否可见
        for (int i = 0; i < m_chartDataVec.size(); i++) {
            if (m_chartDataVec.at(i)->getUniqueID() == id) {
                LxChartData *targetData = m_chartDataVec.at(i);

                // 🎯 如果是MRM数据，设置所有棒子系列的可见性
                if (targetData->isMrmData() && !targetData->getMrmBarSeries().isEmpty()) {
                    for (QAbstractSeries *barSeries : targetData->getMrmBarSeries()) {
                        if (barSeries) {
                            barSeries->setVisible(visible);
                        }
                    }
                    qDebug() << "LxChart::sg_changeVisible: 设置MRM数据可见性:" << visible << "，系列数量:" << targetData->getMrmBarSeries().size();
                } else {
                    // 普通数据：设置单个系列
                    QAbstractSeries *abstractSeries = targetData->getSeries();
                    if (abstractSeries) {
                        abstractSeries->setVisible(visible);
                    }
                }

                // 更新全局范围
                UpdateGlobalRange();

                // 对应曲线上的峰位也要相应的设置visible
                for (int j = 0; j < targetData->peakVec.size(); j++) {
                    Peak &p = targetData->peakVec[j];
                    if (p.item) {
                        p.item->setVisible(visible);
                    }
                    if (p.shadeItem) {
                        p.shadeItem->setVisible(visible);
                    }
                    if (p.textItem) {
                        p.textItem->setVisible(visible);
                    }
                    if (p.lineItem) {
                        p.lineItem->setVisible(visible);
                    }
                }
                break;
            }
        } });
    // 将颜色存储到映射表中
    QString id = chartData->getUniqueID();
    m_colorMap[id] = colorHex;

    // 🎯 检查是否是MRM数据且正在批量加载
    if (chartData->isMrmData())
    {
        bool isBatchMode = false;
        {
            QMutexLocker locker(&m_massCountMutex);
            isBatchMode = m_isBatchLoading;
        }

        if (isBatchMode)
        {
            // qDebug() << "LxChart::AddLxChartData: 批量模式，延迟添加MASS数据，路径:" << chartData->getParamPath();
            onMassCompleted(chartData);
            return; // 批量模式下不立即添加到图表
        }
    }

    // 添加到管理数组
    m_chartDataVec.append(chartData);

    ui->btn_setBackgroundArea->setEnabled(true);

    // 🎯 关键修复：在创建系列之前设置MASS分组位置（主线程安全）
    if (chartData->isMrmData())
    {
        // 使用简单的互斥锁保护，防止快速连续调用导致的竞态
        QMutexLocker locker(&m_massPositionMutex);

        // 计算当前MASS应该的起始位置
        int currentXPosition = 1; // 从1开始

        // 遍历已有的MRM数据，计算累计位置
        for (LxChartData *existingData : m_chartDataVec)
        {
            if (existingData != chartData && existingData->isMrmData())
            {
                currentXPosition += existingData->getMassBarCount();
            }
        }

        // 设置当前MASS的起始位置
        chartData->setMassStartPosition(currentXPosition);

        // 计算当前MASS的棒子数量
        QStringList categories;
        QVector<QPointF> tempDataPoints = chartData->getData();
        for (const QPointF &point : tempDataPoints)
        {
            if (!std::isnan(point.x()) && !std::isinf(point.x()) && !std::isnan(point.y()) && !std::isinf(point.y()))
            {
                categories << QString::number(point.x(), 'f', 1);
            }
        }
        chartData->setMassBarCount(categories.size());

        // qDebug() << "LxChart::AddLxChartData: 设置MASS分组位置，路径:" << chartData->getParamPath() << "，事件ID:" << chartData->getEventNum()
        //          << "，起始位置:" << currentXPosition << "，棒子数量:" << categories.size() << "，线程:" << QThread::currentThread();
    }

    // 获取数据点
    QVector<QPointF> dataPoints = chartData->getData();
    // qDebug() << "LxChart::AddLxChartData: 数据点数量:" << dataPoints.size();
    // if (!dataPoints.isEmpty())
    // {
    //     qDebug() << "LxChart::AddLxChartData: 第一个数据点:" << dataPoints.first();
    //     if (dataPoints.size() > 1)
    //     {
    //         qDebug() << "LxChart::AddLxChartData: 最后一个数据点:" << dataPoints.last();
    //     }
    // }

    // 使用ChartData中的QAbstractSeries指针
    QString seriesName = QString(chartData->getUniqueID());
    // qDebug() << "titie:" << seriesName;

    QAbstractSeries *series = chartData->getSeries();
    if (!series)
    {
        // 如果ChartData中没有图表系列，创建新的
        series = chartData->createSeries(this, seriesName);
        // qDebug() << "创建新的图表系列:" << seriesName;
    }
    else
    {
        // qDebug() << "使用现有的QLineSeries:" << seriesName;
    }

    // 根据系列类型设置样式和数据
    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(series);
    if (lineSeries)
    {
        // 设置连线图的颜色和线宽
        double currentLineWidth = OptionsDialogSettings::getSpectrumLineWidth();
        QPen pen(chartData->getLineColor(), currentLineWidth);
        lineSeries->setPen(pen);
        chartData->setDefaultPen(pen);

        // 检查是否是MRM数据（垂直线条显示）
        if (chartData->isMrmData() && chartData->hasMrmCategories())
        {
            // qDebug() << "LxChart::AddLxChartData: MRM数据显示为垂直线条，数据点数:" << dataPoints.size();

            // 确保Y轴从0开始显示
            if (m_globalMinY > 0)
            {
                m_globalMinY = 0;
                // qDebug() << "LxChart::AddLxChartData: 调整MRM数据Y轴最小值为0";
            }

            // MRM数据已在createSeries中转换为垂直线条，标签将在全局范围更新后添加
            // qDebug() << "LxChart::AddLxChartData: MRM数据标签将在全局范围更新后添加";
        }
        else
        {
            // 普通数据：处理单点数据的特殊情况
            if (dataPoints.size() == 1)
            {
                QPointF singlePoint = dataPoints.first();
                qreal x = singlePoint.x();
                qreal y = singlePoint.y();

                qDebug() << "LxChart::AddLxChartData: 检测到单点数据，将显示为垂直线，位置: (" << x << "," << y << ")";

                // 🎯 修复：检查X坐标的有效性
                if (std::isnan(x) || std::isinf(x))
                {
                    qDebug() << "LxChart::AddLxChartData: ❌ 单点数据X坐标无效，跳过显示";
                    return;
                }

                // 创建垂直线：从Y=0到实际Y值
                dataPoints.clear();
                dataPoints.append(QPointF(x, 0)); // 底部点
                dataPoints.append(QPointF(x, y)); // 顶部点（实际数据点）

                // 设置线条样式为较粗的实线，便于观察
                QPen thickPen(chartData->getLineColor(), currentLineWidth * 2);
                lineSeries->setPen(thickPen);
                chartData->setDefaultPen(thickPen);
            }

            // 设置连线图数据（对于非MRM数据）
            if (!chartData->isMrmData())
            {
                lineSeries->replace(dataPoints);
            }
        }
    }

    // 2. 批量设置图表属性
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);

    // 3. 暂时禁用动画以提高性能
    bool animationEnabled = m_chart->animationOptions() != QChart::NoAnimation;
    if (animationEnabled)
    {
        m_chart->setAnimationOptions(QChart::NoAnimation);
    }

    // 4. 添加到图表并连接到坐标轴
    // 🎯 检查是否是MRM数据，如果是则添加所有棒子系列
    if (chartData->isMrmData() && !chartData->getMrmBarSeries().isEmpty())
    {
        // 🎯 获取统一的颜色（基于主系列的颜色）
        QColor massColor = Qt::blue; // 默认颜色
        if (QLineSeries *lineSeries = qobject_cast<QLineSeries *>(series))
        {
            massColor = lineSeries->color();
        }

        // MRM数据：添加所有棒子系列并设置统一颜色
        for (QAbstractSeries *barSeries : chartData->getMrmBarSeries())
        {
            m_chart->addSeries(barSeries);
            barSeries->attachAxis(axisX);
            barSeries->attachAxis(axisY);

            // 🎯 设置统一颜色
            if (QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries))
            {
                lineSeries->setColor(massColor);
            }
        }
        // qDebug() << "LxChart::AddLxChartData: 添加了" << chartData->getMrmBarSeries().size() << "个MRM棒子系列，统一颜色:" << massColor.name();
    }
    else
    {
        // 普通数据：添加单个系列
        m_chart->addSeries(series);
        series->attachAxis(axisX);
        series->attachAxis(axisY);
    }

    axisX->setTitleText(chartData->getXAxisLabel());
    axisY->setTitleText(chartData->getYAxisLabel());

    m_defaultSeriesName = chartData->getUniqueID();
    defaultSeries = series;

    // 5. 异步更新全局范围，避免阻塞
    QTimer::singleShot(0, this, [this, animationEnabled, chartData, dataPoints]()
                       {
        UpdateGlobalRange();

        // 🎯 为MRM数据添加智能顶点标签（在全局范围更新后）
        // 检查是否有任何MRM数据，如果有就创建标签
        bool hasMrmData = false;
        for (LxChartData *data : m_chartDataVec) {
            if (data && data->isMrmData()) {
                hasMrmData = true;
                break;
            }
        }

        if (hasMrmData) {
            qDebug() << "LxChart::AddLxChartData: 检测到MRM数据，标签将在UpdateGlobalRange中创建";
        } else {
            qDebug() << "LxChart::AddLxChartData: 没有检测到MRM数据";
        }

        // 恢复动画设置
        if (animationEnabled) {
            m_chart->setAnimationOptions(QChart::SeriesAnimations);
        }

        // 强制刷新图表
        m_chart->update();
        m_chartView->viewport()->update();

        // 🎯 更新自定义滑块的数据范围和位置
        updateDataRange();
        updateCustomSliderPosition();

        // 🎯 再次延迟更新，确保质谱图等复杂数据完全加载
        QTimer::singleShot(200, this, [this]() {
            updateDataRange();

            // 🎯 不自动修改阈值，保持用户设置的值

            updateCustomSliderPosition();

            // 🎯 只有在用户手动设置过阈值后才更新峰点可见性
            if (m_thresholdEnabled && m_thresholdManuallySet) {
                updatePeakVisibilityByThreshold();
                updateThresholdLinePosition();
                qDebug() << "LxChart::AddLxChartData二次延迟更新: 峰点可见性已更新，阈值保持=" << m_thresholdValue;
            }

            // 🎯 在延迟更新中不需要再次更新顶点标签，因为已经在主流程中创建了
            // updateVertexLabelsPosition();

            // qDebug() << "LxChart::AddLxChartData二次延迟更新: 滑块位置已更新";
        }); });

    // qDebug() << "添加曲线:" << seriesName << "，颜色:" << curveColor.name();
}

bool LxChart::isTrackTypeAllowed(GlobalEnums::TrackType dataType) const
{
    return allowedTrackTypes.contains(dataType);
}

// 更新全局最值范围
void LxChart::UpdateGlobalRange()
{
    if (m_chartDataVec.isEmpty())
    {
        return;
    }

    // 初始化最小最大值
    m_globalMinX = std::numeric_limits<qreal>::max();
    m_globalMaxX = std::numeric_limits<qreal>::lowest();
    m_globalMinY = std::numeric_limits<qreal>::max();
    m_globalMaxY = std::numeric_limits<qreal>::lowest();
    // 遍历所有曲线数据，找出整体的最小最大值
    for (int i = 0; i < m_chartDataVec.size(); i++)
    {
        const auto chartData = m_chartDataVec.at(i);

        // 检查图例是否存在，如果不存在（如XIC数据），则默认为可见
        bool isVisible = true;
        if (chartData->legend)
        {
            isVisible = chartData->legend->bool_curveVisible();
        }

        if (!isVisible)
        {
            continue;
        }

        // 检查数据范围是否有效（避免-1等无效值）
        if (!chartData->hasValidRange())
        {
            qDebug() << "LxChart::UpdateGlobalRange: 跳过无效范围的数据，UniqueID:" << chartData->getUniqueID() << "，范围: X[" << chartData->getMinX() << ","
                     << chartData->getMaxX() << "]"
                     << "，Y[" << chartData->getMinY() << "," << chartData->getMaxY() << "]";
            continue;
        }

        m_globalMinX = qMin(m_globalMinX, chartData->getMinX());
        m_globalMaxX = qMax(m_globalMaxX, chartData->getMaxX());
        m_globalMinY = qMin(m_globalMinY, chartData->getMinY());
        m_globalMaxY = qMax(m_globalMaxY, chartData->getMaxY());
    }

    // 检查是否有有效的全局范围
    if (m_globalMinX == std::numeric_limits<qreal>::max() || m_globalMaxX == std::numeric_limits<qreal>::lowest())
    {
        qDebug() << "LxChart::UpdateGlobalRange: 没有有效的数据范围，使用默认范围";
        m_globalMinX = 0;
        m_globalMaxX = 100;
        m_globalMinY = 0;
        m_globalMaxY = 100;
    }
    else
    {
        // 处理单点数据的情况（X轴或Y轴范围为0）
        if (m_globalMinX == m_globalMaxX)
        {
            // X轴单点数据，创建一个合理的显示范围
            qreal center = m_globalMinX;
            qreal range = qMax(1.0, qAbs(center * 0.1)); // 使用中心值的10%作为范围，最小为1
            m_globalMinX = center - range;
            m_globalMaxX = center + range;
            qDebug() << "LxChart::UpdateGlobalRange: 处理X轴单点数据，中心值:" << center << "，扩展范围: [" << m_globalMinX << "," << m_globalMaxX << "]";
        }

        if (m_globalMinY == m_globalMaxY)
        {
            // Y轴单点数据，为垂直线创建合适的显示范围
            qreal maxValue = m_globalMinY;
            if (maxValue > 0)
            {
                // 从0开始显示到数据点值的1.1倍，确保垂直线完全可见
                m_globalMinY = 0;
                m_globalMaxY = maxValue * 1.1;
                qDebug() << "LxChart::UpdateGlobalRange: 处理Y轴单点数据（垂直线），最大值:" << maxValue << "，显示范围: [" << m_globalMinY << ","
                         << m_globalMaxY << "]";
            }
            else
            {
                // 处理负值或零值的情况
                qreal range = qMax(1.0, qAbs(maxValue * 0.1));
                m_globalMinY = maxValue - range;
                m_globalMaxY = maxValue + range;
                qDebug() << "LxChart::UpdateGlobalRange: 处理Y轴单点数据（特殊值），中心值:" << maxValue << "，扩展范围: [" << m_globalMinY << ","
                         << m_globalMaxY << "]";
            }
        }
    }

    // 🎯 特殊处理MRM数据的X轴范围，让垂直线分组显示
    bool hasMrmData = false;
    QVector<qreal> mrmXValues;
    int totalBarCount = 0;

    // 🎯 重新分配MASS位置，确保删除后位置正确
    int currentXPosition = 1; // 从位置1开始

    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->isMrmData())
        {
            hasMrmData = true;

            // 从MRM类别标签获取m/z值
            QStringList mrmCategories = chartData->getMrmCategories();
            int massBarCount = chartData->getMassBarCount();

            // 🎯 重新分配起始位置
            int oldPosition = chartData->getMassStartPosition();
            chartData->setMassStartPosition(currentXPosition);

            qDebug() << "LxChart::UpdateGlobalRange: 收集MASS信息，路径:" << chartData->getParamPath() << "，事件ID:" << chartData->getEventNum()
                     << "，起始位置:" << currentXPosition << "，棒子数量:" << massBarCount;

            // 🎯 如果位置发生变化，需要重新创建系列
            if (oldPosition != currentXPosition && !chartData->getMrmBarSeries().isEmpty())
            {
                qDebug() << "LxChart::UpdateGlobalRange: 位置变化(" << oldPosition << "->" << currentXPosition << ")，重新创建系列";

                try
                {
                    // 先删除旧的系列
                    for (QAbstractSeries *barSeries : chartData->getMrmBarSeries())
                    {
                        if (barSeries && m_chart)
                        {
                            m_chart->removeSeries(barSeries);
                        }
                    }

                    // 清除旧的系列数据
                    chartData->clearMrmBarSeries();

                    // 🎯 验证chartData有效性后重新创建系列
                    if (chartData && !chartData->getUniqueID().isEmpty())
                    {
                        QAbstractSeries *newSeries = chartData->createSeries(this, chartData->getUniqueID());
                        if (!newSeries)
                        {
                            qDebug() << "LxChart::UpdateGlobalRange: 重新创建系列失败";
                            continue;
                        }
                    }
                    else
                    {
                        qDebug() << "LxChart::UpdateGlobalRange: chartData无效，跳过重新创建";
                        continue;
                    }
                }
                catch (const std::exception &e)
                {
                    qDebug() << "LxChart::UpdateGlobalRange: 重新创建系列时发生异常:" << e.what();
                    continue;
                }
                catch (...)
                {
                    qDebug() << "LxChart::UpdateGlobalRange: 重新创建系列时发生未知异常";
                    continue;
                }

                // 🎯 重新添加系列到图表，增加保护
                try
                {
                    QColor massColor = Qt::blue; // 默认颜色
                    if (QLineSeries *lineSeries = qobject_cast<QLineSeries *>(chartData->getSeries()))
                    {
                        massColor = lineSeries->color();
                    }

                    // 🎯 验证坐标轴存在
                    if (!m_chart || m_chart->axes(Qt::Horizontal).isEmpty() || m_chart->axes(Qt::Vertical).isEmpty())
                    {
                        qDebug() << "LxChart::UpdateGlobalRange: 坐标轴不存在，跳过系列添加";
                        continue;
                    }

                    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).first();
                    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).first();

                    if (!axisX || !axisY)
                    {
                        qDebug() << "LxChart::UpdateGlobalRange: 坐标轴为空，跳过系列添加";
                        continue;
                    }

                    for (QAbstractSeries *barSeries : chartData->getMrmBarSeries())
                    {
                        if (!barSeries)
                        {
                            qDebug() << "LxChart::UpdateGlobalRange: 棒子系列为空，跳过";
                            continue;
                        }

                        m_chart->addSeries(barSeries);
                        barSeries->attachAxis(axisX);
                        barSeries->attachAxis(axisY);

                        // 设置统一颜色
                        if (QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries))
                        {
                            lineSeries->setColor(massColor);
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    qDebug() << "LxChart::UpdateGlobalRange: 添加系列到图表时发生异常:" << e.what();
                    continue;
                }
                catch (...)
                {
                    qDebug() << "LxChart::UpdateGlobalRange: 添加系列到图表时发生未知异常";
                    continue;
                }
            }

            // 收集所有m/z值（用于调试）
            for (const QString &mzStr : mrmCategories)
            {
                bool ok;
                qreal mzValue = mzStr.toDouble(&ok);
                if (ok && !mrmXValues.contains(mzValue))
                {
                    mrmXValues.append(mzValue);
                }
            }

            // 累计总棒子数并更新下一个起始位置
            totalBarCount += massBarCount;
            currentXPosition += massBarCount;
        }
    }

    // 如果只有MRM数据，使用分组后的垂直线条X轴范围
    if (hasMrmData && totalBarCount > 0)
    {
        std::sort(mrmXValues.begin(), mrmXValues.end());

        // 🎯 垂直线条的X轴范围：棒子数量+2，首尾留空不显示棒子
        m_globalMinX = 0;
        m_globalMaxX = totalBarCount + 1;

        // 🎯 MRM数据强制Y轴从0开始，确保最小的垂直线可见
        if (m_globalMinY > 0)
        {
            qDebug() << "LxChart::UpdateGlobalRange: MRM数据强制Y轴从0开始，原始范围: [" << m_globalMinY << "," << m_globalMaxY << "]";
            m_globalMinY = 0;
        }

        // qDebug() << "LxChart::UpdateGlobalRange: MRM数据使用分组垂直线条坐标系统";
        // qDebug() << "  总棒子数:" << totalBarCount;
        // qDebug() << "  X轴范围: [" << m_globalMinX << "," << m_globalMaxX << "]";
        // qDebug() << "  Y轴范围: [" << m_globalMinY << "," << m_globalMaxY << "]";
        // qDebug() << "  m/z值范围: [" << (mrmXValues.isEmpty() ? 0 : mrmXValues.first()) << "," << (mrmXValues.isEmpty() ? 0 : mrmXValues.last()) << "]";

        // 保存m/z值用于X轴标签和双击处理
        m_mrmMzValues = mrmXValues;
    }

    // qDebug() << "LxChart::UpdateGlobalRange: 最终全局范围: X[" << m_globalMinX << "," << m_globalMaxX << "] Y[" << m_globalMinY << "," << m_globalMaxY <<
    // "]";

    // 检查chartView是否有效
    if (!m_chartView)
    {
        qDebug() << "LxChart::UpdateGlobalRange: m_chartView为空，跳过视图更新";
        return;
    }

    if (m_isPercentMode)
    {
        // 计算百分比模式所需的参数
        m_chartView->diff = m_globalMaxY - m_globalMinY;
        m_chartView->minY = m_globalMinY;
        m_chartView->m_bool_enableUpdate = true;
        m_chartView->viewport()->update();

        // qDebug() << "更新百分比轴" << __FUNCTION__;
    }

    m_chartView->minY = m_globalMinY;
    m_chartView->diff = m_globalMaxY - m_globalMinY;

    // 重新计算坐标轴范围
    SetAxisScale();

    // 🎯 在所有坐标轴调整后更新自定义标签位置，增加保护
    try
    {
        refreashLabelWidgetPos();
    }
    catch (const std::exception &e)
    {
        qDebug() << "LxChart::UpdateGlobalRange: 刷新标签位置时发生异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "LxChart::UpdateGlobalRange: 刷新标签位置时发生未知异常";
    }

    // 🎯 MRM标签创建已移到批量显示完成后，这里不再创建
    // 避免重复创建标签
    if (hasMrmData)
    {
        // qDebug() << "LxChart::UpdateGlobalRange: MRM数据检测完成，标签将在批量显示后创建";
    }
}

// 通过曲线唯一ID移除管理数组的曲线
bool LxChart::RemoveLxChartDataByUniqueID(QString UniqueID)
{
    // 查找对应的LxChartData
    for (int i = 0; i < m_chartDataVec.size(); ++i)
    {
        if (m_chartDataVec[i]->getUniqueID() == UniqueID)
        {
            LxChartData *chartData = m_chartDataVec[i];

            // 检查是否是平均质谱MASS，如果是则需要删除对应的自定义区域
            if (chartData->getTrackType() == GlobalEnums::TrackType::MS)
            {
                // 检查是否是平均质谱MASS（通过虚拟路径判断）
                QString paramPath = chartData->getParamPath();
                if (paramPath.startsWith("VIRTUAL://AvgMass_"))
                {
                    // 平均质谱使用事件ID来标识对应的自定义区域索引
                    int customRangeIndex = chartData->getEventNum();

                    if (customRangeIndex >= 0 && customRangeIndex < vecCustomRange.size())
                    {
                        qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 检测到平均质谱MASS删除，对应自定义区域索引:" << customRangeIndex;

                        // 清理自定义区域中的平均质谱引用
                        GlobalDefine::CustomRange &customRange = vecCustomRange[customRangeIndex];
                        customRange.avgMassData = nullptr;
                        customRange.hasAvgMass = false;
                        customRange.avgMassUniqueId.clear();

                        // 删除自定义区域
                        clearCustomRangeSelection(customRangeIndex);

                        qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 已删除对应的自定义区域，索引:" << customRangeIndex;
                    }
                }
            }

            // 实现级联删除逻辑
            if (chartData->getTrackType() == GlobalEnums::TrackType::TIC)
            {
                // TIC删除时，级联删除所有关联的XIC和MASS及其LxChartLegend
                TicChartData *ticData = qobject_cast<TicChartData *>(chartData);
                if (ticData)
                {
                    qDebug() << "LxChart::RemoveLxChartDataByUniqueID: TIC删除，开始级联删除关联的XIC和MASS";

                    // 删除所有关联的XIC数据及其Legend
                    QVector<XicChartData *> xicList = ticData->getXicDataList();
                    for (XicChartData *xicData : xicList)
                    {
                        if (xicData && xicData->legend)
                        {
                            qDebug() << "级联删除XIC Legend，UniqueID:" << xicData->getUniqueID();
                            // 🎯 安全删除：removeLegendWidget会返回false如果控件不存在
                            if (!customLegendWidget->removeLegendWidget(xicData->getUniqueID()))
                            {
                                qDebug() << "XIC Legend已被删除，跳过:" << xicData->getUniqueID();
                            }
                        }
                    }

                    // 删除关联的MASS数据及其Legend
                    MassChartData *massData = ticData->getMassData();
                    if (massData && massData->legend)
                    {
                        qDebug() << "级联删除MASS Legend，UniqueID:" << massData->getUniqueID();
                        // 🎯 安全删除：removeLegendWidget会返回false如果控件不存在
                        if (!customLegendWidget->removeLegendWidget(massData->getUniqueID()))
                        {
                            qDebug() << "MASS Legend已被删除，跳过:" << massData->getUniqueID();
                        }
                    }

                    // 🎯 修复：发出TIC删除信号，让MainWindow删除massChart中的MASS数据
                    int eventId = ticData->getEventNum();
                    QString paramPath = ticData->getParamPath();
                    qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 发出TIC删除信号，事件ID:" << eventId << "，路径:" << paramPath;
                    emit ticDataRemoved(eventId, paramPath);
                }
            }
            if (!customLegendWidget->removeLegendWidget(UniqueID))
            {
                // 先移除图例
                qDebug() << m_chartDataVec[i]->legend->path() << "移除失败" << m_chartDataVec[i]->legend->getUniqueID();
                return false;
            }
            else
            {
                m_chartDataVec[i]->legend = nullptr;
            }
            if (lastSelectedChartData == m_chartDataVec[i])
            {
                // 如果要删除的曲线就是当前被选中的，则重置当前选中曲线
                lastSelectedChartData = nullptr;
            }
            // 找到对应的曲线并移除
            QString seriesName = QString(m_chartDataVec[i]->getUniqueID());

            // 清除该曲线的峰标记
            clearPeaksForChartData(m_chartDataVec[i]);

            // 🎯 如果是MRM数据，清除MRM标签（删除后会在UpdateGlobalRange中重新创建）
            if (m_chartDataVec[i]->isMrmData())
            {
                // qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 清除MRM标签";
                clearMrmLabels();
            }

            // 🎯 统一清理：从图表移除 + 删除对象（避免重复操作）
            QList<QAbstractSeries *> allSeries = m_chartDataVec[i]->getAllSeries();
            // qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 清理" << allSeries.size() << "个系列";

            // 先从图表中移除所有系列
            for (QAbstractSeries *series : allSeries)
            {
                if (series && m_chart->series().contains(series))
                {
                    m_chart->removeSeries(series);
                }
            }

            // 🎯 检查是否删除的是defaultSeries
            if (defaultSeries && allSeries.contains(defaultSeries))
            {
                defaultSeries = nullptr;
                // qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 默认系列已被删除，清空引用";
            }

            // 再删除所有系列对象
            m_chartDataVec[i]->clearAllSeries();

            // 删除关联的LxChartLegend（如果还没有被删除）
            if (m_chartDataVec[i]->legend)
            {
                qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 删除LxChartLegend";
                removeLegendWidget(UniqueID);
                m_chartDataVec[i]->legend = nullptr; // 设置为nullptr，避免重复删除
            }

            // 从颜色映射表中移除
            QString id = UniqueID;
            if (m_colorMap.contains(id))
            {
                // 将颜色存入freeColorStack
                freeColorStack.push(m_colorMap.value(id));
                m_colorMap.remove(id);
            }

            if (allowedTrackTypes.contains(GlobalEnums::TrackType::TIC))
            {
                const QString &deletePath = m_chartDataVec[i]->getParamPath();
                bool flag = true;
                // 检查当前是否还有被删除曲线同文件的数据，如果有则不释放资源，否则释放
                foreach (LxChartData *data, m_chartDataVec)
                {
                    if (data->getParamPath() == deletePath)
                    {
                        flag = false;
                        break;
                    }
                }

                // 暂存删除信息，稍后发射信号
                QString pendingDeletePath = deletePath;
                int pendingFlag = flag;

                // 从管理数组中移除
                m_chartDataVec.remove(i);

                // 完成所有图表操作后再发射删除信号
                // 使用QMetaObject::invokeMethod确保安全的延迟调用
                QMetaObject::invokeMethod(
                    this,
                    [this, pendingDeletePath, pendingFlag]()
                    {
                        qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 延迟发射sg_removeData信号";
                        emit sg_removeData(pendingDeletePath, pendingFlag);
                    },
                    Qt::QueuedConnection);
            }
            else
            {
                // 从管理数组中移除
                m_chartDataVec.remove(i);
            }

            if (m_chartDataVec.isEmpty())
            {
                if (customLegendWidget->isVisible())
                {
                    customLegendWidget->setVisible(false);
                }
                ui->btn_setBackgroundArea->setEnabled(false);

                m_defaultSeriesName.clear();
                // 曲线清空后清除区域选择，但不清除背景区域
                // 背景区域是在TIC图表中设置的，与MASS图表无关
                clearRegionSelection();
                // 注意：不清除背景区域！背景区域应该保持持久性，
                // 只有用户主动删除或TIC图表关闭时才应该清除
                // clearBackgroundAreaSelection();
                // initDefaultSeries();
            }
            else
            {
                m_defaultSeriesName = m_chartDataVec.last()->getUniqueID();

                // 安全检查：确保series列表不为空
                if (!m_chart->series().isEmpty())
                {
                    defaultSeries = static_cast<QLineSeries *>(m_chart->series().last());
                }
                else
                {
                    defaultSeries = nullptr;
                    qDebug() << "LxChart::RemoveLxChartDataByUniqueID: 警告：图表series列表为空";
                }
            }
            // 更新全局最值
            UpdateGlobalRange();

            // qDebug() << "移除曲线，UniqueID:" << UniqueID << "，名称:" << seriesName;

            return true;
        }
    }

    qDebug() << "未找到UniqueID为" << UniqueID << "的曲线";
    return false;
}

void LxChart::removeLegendWidget(const QString &UniqueID)
{
    if (customLegendWidget)
    {
        customLegendWidget->removeLegendWidget(UniqueID);
        qDebug() << "LxChart::removeLegendWidget: 删除LxChartLegend，UniqueID:" << UniqueID;
    }
    else
    {
        qDebug() << "LxChart::removeLegendWidget: customLegendWidget为空，无法删除Legend";
    }
}

// 删除全部曲线
void LxChart::ClearAllLxChartData()
{
    // 移除所有曲线
    m_chart->removeAllSeries();

    // 清空管理数组
    m_chartDataVec.clear();

    // 将所有颜色保存到freeColorStack中
    freeColorStack.clear();
    for (auto it = m_colorMap.begin(); it != m_colorMap.end(); ++it)
    {
        freeColorStack.push(it.value());
    }

    // 清空颜色映射表
    m_colorMap.clear();

    // 清除所有自定义区域
    clearCustomRangeSelection(-1);

    m_defaultSeriesName.clear();
    qDebug() << "清除所有曲线和颜色映射";

    // initDefaultSeries();
}

// 设置坐标轴范围
void LxChart::SetAxisScale()
{
    if (m_chartDataVec.isEmpty())
    {
        qDebug() << "LxChart::SetAxisScale: 图表数据为空，跳过坐标轴设置";
        return;
    }

    // 检查坐标轴是否存在
    if (m_chart->axes(Qt::Horizontal).isEmpty() || m_chart->axes(Qt::Vertical).isEmpty())
    {
        qDebug() << "LxChart::SetAxisScale: 坐标轴未初始化，跳过坐标轴设置";
        return;
    }

    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);

    if (!axisX || !axisY)
    {
        qDebug() << "LxChart::SetAxisScale: 坐标轴指针为空，跳过坐标轴设置";
        return;
    }

    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);

    if (!valueAxisX || !valueAxisY)
    {
        qDebug() << "LxChart::SetAxisScale: 坐标轴类型转换失败，跳过坐标轴设置";
        return;
    }
    // 设置坐标轴范围
    // qDebug() << "LxChart::SetAxisScale: 设置轴范围: X[" << m_globalMinX << "," << m_globalMaxX << "] Y[" << m_globalMinY << "," << m_globalMaxY << "]";
    valueAxisX->setRange(m_globalMinX, m_globalMaxX);
    valueAxisY->setRange(m_globalMinY, m_globalMaxY);

    // 🎯 检查是否有MRM数据，为MRM数据设置特殊的X轴显示，增加保护
    if (!m_mrmMzValues.isEmpty())
    {
        try
        {
            setupMrmXAxisLabels(valueAxisX, m_mrmMzValues);
        }
        catch (const std::exception &e)
        {
            qDebug() << "LxChart::SetAxisScale: 设置MRM X轴标签时发生异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "LxChart::SetAxisScale: 设置MRM X轴标签时发生未知异常";
        }
    }

    // 设置Y轴格式和标题
    // qDebug() << "设置Y轴格式和标题";
    valueAxisY->setLabelFormat("%g");

    if (m_isPercentMode)
    {
        // 计算百分比模式所需的参数
        m_chartView->diff = m_globalMaxY - m_globalMinY;
        m_chartView->minY = m_globalMinY;
        m_chartView->m_bool_enableUpdate = true;
        m_chartView->viewport()->update();
        valueAxisY->setTitleText("相对强度%");
    }
    else
    {
        valueAxisY->setTitleText("强度");
    }
    // qDebug() << "Y轴设置完成";

    // 如果存在背景区域，需要重新绘制背景区域以适应新的坐标轴范围
    if (m_bgRect && m_backgroundAreaRange.first != m_backgroundAreaRange.second)
    {
        // 检查图表是否有数据系列
        if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
        {
            qDebug() << "SetAxisScale中重绘背景区域失败：图表无数据系列";
            return;
        }

        // 检查默认曲线是否存在
        if (!defaultSeries)
        {
            defaultSeries = getDefaultSeries();
            if (!defaultSeries)
            {
                qDebug() << "SetAxisScale中重绘背景区域失败：defaultSeries不存在";
                return;
            }
        }

        // 确保背景区域范围在当前坐标轴范围内
        qreal minX = valueAxisX->min();
        qreal maxX = valueAxisX->max();
        qreal startX = qMax(m_backgroundAreaRange.first, minX);
        qreal endX = qMin(m_backgroundAreaRange.second, maxX);

        qDebug() << "SetAxisScale中重绘背景区域：" << startX << "到" << endX << "（原始:" << m_backgroundAreaRange.first << "到" << m_backgroundAreaRange.second
                 << ")";

        // 根据原始范围更新背景区域的显示，但不更改保存的原始范围
        if (startX < endX)
        { // 确保范围有效
            updateTempBackgroundArea();
        }
    }

    // 更新自定义区域以适应新的坐标轴范围
    if (!vecCustomRange.isEmpty())
    {
        updateCustomArea();
    }

    // 🎯 坐标轴范围设置后更新自定义滑块位置，确保滑块始终对齐阈值线
    updateDataRange(); // 坐标轴变化需要重新计算数据范围
    updateCustomSliderPosition();

    // qDebug() << "LxChart::SetAxisScale: 坐标轴设置完成，滑块位置已更新";
}

// 获取图表截图
QPixmap LxChart::getPixmap()
{
    return m_chartView->grab();
}

// 设置最大LxChartData数量
void LxChart::setMaxChartDataCount(int maxCount)
{
    // 如果设置为负值，表示不限制数量
    if (maxCount < 0)
    {
        m_maxChartDataCount = -1;
        qDebug() << "LxChart设置为不限制曲线数量";
    }
    else
    {
        m_maxChartDataCount = maxCount;
        qDebug() << "LxChart设置最大曲线数量限制为:" << maxCount;

        // 如果当前数量超过限制，移除多余的曲线
        if (m_chartDataVec.size() > m_maxChartDataCount)
        {
            int excessCount = m_chartDataVec.size() - m_maxChartDataCount;
            qDebug() << "当前曲线数量" << m_chartDataVec.size() << "超过限制" << m_maxChartDataCount << "，移除最早添加的" << excessCount << "条曲线";

            // 从最早添加的开始移除，直到满足限制
            while (m_chartDataVec.size() > m_maxChartDataCount)
            {
                // 获取最早添加的曲线的UniqueID
                QString oldestID = m_chartDataVec.first()->getUniqueID();
                // 移除该曲线
                RemoveLxChartDataByUniqueID(oldestID);
            }
        }
    }
}

/**
 * @brief 生成随机颜色，仅随机绿色和蓝色分量
 * @return 随机颜色
 */
QColor LxChart::generateRandomColor()
{
    // 生成随机的绿色和蓝色分量
    int g = QRandomGenerator::global()->bounded(0, 256);
    int b = QRandomGenerator::global()->bounded(0, 256);

    // 创建颜色，红色分量为0，避免与红色混淆
    QColor color(0, g, b);

    // 将颜色转换为十六进制格式
    QString hexColor = color.name();

    // 检查是否已经存在相同的颜色
    bool colorExists = m_colorMap.values().contains(hexColor);

    // 如果颜色已存在，重新生成
    while (colorExists)
    {
        g = QRandomGenerator::global()->bounded(0, 256);
        b = QRandomGenerator::global()->bounded(0, 256);
        color = QColor(0, g, b);
        hexColor = color.name();
        colorExists = m_colorMap.values().contains(hexColor);
    }

    return color;
}

/**
 * @brief 获取曲线的原始颜色
 * @param id 曲线唯一标识
 * @return 原始颜色
 */
QColor LxChart::getOriginalColor(const QString &id)
{
    if (m_colorMap.contains(id))
    {
        return QColor(m_colorMap.value(id));
    }

    return QColor(0, 0, 0); // 默认返回黑色
}

/**
 * @brief 设置曲线为高亮粗细（悬停时变粗）
 * @param chartData 要高亮的曲线数据
 */
void LxChart::setHighlightWidth(LxChartData *chartData)
{
    if (!chartData)
        return;

    // 更新曲线粗细
    for (int i = 0; i < m_chart->series().size(); i++)
    {
        QAbstractSeries *abstractSeries = m_chart->series().at(i);
        if (abstractSeries->name() == chartData->getUniqueID())
        {
            QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
            if (lineSeries)
            {
                QPen pen = lineSeries->pen();
                pen.setWidth(pen.width() + GlobalDefine::WIDTH_INCREMENT);
                lineSeries->setPen(pen);
            }
            break;
        }
    }

    // 更新图表
    m_chart->update();
}

void LxChart::setSelectColor(LxChartData *chartData)
{
    if (!chartData)
        return;

    LxChartData *previousSelected = lastSelectedChartData;

    if (lastSelectedChartData)
    {
        // 先恢复上一个选中曲线的颜色
        QPen defaultPen = lastSelectedChartData->getDefaultPen();
        QColor originalColor = lastSelectedChartData->getOriginalColor();
        defaultPen.setColor(originalColor); // 确保使用原始颜色

        // 🎯 如果是MRM数据，恢复所有棒子系列的颜色
        if (lastSelectedChartData->isMrmData() && !lastSelectedChartData->getMrmBarSeries().isEmpty())
        {
            for (QAbstractSeries *barSeries : lastSelectedChartData->getMrmBarSeries())
            {
                QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries);
                if (lineSeries)
                {
                    lineSeries->setPen(defaultPen);
                    lineSeries->setColor(originalColor); // 确保颜色正确
                }
            }
            qDebug() << "LxChart::setSelectColor: 恢复MRM数据原始颜色:" << originalColor.name()
                     << "，系列数量:" << lastSelectedChartData->getMrmBarSeries().size();
        }
        else
        {
            // 普通数据：恢复单个系列
            for (int i = 0; i < m_chart->series().size(); i++)
            {
                QAbstractSeries *abstractSeries = m_chart->series().at(i);
                if (abstractSeries->name() == lastSelectedChartData->getUniqueID())
                {
                    QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                    if (lineSeries)
                    {
                        lineSeries->setPen(defaultPen);
                        lineSeries->setColor(originalColor); // 确保颜色正确
                    }
                    break;
                }
            }
        }

        // 通知子类取消之前选中的图例
        syncLegendSelection(lastSelectedChartData, false);
    }

    if (lastSelectedChartData == chartData)
    {
        // 如果当前选中的曲线就是之前选中的，则表示取消选中，不再重复点亮
        lastSelectedChartData = nullptr;
        return;
    }

    lastSelectedChartData = chartData;

    qDebug() << "选中曲线，UniqueID:" << lastSelectedChartData->getUniqueID();

    // 更新曲线颜色为选中颜色（红色）
    // 🎯 如果是MRM数据，设置所有棒子系列为选中颜色
    if (chartData->isMrmData() && !chartData->getMrmBarSeries().isEmpty())
    {
        for (QAbstractSeries *barSeries : chartData->getMrmBarSeries())
        {
            QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries);
            if (lineSeries)
            {
                QPen pen = lineSeries->pen();
                pen.setColor(selectCurveColor);
                lineSeries->setPen(pen);
            }
        }
        qDebug() << "LxChart::setSelectColor: 设置MRM数据选中颜色，系列数量:" << chartData->getMrmBarSeries().size();
    }
    else
    {
        // 普通数据：设置单个系列
        for (int i = 0; i < m_chart->series().size(); i++)
        {
            QAbstractSeries *abstractSeries = m_chart->series().at(i);
            if (abstractSeries->name() == chartData->getUniqueID())
            {
                QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
                if (lineSeries)
                {
                    QPen pen = lineSeries->pen();
                    pen.setColor(selectCurveColor);
                    lineSeries->setPen(pen);
                }
                break;
            }
        }
    }

    // 通知子类同步图例选中状态
    syncLegendSelection(chartData, true);

    // 更新图表
    m_chart->update();
}

/**
 * @brief 恢复曲线的原始颜色和粗细
 * @param chartData 要恢复样式的曲线数据
 */
void LxChart::restoreOriginalStyle(LxChartData *chartData)
{
    if (!chartData)
        return;

    // 恢复原始颜色
    QColor originalColor = chartData->getOriginalColor();
    chartData->setLineColor(originalColor);

    // 更新曲线颜色和粗细
    for (int i = 0; i < m_chart->series().size(); i++)
    {
        QAbstractSeries *abstractSeries = m_chart->series().at(i);
        if (abstractSeries->name() == chartData->getUniqueID())
        {
            QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
            if (lineSeries)
            {
                lineSeries->setPen(chartData->getDefaultPen());
            }
            break;
        }
    }

    // 更新图表
    m_chart->update();
}

/**
 * @brief 恢复曲线的原始粗细（悬停离开时）
 * @param chartData 要恢复粗细的曲线数据
 */
void LxChart::restoreOriginalWidth(LxChartData *chartData)
{
    if (!chartData)
        return;

    // 更新曲线粗细
    for (int i = 0; i < m_chart->series().size(); i++)
    {
        QAbstractSeries *abstractSeries = m_chart->series().at(i);
        if (abstractSeries->name() == chartData->getUniqueID())
        {
            QLineSeries *lineSeries = qobject_cast<QLineSeries *>(abstractSeries);
            if (lineSeries)
            {
                QPen pen = lineSeries->pen();
                // 恢复到原始粗细，但保持当前颜色
                pen.setWidth(chartData->getDefaultPen().width());
                lineSeries->setPen(pen);
            }
            // 对于棒状图，不需要处理线宽
            break;
        }
    }

    // 更新图表
    m_chart->update();
}

/**
 * @brief 处理图例点击事件（基类默认实现）
 * @param uniqueId 被点击的图例对应的曲线唯一ID
 */
void LxChart::handleLegendClicked(const QString &uniqueId)
{
    qDebug() << "LxChart::handleLegendClicked: 图例被点击，UniqueID:" << uniqueId;

    // 基类默认实现：选中对应的曲线
    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->getUniqueID() == uniqueId)
        {
            setSelectColor(chartData);
            break;
        }
    }
}

/**
 * @brief 同步图例选中状态（基类默认实现为空）
 * @param chartData 曲线数据
 * @param isSelected 是否选中
 */
void LxChart::syncLegendSelection(LxChartData *chartData, bool isSelected)
{
    // 基类默认实现为空，子类可以重写此方法来实现图例同步
    Q_UNUSED(chartData);
    Q_UNUSED(isSelected);
}

/**
 * @brief 向前浏览（基类默认实现为空）
 */
void LxChart::browsePrevious()
{
    // 基类默认实现为空，子类可以重写此方法来实现移动浏览
    // qDebug() << "=== LxChart::browsePrevious: 基类默认实现被调用，子类应重写此方法 ===";
}

/**
 * @brief 向后浏览（基类默认实现为空）
 */
void LxChart::browseNext()
{
    // 基类默认实现为空，子类可以重写此方法来实现移动浏览
    // qDebug() << "=== LxChart::browseNext: 基类默认实现被调用，子类应重写此方法 ===";
}

QPointF LxChart::convertToScreenPixel(const QPointF &point)
{
    QPointF destPoint = m_chartView->mapToGlobal(m_chartView->mapFromScene(m_chart->mapToPosition(point)));

    return destPoint;
}

bool LxChart::isPointNearLine(const QPointF &point, const QVector<QPointF> &vecPoint, double threadShold, int PcIndex)
{
    // 获取目标点的屏幕像素坐标
    QPointF screenPoint = convertToScreenPixel(point);
    double Px = screenPoint.x();

    // 计算像素区间
    double leftBound = Px - threadShold;
    double rightBound = Px + threadShold;

    // 获取最近点的屏幕像素坐标
    QPointF nearestPoint = convertToScreenPixel(vecPoint[PcIndex]);
    double PcX = nearestPoint.x();

    // 用于存储待测点
    QVector<QPointF> vecWaitPoint;

    // 判断最近点的像素坐标是否在区间内
    if (PcX >= leftBound && PcX <= rightBound)
    {
        int leftIndex = PcIndex;
        int rightIndex = PcIndex;
        vecWaitPoint.append(vecPoint[PcIndex]); // 添加最近点
        // 如果在区间内，直接从vecPoint中查找
        // 扩展左右各点
        if (PcIndex > 0)
        {
            // 一直向左侧扩展区间内的点
            while (leftIndex > 0)
            {
                QPointF pLeft = vecPoint[leftIndex - 1];
                leftIndex--;
                double pix = convertToScreenPixel(pLeft).x();
                vecWaitPoint.append(pLeft);
                if (!(pix >= leftBound && pix <= rightBound))
                {
                    // 如果这个点超出区间则停止循环，这个点还是要添加进去
                    break;
                }
            }
        }
        if (PcIndex < vecPoint.size() - 1)
        {
            // 一直向左侧扩展区间内的点
            while (rightIndex < vecPoint.size() - 1)
            {
                QPointF pRight = vecPoint[rightIndex + 1];
                rightIndex++;
                double pix = convertToScreenPixel(pRight).x();
                vecWaitPoint.append(pRight);
                if (!(pix >= leftBound && pix <= rightBound))
                {
                    // 如果这个点超出区间则停止循环，这个点还是要添加进去
                    break;
                }
            }
        }
    }
    else
    {
        // qDebug() << "不在区间内";

        // 如果不在区间内，判断最近点在目标点左侧还是右侧
        if (PcX < Px)
        {
            // 最近点在目标点左侧，从右边获取下一个点
            if (PcIndex < vecPoint.size() - 1)
            {
                vecWaitPoint.append(vecPoint[PcIndex]);     // 添加最近点
                vecWaitPoint.append(vecPoint[PcIndex + 1]); // 添加右边界点
            }
        }
        else
        {
            // 最近点在目标点右侧，从左边获取下一个点
            if (PcIndex > 0)
            {
                vecWaitPoint.append(vecPoint[PcIndex - 1]); // 添加左边界点
                vecWaitPoint.append(vecPoint[PcIndex]);     // 添加最近点
            }
        }
    }

    // 检查 vecWaitPoint 中的点是否满足像素距离要求
    for (const QPointF &p : vecWaitPoint)
    {
        QPointF pixelPoint = convertToScreenPixel(p);
        if (QLineF(pixelPoint, screenPoint).length() <= threadShold)
        {
            // qDebug() << "找到点:" << p;
            // highlightPoint(p);
            return true; // 找到符合要求的点
        }
    }

    // 组合线段并检查距离
    for (int i = 0; i < vecWaitPoint.size() - 1; ++i)
    {
        QLineF lineSegment(vecWaitPoint[i], vecWaitPoint[i + 1]);
        QPointF lineStart = convertToScreenPixel(lineSegment.p1());
        QPointF lineEnd = convertToScreenPixel(lineSegment.p2());
        QLineF line(lineStart, lineEnd);

        double dest = pointToSegmentDistance(screenPoint, line);
        if (dest <= threadShold)
        {
            // qDebug() << "找到线段了" << vecWaitPoint[i] << vecWaitPoint[i + 1];
            return true;
        }

        continue;
        // 计算线段的方向向量
        QPointF lineVector = lineEnd - lineStart;
        double lineLengthSquared = lineVector.x() * lineVector.x() + lineVector.y() * lineVector.y();

        // 计算投影系数
        double t = ((screenPoint.x() - lineStart.x()) * lineVector.x() + (screenPoint.y() - lineStart.y()) * lineVector.y()) / lineLengthSquared;

        // 限制投影系数在[0, 1]范围内
        t = std::max(0.0, std::min(1.0, t));

        // 计算投影点
        QPointF projectedPoint = lineStart + t * lineVector;

        // 检查投影点到目标点的距离
        if (QLineF(projectedPoint, screenPoint).length() <= threadShold)
        {
            // qDebug() << "找到点:" << lineSegment.p1() << lineSegment.p2();
            // highlightPoint(lineSegment.p1());
            return true; // 找到符合要求的线段
        }
    }

    return false; // 没有找到符合要求的点或线段
}
double LxChart::pointToSegmentDistance(const QPointF &p, const QLineF &line)
{
    QPointF a = line.p1();
    QPointF b = line.p2();

    double dx = b.x() - a.x();
    double dy = b.y() - a.y();

    if (dx == 0 && dy == 0)
    {
        // A 和 B 是同一个点
        return QLineF(p, a).length();
    }

    double t = ((p.x() - a.x()) * dx + (p.y() - a.y()) * dy) / (dx * dx + dy * dy);

    QPointF proj;
    if (t < 0.0)
    {
        proj = a;
    }
    else if (t > 1.0)
    {
        proj = b;
    }
    else
    {
        proj = QPointF(a.x() + t * dx, a.y() + t * dy);
    }

    return QLineF(p, proj).length();
}

// 新增: 检查是否在背景区域左边缘
bool LxChart::isNearBgLeftEdge(const QPointF &pos)
{
    if (!m_bgRect || !m_bgAreaDraggable)
        return false;

    // 如果存在左边界线，优先使用它来检测
    if (m_bgStartLine)
    {
        QLineF line = m_bgStartLine->line();
        // 计算点到直线的距离
        qreal distance = pointToSegmentDistance(pos, line);
        bool isNear = distance <= m_bgEdgeDetectionWidth;

        // 更新光标形状 - 确保立即设置
        if (isNear && m_chartView)
        {
            m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
        }

        return isNear;
    }
    else
    {
        // 使用矩形左边界
        QRectF rect = m_bgRect->rect();
        qreal left = rect.left();

        bool isNear = (qAbs(pos.x() - left) <= m_bgEdgeDetectionWidth);

        // 更新光标形状 - 确保立即设置
        if (isNear && m_chartView)
        {
            m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
        }

        return isNear;
    }
}

// 新增: 检查是否在背景区域右边缘
bool LxChart::isNearBgRightEdge(const QPointF &pos)
{
    if (!m_bgRect || !m_bgAreaDraggable)
        return false;

    // 如果存在右边界线，优先使用它来检测
    if (m_bgEndLine)
    {
        QLineF line = m_bgEndLine->line();
        // 计算点到直线的距离
        qreal distance = pointToSegmentDistance(pos, line);
        bool isNear = distance <= m_bgEdgeDetectionWidth;

        // 更新光标形状 - 确保立即设置
        if (isNear && m_chartView)
        {
            m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
        }

        return isNear;
    }
    else
    {
        // 使用矩形右边界
        QRectF rect = m_bgRect->rect();
        qreal right = rect.right();

        bool isNear = (qAbs(pos.x() - right) <= m_bgEdgeDetectionWidth);

        // 更新光标形状 - 确保立即设置
        if (isNear && m_chartView)
        {
            m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));
        }

        return isNear;
    }
}

// 新增: 处理背景区域边缘拖动
void LxChart::handleBgEdgeDragging(const QPointF &pos)
{
    if (!defaultSeries || !m_bgAreaDraggable)
        return;

    // 确保不显示十字准星
    if (m_showCrosshair)
    {
        clearCrosshair(false);
    }

    // 确保光标为双箭头形状
    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));

    QPointF chartPos = m_chart->mapToValue(pos, defaultSeries);

    // 获取X轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 确保拖动不超出坐标轴范围
    chartPos.setX(qMax(minX, qMin(chartPos.x(), maxX)));

    if (m_isDraggingBgLeft)
    {
        // 确保左边界不超过右边界
        chartPos.setX(qMin(chartPos.x(), m_tempBackgroundAreaRange.second));

        // 更新临时背景区域
        m_tempBackgroundAreaRange.first = chartPos.x();

        // 同时更新实际的背景区域范围
        m_backgroundAreaRange.first = m_tempBackgroundAreaRange.first;

        updateTempBackgroundArea();

        // 更新坐标气泡提示，显示当前拖动位置
        // 我们可以直接创建或更新坐标文本
        if (!m_coordinateText)
        {
            m_coordinateText = new QGraphicsTextItem(m_chart);
            m_coordinateText->setZValue(101);

            QFont font = m_coordinateText->font();
            font.setBold(true);
            m_coordinateText->setFont(font);

            QGraphicsRectItem *bg = new QGraphicsRectItem(m_coordinateText);
            bg->setBrush(QBrush(QColor(255, 255, 255, 200)));
            bg->setPen(QPen(Qt::lightGray));
            bg->setZValue(-1);
        }

        // 格式化X坐标文本
        QString xValueStr;
        if (qAbs(chartPos.x()) < 0.01)
        {
            xValueStr = "0";
        }
        else if (qAbs(chartPos.x()) < 10)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 3);
        }
        else if (qAbs(chartPos.x()) < 100)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 2);
        }
        else
        {
            xValueStr = QString::number(chartPos.x(), 'f', 1);
        }

        m_coordinateText->setHtml(QString("<div style='background: rgba(255,255,255,0.7); padding: 2px;'>"
                                          "<span style='color:black;'>背景左边界: %1</span></div>")
                                      .arg(xValueStr));

        // 计算文本位置 - 放在鼠标位置的右上方
        QPointF textPos = pos;
        textPos.setX(textPos.x() + 15);
        textPos.setY(textPos.y() - 15 - m_coordinateText->boundingRect().height());

        // 确保文本框不超出图表区域
        QRectF plotArea = m_chart->plotArea();
        if (textPos.x() + m_coordinateText->boundingRect().width() > plotArea.right())
        {
            textPos.setX(pos.x() - 15 - m_coordinateText->boundingRect().width());
        }

        if (textPos.y() < plotArea.top())
        {
            textPos.setY(pos.y() + 15);
        }

        m_coordinateText->setPos(textPos);

        // 调整背景矩形大小
        if (m_coordinateText->childItems().size() > 0)
        {
            QGraphicsRectItem *bg = qgraphicsitem_cast<QGraphicsRectItem *>(m_coordinateText->childItems().at(0));
            if (bg)
            {
                bg->setRect(m_coordinateText->boundingRect());
            }
        }
    }
    else if (m_isDraggingBgRight)
    {
        // 确保右边界不小于左边界
        chartPos.setX(qMax(chartPos.x(), m_tempBackgroundAreaRange.first));

        // 更新临时背景区域
        m_tempBackgroundAreaRange.second = chartPos.x();

        // 同时更新实际的背景区域范围
        m_backgroundAreaRange.second = m_tempBackgroundAreaRange.second;

        updateTempBackgroundArea();

        // 更新坐标气泡提示
        if (!m_coordinateText)
        {
            m_coordinateText = new QGraphicsTextItem(m_chart);
            m_coordinateText->setZValue(101);

            QFont font = m_coordinateText->font();
            font.setBold(true);
            m_coordinateText->setFont(font);

            QGraphicsRectItem *bg = new QGraphicsRectItem(m_coordinateText);
            bg->setBrush(QBrush(QColor(255, 255, 255, 200)));
            bg->setPen(QPen(Qt::lightGray));
            bg->setZValue(-1);
        }

        // 格式化X坐标文本
        QString xValueStr;
        if (qAbs(chartPos.x()) < 0.01)
        {
            xValueStr = "0";
        }
        else if (qAbs(chartPos.x()) < 10)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 3);
        }
        else if (qAbs(chartPos.x()) < 100)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 2);
        }
        else
        {
            xValueStr = QString::number(chartPos.x(), 'f', 1);
        }

        m_coordinateText->setHtml(QString("<div style='background: rgba(255,255,255,0.7); padding: 2px;'>"
                                          "<span style='color:black;'>背景右边界: %1</span></div>")
                                      .arg(xValueStr));

        // 计算文本位置 - 放在鼠标位置的右上方
        QPointF textPos = pos;
        textPos.setX(textPos.x() + 15);
        textPos.setY(textPos.y() - 15 - m_coordinateText->boundingRect().height());

        // 确保文本框不超出图表区域
        QRectF plotArea = m_chart->plotArea();
        if (textPos.x() + m_coordinateText->boundingRect().width() > plotArea.right())
        {
            textPos.setX(pos.x() - 15 - m_coordinateText->boundingRect().width());
        }

        if (textPos.y() < plotArea.top())
        {
            textPos.setY(pos.y() + 15);
        }

        m_coordinateText->setPos(textPos);

        // 调整背景矩形大小
        if (m_coordinateText->childItems().size() > 0)
        {
            QGraphicsRectItem *bg = qgraphicsitem_cast<QGraphicsRectItem *>(m_coordinateText->childItems().at(0));
            if (bg)
            {
                bg->setRect(m_coordinateText->boundingRect());
            }
        }
    }
}

// 新增: 更新临时背景区域显示
void LxChart::updateTempBackgroundArea()
{
    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "LxChart::updateTempBackgroundArea: 图表无数据系列，跳过背景区域更新";
        return;
    }

    // 检查并更新defaultSeries
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "LxChart::updateTempBackgroundArea: 无法获取默认系列，跳过背景区域更新";
            return;
        }
    }

    if (!m_bgAreaDraggable)
        return;

    // 获取背景区域的范围
    qreal startX = m_backgroundAreaRange.first;
    qreal endX = m_backgroundAreaRange.second;

    // 确保起点在终点前
    if (startX > endX)
    {
        qSwap(startX, endX);
    }

    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    qreal minY = 0;
    qreal maxY = 0;
    if (valueAxisY)
    {
        minY = valueAxisY->min();
        maxY = valueAxisY->max();
    }

    // 获取X轴范围

    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 检查背景区域是否在当前X轴范围内
    // 如果完全不在X轴范围内，则不显示背景区域
    if (endX < minX || startX > maxX)
    {
        // 背景区域完全不在当前X轴范围内，隐藏相关元素
        if (m_bgRect)
        {
            m_bgRect->setVisible(false);
        }
        if (m_bgStartLine)
        {
            m_bgStartLine->setVisible(false);
        }
        if (m_bgEndLine)
        {
            m_bgEndLine->setVisible(false);
        }
        return;
    }

    // 确保背景区域在X轴不超出坐标轴范围
    startX = qMax(startX, minX);
    endX = qMin(endX, maxX);

    // 转换为图表坐标
    QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);

    // 更新背景区域矩形
    if (m_bgRect)
    {
        m_bgRect->setVisible(true);
        m_bgRect->setRect(QRectF(topLeft, bottomRight).normalized());
    }

    // 更新左边界虚线
    if (m_bgStartLine)
    {
        m_bgStartLine->setVisible(true);
        QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
        QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
        m_bgStartLine->setLine(QLineF(startTop, startBottom));
    }

    // 更新右边界虚线
    if (m_bgEndLine)
    {
        m_bgEndLine->setVisible(true);
        QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
        QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
        m_bgEndLine->setLine(QLineF(endTop, endBottom));
    }
}

void LxChart::updateCustomArea()
{
    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);

    // 获取当前X轴和Y轴范围
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();
    qreal minY = valueAxisY->min();
    qreal maxY = valueAxisY->max();

    // 更新自定义区域以适应新的坐标轴范围
    for (int i = 0; i < vecCustomRange.size(); i++)
    {
        GlobalDefine::CustomRange &customRange = vecCustomRange[i];

        // 检查自定义区域是否完全在当前X轴范围外
        if (customRange.range.second < minX || customRange.range.first > maxX)
        {
            // 如果自定义区域完全不在当前X轴范围内，隐藏相关元素
            if (customRange.m_StartLine)
            {
                customRange.m_StartLine->setVisible(false);
            }
            if (customRange.m_EndLine)
            {
                customRange.m_EndLine->setVisible(false);
            }
            if (customRange.m_Rect)
            {
                customRange.m_Rect->setVisible(false);
            }
            continue;
        }

        // 获取默认曲线，用于坐标转换
        if (!defaultSeries)
        {
            defaultSeries = getDefaultSeries();
            if (!defaultSeries)
            {
                qDebug() << "updateCustomArea更新自定义区域失败：defaultSeries不存在";
                continue;
            }
        }

        // 确保自定义区域范围在当前坐标轴范围内
        qreal startX = qMax(customRange.range.first, minX);
        qreal endX = qMin(customRange.range.second, maxX);

        // 更新左边界线
        if (customRange.m_StartLine)
        {
            customRange.m_StartLine->setVisible(true);
            QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
            QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
            customRange.m_StartLine->setLine(QLineF(startTop, startBottom));
        }

        // 更新右边界线
        if (customRange.m_EndLine)
        {
            customRange.m_EndLine->setVisible(true);
            QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
            QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
            customRange.m_EndLine->setLine(QLineF(endTop, endBottom));
        }

        // 更新区域矩形
        if (customRange.m_Rect)
        {
            customRange.m_Rect->setVisible(true);
            QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
            QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
            customRange.m_Rect->setRect(QRectF(topLeft, bottomRight).normalized());
        }
    }
}

void LxChart::checkChartTypeForHideControls(GlobalEnums::TrackType type)
{
    // 隐藏拆分图层按钮
    ui->btn_splitCharts->hide();

    if (type == GlobalEnums::TrackType::MS)
    {
        // MassChart需要隐藏设置背景按钮
        ui->btn_setBackgroundArea->hide(); // 隐藏设置背景按钮
        ui->btn_showPeakArea->hide();      // 隐藏显示积分区域按钮
        ui->btn_lastExperiment->hide();    // 隐藏移动浏览按钮(向前)
        ui->btn_nextExperiment->hide();    // 隐藏移动浏览按钮(向后)
        ui->label_currentLoadTime->hide(); // 隐藏当前加载时间控件
    }
}

// 处理自定义区域选择
void LxChart::handleCustomRangeSelection(qreal startX, qreal endX)
{
    // 确保起点在终点前
    if (startX > endX)
    {
        qSwap(startX, endX);
    }

    // 检查自定义区域数量是否已达到上限
    if (vecCustomRange.size() >= GlobalDefine::CUSTOM_RANGE_SIZE)
    {
        qDebug() << QString("自定义区域数量已达到上限(%1)，无法添加更多区域").arg(GlobalDefine::CUSTOM_RANGE_SIZE);
        return;
    }

    // 获取默认曲线
    if (!defaultSeries)
    {
        return;
    }

    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    qreal minY = 0;
    qreal maxY = 0;
    if (valueAxisY)
    {
        minY = valueAxisY->min();
        maxY = valueAxisY->max();
    }

    // 获取X轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 确保区域范围在坐标系内
    startX = qMax(startX, minX);
    endX = qMin(endX, maxX);

    // 创建一个新的自定义区域结构体
    GlobalDefine::CustomRange customRange;
    customRange.range = qMakePair(startX, endX);
    customRange.index = vecCustomRange.size(); // 当前索引就是数组大小

    // 获取当前应使用的颜色（循环使用颜色列表）
    if (m_customRangeColors.isEmpty())
    {
        // 如果颜色列表为空，使用默认浅蓝色
        customRange.color = QColor(173, 216, 230);
    }
    else
    {
        customRange.color = m_customRangeColors[customRange.index % m_customRangeColors.size()];
    }

    // 创建左边界线
    customRange.m_StartLine = new QGraphicsLineItem(m_chart);
    customRange.m_StartLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
    customRange.m_StartLine->setZValue(5);
    QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
    customRange.m_StartLine->setLine(QLineF(startTop, startBottom));

    // 创建右边界线
    customRange.m_EndLine = new QGraphicsLineItem(m_chart);
    customRange.m_EndLine->setPen(QPen(Qt::gray, 1, Qt::SolidLine));
    customRange.m_EndLine->setZValue(5);
    QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
    QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
    customRange.m_EndLine->setLine(QLineF(endTop, endBottom));

    // 创建区域矩形
    customRange.m_Rect = new QGraphicsRectItem(m_chart);
    customRange.m_Rect->setPen(Qt::NoPen);
    // customRange.m_Rect->setBrush(QBrush(customRange.color.lighter(150))); // 使用更浅的颜色作为填充，增加透明度
    customRange.m_Rect->setBrush(QBrush(customRange.color)); // 使用更浅的颜色作为填充，增加透明度
    customRange.m_Rect->setOpacity(0.3);                     // 设置透明度
    QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
    customRange.m_Rect->setRect(QRectF(topLeft, bottomRight).normalized());
    customRange.m_Rect->setZValue(4);

    // 添加到自定义区域数组
    vecCustomRange.append(customRange);

    qDebug() << "添加自定义区域" << customRange.index << "范围:" << startX << "到" << endX << "颜色:" << customRange.color.name();

    // 创建平均质谱
    createAvgMassForCustomRange(customRange.index);
}

// 清除指定索引的自定义区域，若索引为-1则清除所有
void LxChart::clearCustomRangeSelection(int index)
{
    if (index >= 0 && index < vecCustomRange.size())
    {
        // 清除指定索引的自定义区域
        GlobalDefine::CustomRange &customRange = vecCustomRange[index];

        // 先删除关联的平均质谱
        if (customRange.hasAvgMass || customRange.avgMassData)
        {
            removeAvgMassForCustomRange(index);
        }

        // 移除图形项
        if (customRange.m_StartLine)
        {
            m_chart->scene()->removeItem(customRange.m_StartLine);
            delete customRange.m_StartLine;
            customRange.m_StartLine = nullptr;
        }

        if (customRange.m_EndLine)
        {
            m_chart->scene()->removeItem(customRange.m_EndLine);
            delete customRange.m_EndLine;
            customRange.m_EndLine = nullptr;
        }

        if (customRange.m_Rect)
        {
            m_chart->scene()->removeItem(customRange.m_Rect);
            delete customRange.m_Rect;
            customRange.m_Rect = nullptr;
        }

        // 从数组中移除
        vecCustomRange.removeAt(index);

        // 更新剩余区域的索引
        for (int i = 0; i < vecCustomRange.size(); i++)
        {
            vecCustomRange[i].index = i;
        }

        qDebug() << "移除自定义区域" << index << "，当前剩余" << vecCustomRange.size() << "个区域";
    }
    else if (index == -1)
    {
        // 清除所有自定义区域
        for (int i = 0; i < vecCustomRange.size(); i++)
        {
            GlobalDefine::CustomRange &customRange = vecCustomRange[i];

            if (customRange.m_StartLine)
            {
                m_chart->scene()->removeItem(customRange.m_StartLine);
                delete customRange.m_StartLine;
                customRange.m_StartLine = nullptr;
            }

            if (customRange.m_EndLine)
            {
                m_chart->scene()->removeItem(customRange.m_EndLine);
                delete customRange.m_EndLine;
                customRange.m_EndLine = nullptr;
            }

            if (customRange.m_Rect)
            {
                m_chart->scene()->removeItem(customRange.m_Rect);
                delete customRange.m_Rect;
                customRange.m_Rect = nullptr;
            }
        }

        vecCustomRange.clear();
        qDebug() << "清除所有自定义区域";
    }
}

// 更新临时自定义区域显示
void LxChart::updateCustomRangeTemp(qreal startX, qreal endX)
{
    if (!defaultSeries)
    {
        return;
    }

    // 确保起点在终点前
    if (startX > endX)
    {
        qSwap(startX, endX);
    }

    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    qreal minY = 0;
    qreal maxY = 0;
    if (valueAxisY)
    {
        minY = valueAxisY->min();
        maxY = valueAxisY->max();
    }

    // 获取X轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 确保区域范围在坐标系内
    startX = qMax(startX, minX);
    endX = qMin(endX, maxX);

    // 清除之前的临时自定义区域
    if (m_customRangeRect)
    {
        m_chart->scene()->removeItem(m_customRangeRect);
        delete m_customRangeRect;
        m_customRangeRect = nullptr;
    }

    if (m_customRangeStartLine)
    {
        m_chart->scene()->removeItem(m_customRangeStartLine);
        delete m_customRangeStartLine;
        m_customRangeStartLine = nullptr;
    }

    if (m_customRangeEndLine)
    {
        m_chart->scene()->removeItem(m_customRangeEndLine);
        delete m_customRangeEndLine;
        m_customRangeEndLine = nullptr;
    }

    // 创建临时左边界线
    m_customRangeStartLine = new QGraphicsLineItem(m_chart);
    m_customRangeStartLine->setPen(QPen(Qt::black, 1, Qt::DashLine));
    m_customRangeStartLine->setZValue(5);
    QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
    m_customRangeStartLine->setLine(QLineF(startTop, startBottom));

    // 创建临时右边界线
    m_customRangeEndLine = new QGraphicsLineItem(m_chart);
    m_customRangeEndLine->setPen(QPen(Qt::black, 1, Qt::DashLine));
    m_customRangeEndLine->setZValue(5);
    QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
    QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
    m_customRangeEndLine->setLine(QLineF(endTop, endBottom));

    // 创建临时区域矩形
    m_customRangeRect = new QGraphicsRectItem(m_chart);
    m_customRangeRect->setPen(Qt::NoPen);
    m_customRangeRect->setBrush(QBrush(QColor(128, 0, 128, 50))); // 紫色半透明
    QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
    QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
    m_customRangeRect->setRect(QRectF(topLeft, bottomRight).normalized());
    m_customRangeRect->setZValue(4);
}

// 检查指定位置是否已存在自定义区域
bool LxChart::isCustomRangeExist(qreal x)
{
    for (int i = 0; i < vecCustomRange.size(); i++)
    {
        qreal start = vecCustomRange[i].range.first;
        qreal end = vecCustomRange[i].range.second;

        if (x >= start && x <= end)
        {
            return true;
        }
    }

    return false;
}

// 根据点击位置查找自定义区域索引
QVector<int> LxChart::findCustomRangeAtPosition(const QPointF &pos)
{
    QVector<int> tempVec;
    for (int i = 0; i < vecCustomRange.size(); i++)
    {
        if (vecCustomRange[i].m_Rect && vecCustomRange[i].m_Rect->contains(pos))
        {
            tempVec << i;
        }
    }

    return tempVec;
}

// 检查是否靠近自定义区域边界
bool LxChart::isNearCustomRangeEdge(const QPointF &pos, int &index, bool &isLeftEdge)
{
    index = -1;
    isLeftEdge = false;

    // 获取边界检测宽度，复用背景区域的设置
    qreal detectionWidth = m_bgEdgeDetectionWidth;

    for (int i = 0; i < vecCustomRange.size(); i++)
    {
        // 检查是否接近左边界
        if (vecCustomRange[i].m_StartLine)
        {
            QLineF line = vecCustomRange[i].m_StartLine->line();
            qreal distance = pointToSegmentDistance(pos, line);

            if (distance <= detectionWidth)
            {
                index = i;
                isLeftEdge = true;
                return true;
            }
        }

        // 检查是否接近右边界
        if (vecCustomRange[i].m_EndLine)
        {
            QLineF line = vecCustomRange[i].m_EndLine->line();
            qreal distance = pointToSegmentDistance(pos, line);

            if (distance <= detectionWidth)
            {
                index = i;
                isLeftEdge = false;
                return true;
            }
        }
    }

    return false;
}

// 处理自定义区域边缘拖动
void LxChart::handleCustomRangeEdgeDragging(const QPointF &pos)
{
    if (!defaultSeries || m_draggingCustomRangeIndex < 0 || m_draggingCustomRangeIndex >= vecCustomRange.size())
        return;

    // 确保不显示十字准星
    if (m_showCrosshair)
    {
        clearCrosshair(false);
    }

    // 确保光标为双箭头形状
    m_chart->setCursor(Qt::CursorShape(Qt::SizeHorCursor));

    QPointF chartPos = m_chart->mapToValue(pos, defaultSeries);

    // 获取X轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 确保拖动不超出坐标轴范围
    chartPos.setX(qMax(minX, qMin(chartPos.x(), maxX)));

    // 获取当前拖动的自定义区域
    GlobalDefine::CustomRange &customRange = vecCustomRange[m_draggingCustomRangeIndex];

    if (m_isDraggingCustomRangeLeft)
    {
        // 确保左边界不超过右边界
        chartPos.setX(qMin(chartPos.x(), customRange.range.second));

        // 更新自定义区域范围
        customRange.range.first = chartPos.x();

        // 更新区域显示
        updateCustomRange(m_draggingCustomRangeIndex);

        // 更新坐标气泡提示
        if (!m_coordinateText)
        {
            m_coordinateText = new QGraphicsTextItem(m_chart);
            m_coordinateText->setZValue(101);

            QFont font = m_coordinateText->font();
            font.setBold(true);
            m_coordinateText->setFont(font);

            QGraphicsRectItem *bg = new QGraphicsRectItem(m_coordinateText);
            bg->setBrush(QBrush(QColor(255, 255, 255, 200)));
            bg->setPen(QPen(Qt::lightGray));
            bg->setZValue(-1);
        }

        // 格式化X坐标文本
        QString xValueStr;
        if (qAbs(chartPos.x()) < 0.01)
        {
            xValueStr = "0";
        }
        else if (qAbs(chartPos.x()) < 10)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 3);
        }
        else if (qAbs(chartPos.x()) < 100)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 2);
        }
        else
        {
            xValueStr = QString::number(chartPos.x(), 'f', 1);
        }

        m_coordinateText->setHtml(QString("<div style='background: rgba(255,255,255,0.7); padding: 2px;'>"
                                          "<span style='color:black;'>自定义区域 %1 左边界: %2</span></div>")
                                      .arg(customRange.index)
                                      .arg(xValueStr));

        // 计算文本位置 - 放在鼠标位置的右上方
        QPointF textPos = pos;
        textPos.setX(textPos.x() + 15);
        textPos.setY(textPos.y() - 15 - m_coordinateText->boundingRect().height());

        // 确保文本框不超出图表区域
        QRectF plotArea = m_chart->plotArea();
        if (textPos.x() + m_coordinateText->boundingRect().width() > plotArea.right())
        {
            textPos.setX(pos.x() - 15 - m_coordinateText->boundingRect().width());
        }

        if (textPos.y() < plotArea.top())
        {
            textPos.setY(pos.y() + 15);
        }

        m_coordinateText->setPos(textPos);

        // 调整背景矩形大小
        if (m_coordinateText->childItems().size() > 0)
        {
            QGraphicsRectItem *bg = qgraphicsitem_cast<QGraphicsRectItem *>(m_coordinateText->childItems().at(0));
            if (bg)
            {
                bg->setRect(m_coordinateText->boundingRect());
            }
        }
    }
    else if (m_isDraggingCustomRangeRight)
    {
        // 确保右边界不小于左边界
        chartPos.setX(qMax(chartPos.x(), customRange.range.first));

        // 更新自定义区域范围
        customRange.range.second = chartPos.x();

        // 更新区域显示
        updateCustomRange(m_draggingCustomRangeIndex);

        // 更新坐标气泡提示
        if (!m_coordinateText)
        {
            m_coordinateText = new QGraphicsTextItem(m_chart);
            m_coordinateText->setZValue(101);

            QFont font = m_coordinateText->font();
            font.setBold(true);
            m_coordinateText->setFont(font);

            QGraphicsRectItem *bg = new QGraphicsRectItem(m_coordinateText);
            bg->setBrush(QBrush(QColor(255, 255, 255, 200)));
            bg->setPen(QPen(Qt::lightGray));
            bg->setZValue(-1);
        }

        // 格式化X坐标文本
        QString xValueStr;
        if (qAbs(chartPos.x()) < 0.01)
        {
            xValueStr = "0";
        }
        else if (qAbs(chartPos.x()) < 10)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 3);
        }
        else if (qAbs(chartPos.x()) < 100)
        {
            xValueStr = QString::number(chartPos.x(), 'f', 2);
        }
        else
        {
            xValueStr = QString::number(chartPos.x(), 'f', 1);
        }

        m_coordinateText->setHtml(QString("<div style='background: rgba(255,255,255,0.7); padding: 2px;'>"
                                          "<span style='color:black;'>自定义区域 %1 右边界: %2</span></div>")
                                      .arg(customRange.index)
                                      .arg(xValueStr));

        // 计算文本位置 - 放在鼠标位置的右上方
        QPointF textPos = pos;
        textPos.setX(textPos.x() + 15);
        textPos.setY(textPos.y() - 15 - m_coordinateText->boundingRect().height());

        // 确保文本框不超出图表区域
        QRectF plotArea = m_chart->plotArea();
        if (textPos.x() + m_coordinateText->boundingRect().width() > plotArea.right())
        {
            textPos.setX(pos.x() - 15 - m_coordinateText->boundingRect().width());
        }

        if (textPos.y() < plotArea.top())
        {
            textPos.setY(pos.y() + 15);
        }

        m_coordinateText->setPos(textPos);

        // 调整背景矩形大小
        if (m_coordinateText->childItems().size() > 0)
        {
            QGraphicsRectItem *bg = qgraphicsitem_cast<QGraphicsRectItem *>(m_coordinateText->childItems().at(0));
            if (bg)
            {
                bg->setRect(m_coordinateText->boundingRect());
            }
        }
    }
}

// 更新自定义区域
void LxChart::updateCustomRange(int index)
{
    if (index < 0 || index >= vecCustomRange.size() || !defaultSeries)
        return;

    GlobalDefine::CustomRange &customRange = vecCustomRange[index];

    // 获取坐标轴范围
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);
    qreal minY = 0;
    qreal maxY = 0;
    if (valueAxisY)
    {
        minY = valueAxisY->min();
        maxY = valueAxisY->max();
    }

    // 获取X轴范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    qreal minX = valueAxisX->min();
    qreal maxX = valueAxisX->max();

    // 确保范围在坐标系内
    qreal startX = qMax(customRange.range.first, minX);
    qreal endX = qMin(customRange.range.second, maxX);

    // 更新左边界线
    if (customRange.m_StartLine)
    {
        QPointF startTop = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
        QPointF startBottom = m_chart->mapToPosition(QPointF(startX, minY), defaultSeries);
        customRange.m_StartLine->setLine(QLineF(startTop, startBottom));
    }

    // 更新右边界线
    if (customRange.m_EndLine)
    {
        QPointF endTop = m_chart->mapToPosition(QPointF(endX, maxY), defaultSeries);
        QPointF endBottom = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
        customRange.m_EndLine->setLine(QLineF(endTop, endBottom));
    }

    // 更新矩形区域//
    if (customRange.m_Rect)
    {
        QPointF topLeft = m_chart->mapToPosition(QPointF(startX, maxY), defaultSeries);
        QPointF bottomRight = m_chart->mapToPosition(QPointF(endX, minY), defaultSeries);
        customRange.m_Rect->setRect(QRectF(topLeft, bottomRight).normalized());
    }
}

// 更新标签数据坐标
void LxChart::updateLabelPosition(CustomLabelWidget *widget)
{
    if (!widget)
        return;

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "LxChart::updateLabelPosition: 图表无数据系列，跳过标签位置更新";
        return;
    }

    // 确保存在默认曲线用于坐标转换
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "更新标签坐标失败：没有默认曲线";
            return;
        }
    }

    // 获取标签当前像素位置（中心点）
    // QPoint widgetCenter = widget->pos() + QPoint(widget->width() / 2, widget->height() / 2);
    QPoint widgetCenter = widget->pos();

    // 将屏幕坐标转换为数据坐标
    QPointF dataPoint = m_chart->mapToValue(widgetCenter, defaultSeries);

    // 更新标签的数据坐标
    widget->setPoint(dataPoint);

    // 更新对应标记点的位置
    if (widget->getMarker())
    {
        QPointF position = m_chart->mapToPosition(dataPoint, defaultSeries);
        widget->getMarker()->setPos(position);
    }

    // qDebug() << "标签移动 - 屏幕坐标:" << widgetCenter << "数据坐标:" << dataPoint;
}

// 为标注创建对应的标记点
QGraphicsEllipseItem *LxChart::createMarkerForLabel(CustomLabelWidget *widget)
{
    if (!widget)
        return nullptr;

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "LxChart::createMarkerForLabel: 图表无数据系列，跳过标记点创建";
        return nullptr;
    }

    // 确保存在默认曲线用于坐标转换
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "创建标记点失败：没有默认曲线";
            return nullptr;
        }
    }

    // 创建一个小圆点作为标记（不传递父对象，避免自动添加到场景）
    QGraphicsEllipseItem *marker = new QGraphicsEllipseItem();

    // 设置标记点大小为5x5像素
    marker->setRect(-4, -4, 8, 8);

    // 设置标记点颜色为蓝色填充
    marker->setBrush(QBrush(Qt::blue));
    marker->setPen(QPen(Qt::blue));

    // 设置Z值使其位于图表上方
    marker->setZValue(10);

    // 手动添加到场景中，避免重复添加
    if (!marker->scene())
    {
        m_chart->scene()->addItem(marker);
    }

    // 更新标记点位置
    QPointF dataPoint = widget->getPoint();
    QPointF screenPos = m_chart->mapToPosition(dataPoint, defaultSeries);
    marker->setPos(screenPos);

    qDebug() << "创建标记点 - 数据坐标:" << dataPoint << "屏幕坐标:" << screenPos;

    return marker;
}

QVector<std::tuple<QString, int, QVector<double>>> LxChart::getBgMassPointVec()
{
    if (!AvgMassManager::isRefExist)
    {
        AvgMassManager::isRefExist = true;
    }
    bgMassPointVec.clear();
    QVector<std::tuple<QString, int, QVector<double>>> vec; // 直接使用栈上变量，而不是在堆上分配

    // 获取背景区域左右范围
    double left = m_backgroundAreaRange.first;
    double right = m_backgroundAreaRange.second;

    // 检查范围有效性
    if (left >= right)
    {
        qDebug() << __FUNCTION__ << "背景区域范围无效: [" << left << ", " << right << "]";
        return vec; // 返回空向量
    }

    foreach (LxChartData *chartData, m_chartDataVec)
    {
        if (!chartData)
        {
            qDebug() << __FUNCTION__ << "图表数据为空";
            continue;
        }

        int eventId = chartData->getEventNum();
        QString path = chartData->getParamPath();

        // 检查数据向量有效性
        if (chartData->getDataX().isEmpty())
        {
            qDebug() << path << "事件:" << eventId << __FUNCTION__ << "X数据为空";
            continue;
        }

        int indexLeft = findIndex(chartData->getDataX(), left, true, false);
        int indexRight = findIndex(chartData->getDataX(), right, true, true);

        if (!(indexLeft < indexRight && indexLeft != -1 && indexRight != -1))
        {
            qDebug() << path << "事件:" << eventId << __FUNCTION__ << "索引无效: left=" << indexLeft << ", right=" << indexRight;
            continue;
        }

        // 边界检查
        if (indexLeft < 0 || indexRight >= chartData->getDataX().size())
        {
            qDebug() << path << "事件:" << eventId << __FUNCTION__ << "索引越界: left=" << indexLeft << ", right=" << indexRight
                     << ", 大小=" << chartData->getDataX().size();
            continue;
        }

        qDebug() << "left right" << indexLeft << indexRight;

        // 安全地计算子向量长度
        int length = indexRight - indexLeft + 1;
        if (length <= 0 || indexLeft + length > chartData->getDataX().size())
        {
            qDebug() << path << "事件:" << eventId << __FUNCTION__ << " 子向量长度无效: " << length;
            continue;
        }

        QVector<double> subIndexVec = chartData->getDataX().mid(indexLeft, length);
        qDebug() << "size:" << subIndexVec.size();
        vec.push_back(std::make_tuple(path, eventId, subIndexVec));
    }

    // 将结果保存到成员变量
    bgMassPointVec = vec;

    if (!vec.isEmpty())
    {
        emit sg_calAvgMass(vec);
    }
    else
    {
        qDebug() << __FUNCTION__ << "没有有效的背景区域数据点";
    }

    return vec; // 返回结果
}

// 显示峰标记点
void LxChart::showPeaks()
{
    // 先清除所有现有的峰标记图形项（但保留峰数据）
    clearAllPeakGraphicsItems();

    // 如果没有默认曲线，则尝试获取
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            // qDebug() << "showPeaks: 没有默认曲线";
            return;
        }
    }

    // 记录是否是第一次寻峰
    bool isFirstTime = m_isFirstPeakFinding;

    // 初始化峰标记文本字体
    if (m_peakLabelFont.family().isEmpty())
    {
        m_peakLabelFont = QFont("Arial", 8);
    }

    // 全局收集所有现有标签的位置，用于重叠检测
    QVector<QRectF> globalExistingLabels;

    // 遍历所有 LxChartData 对象
    for (LxChartData *data : m_chartDataVec)
    {
        // 跳过无效数据
        if (!data)
            continue;

        // 通过UniqueID查找对应的曲线series
        QAbstractSeries *abstractSeries = nullptr;
        QString uniqueID = data->getUniqueID();

        for (QAbstractSeries *series : m_chart->series())
        {
            if (series->name() == uniqueID)
            {
                abstractSeries = series;
                break;
            }
        }

        if (!abstractSeries)
        {
            // qDebug() << "showPeaks: 找不到对应的曲线series，UniqueID:" << uniqueID;
            continue;
        }

        // 检查曲线是否可见
        if (!abstractSeries->isVisible())
        {
            // qDebug() << "showPeaks: 曲线不可见，跳过峰标记，UniqueID:" << uniqueID;
            continue;
        }

        // qDebug() << "showPeaks: 开始为曲线创建峰标记，UniqueID:" << uniqueID << "峰数量:" << data->peakVec.size();

        int colorIndex = 0; // 用于交替使用颜色

        // 为每个峰创建峰标签（移除峰点绘制）
        for (int i = 0; i < data->peakVec.size(); i++)
        {
            Peak &peak = data->peakVec[i];
            // qDebug() << peak.pTop << peak.pStart << peak.pEnd;

            // 不再创建峰点标记，只保留峰标签
            // peak.item 保持为 nullptr

            // 创建阴影区域（但不立即显示）
            peak.shadeItem = new QGraphicsPathItem(m_chart);
            peak.shadeItem->setVisible(false); // 默认隐藏积分区域

            // 获取原始数据
            const QVector<double> dataY = data->getDataY();
            const QVector<double> dataX = data->getDataX();

            // 创建路径 - 使用屏幕坐标
            QPainterPath path;

            // 确保索引在有效范围内
            if (peak.start >= 0 && peak.end < dataX.size() && peak.start < peak.end)
            {
                // 转换起点到屏幕坐标
                QPointF startPos = m_chart->mapToPosition(peak.pStart, abstractSeries);

                // 🎯 检查起点坐标有效性
                if (std::isnan(startPos.x()) || std::isnan(startPos.y()) || std::isinf(startPos.x()) || std::isinf(startPos.y()))
                {
                    continue; // 跳过这个峰
                }

                path.moveTo(startPos);

                // 从起点到终点添加所有数据点 - 转换为屏幕坐标
                for (int j = peak.start + 1; j <= peak.end; j++)
                {
                    if (j < dataX.size() && j < dataY.size())
                    {
                        QPointF dataPos(dataX[j], dataY[j]);
                        QPointF screenPos = m_chart->mapToPosition(dataPos, abstractSeries);

                        // 🎯 检查屏幕坐标有效性，避免QPainterPath::lineTo错误
                        if (!std::isnan(screenPos.x()) && !std::isnan(screenPos.y()) && !std::isinf(screenPos.x()) && !std::isinf(screenPos.y()))
                        {
                            path.lineTo(screenPos);
                        }
                    }
                }

                // 添加基线（终点到起点的直线）以闭合路径 - 使用屏幕坐标
                QPointF endPos = m_chart->mapToPosition(peak.pEnd, abstractSeries);
                path.lineTo(endPos);

                // 计算基线坐标（使用Peak中的实际基线）
                QPointF baseStart = m_chart->mapToPosition(peak.pStart, abstractSeries);
                QPointF baseEnd = m_chart->mapToPosition(peak.pEnd, abstractSeries);
                path.lineTo(baseEnd);
                path.lineTo(baseStart);

                // 设置路径
                peak.shadeItem->setPath(path);

                // 设置阴影颜色（交替使用两种颜色）
                QColor shadeColor = (colorIndex % 2 == 0) ? m_peakShadeColor1 : m_peakShadeColor2;
                peak.shadeItem->setBrush(QBrush(shadeColor));
                peak.shadeItem->setPen(Qt::NoPen); // 无边框

                // 设置Z值，确保阴影在图表上但在标记点下方
                peak.shadeItem->setZValue(5);

                colorIndex++; // 更新颜色索引
                // qDebug() << "添加阴影区域，路径长度:" << path.length();
            }

            // 创建峰值文本标签
            QVector<QRectF> newLabels;
            createPeakLabel(peak, data, abstractSeries, globalExistingLabels, newLabels);
            globalExistingLabels.append(newLabels);

            // qDebug() << "添加marker peak" << screenPos << dataPoint << "end";
        }
    }

    // 更新标记点位置，考虑可视范围
    updatePeaksPos();

    // 如果是第一次寻峰，调用重置坐标轴来修正坐标映射
    if (isFirstTime)
    {
        m_isFirstPeakFinding = false; // 标记已不是第一次
        // qDebug() << "第一次寻峰完成，调用重置坐标轴修正坐标映射";
        QTimer::singleShot(50, this, [this]()
                           {
                               resetZoom(); // 重置坐标轴，修正坐标映射
                                            // qDebug() << "第一次寻峰坐标映射修正完成"; });
                           });
    }

    // 🎯 修复：连接坐标轴范围变化信号，确保缩放后更新标签位置
    connectAxisRangeChangedSignals();
}

// 隐藏峰标记点
void LxChart::hidePeaks()
{
    // 遍历所有数据及其标记点
    for (auto it = m_chartDataVec.begin(); it != m_chartDataVec.end(); it++)
    {
        // 从图表删除阴影区域、文本标签和引线（不再删除峰点）
        for (int i = 0; i < (*it)->peakVec.size(); i++)
        {
            Peak &p = (*it)->peakVec[i];
            // 不再删除峰点，因为已移除峰点绘制
            // p.item 始终为 nullptr
            // 删除阴影区域
            if (p.shadeItem)
            {
                m_chart->scene()->removeItem(p.shadeItem);
                // qDebug() << "移除阴影区域";
            }
            // 删除文本标签
            if (p.textItem)
            {
                m_chart->scene()->removeItem(p.textItem);
                // qDebug() << "移除文本标签";
            }
            // 删除引线
            if (p.lineItem)
            {
                m_chart->scene()->removeItem(p.lineItem);
                // qDebug() << "移除引线";
            }
        }
    }
}

// 更新峰标记点位置
void LxChart::updatePeaksPos()
{
    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        // qDebug() << "图表无数据系列，跳过峰位置更新";
        return;
    }

    // 如果没有默认曲线，则尝试获取
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            // qDebug() << "LxChart::updatePeaksPos没有默认曲线";
            return;
        }
    }

    // 获取当前图表的可视范围
    QRectF plotArea = m_chart->plotArea();

    // 全局收集所有现有标签的位置，用于重叠检测
    QVector<QRectF> globalExistingLabels;

    // 遍历所有数据
    for (LxChartData *data : m_chartDataVec)
    {
        if (!data)
        {
            continue;
        }

        // 通过UniqueID查找对应的曲线series
        QAbstractSeries *abstractSeries = nullptr;
        QString uniqueID = data->getUniqueID();

        for (QAbstractSeries *series : m_chart->series())
        {
            if (series->name() == uniqueID)
            {
                abstractSeries = series;
                break;
            }
        }

        if (!abstractSeries)
        {
            // qDebug() << "updatePeaksPos: 找不到对应的曲线series，UniqueID:" << uniqueID;
            continue;
        }

        // 更新每个峰标记点的位置
        for (int i = 0; i < data->peakVec.size(); i++)
        {
            Peak &peak = data->peakVec[i];

            // 不再更新峰点位置，因为已移除峰点绘制
            // peak.item 始终为 nullptr

            // 更新阴影区域
            if (peak.shadeItem)
            {
                // 检查峰的任一点是否在可视区域内 - 使用屏幕坐标
                QPointF topScreenPos = m_chart->mapToPosition(peak.pTop, abstractSeries);
                QPointF startScreenPos = m_chart->mapToPosition(peak.pStart, abstractSeries);
                QPointF endScreenPos = m_chart->mapToPosition(peak.pEnd, abstractSeries);

                bool isInViewport =
                    (plotArea.contains(topScreenPos) || plotArea.contains(startScreenPos) || plotArea.contains(endScreenPos)) && abstractSeries->isVisible();

                // 积分区域的可见性取决于：1.是否在视口内 2.用户是否开启了积分区域显示 3.是否受阈值控制
                bool aboveThreshold = !m_thresholdEnabled || (peak.pTop.y() >= m_thresholdValue);
                bool isVisible = isInViewport && m_peakAreasVisible && aboveThreshold;

                // qDebug() << "阴影是否可见:" << isVisible << "视口内:" << isInViewport << "用户开启:" << m_peakAreasVisible;

                // 设置可见性
                peak.shadeItem->setVisible(isVisible);

                // 如果可见，更新阴影区域路径
                if (isVisible)
                {
                    // 获取原始数据
                    const QVector<double> dataY = data->getDataY();
                    const QVector<double> dataX = data->getDataX();

                    // 确保索引在有效范围内
                    if (peak.start >= 0 && peak.end < dataX.size() && peak.start < peak.end)
                    {
                        // 创建路径 - 使用屏幕坐标
                        QPainterPath path;

                        // 转换起点到屏幕坐标
                        QPointF startPos = m_chart->mapToPosition(peak.pStart, abstractSeries);

                        // 🎯 检查起点坐标有效性
                        if (std::isnan(startPos.x()) || std::isnan(startPos.y()) || std::isinf(startPos.x()) || std::isinf(startPos.y()))
                        {
                            continue; // 跳过这个峰
                        }

                        path.moveTo(startPos);

                        // 从起点到终点添加所有数据点 - 转换为屏幕坐标
                        for (int j = peak.start + 1; j <= peak.end; j++)
                        {
                            if (j < dataX.size() && j < dataY.size())
                            {
                                QPointF dataPos(dataX[j], dataY[j]);
                                QPointF screenPos = m_chart->mapToPosition(dataPos, abstractSeries);

                                // 🎯 检查屏幕坐标有效性，避免QPainterPath::lineTo错误
                                if (!std::isnan(screenPos.x()) && !std::isnan(screenPos.y()) && !std::isinf(screenPos.x()) && !std::isinf(screenPos.y()))
                                {
                                    path.lineTo(screenPos);
                                }
                            }
                        }

                        // 添加基线（终点到起点的直线）以闭合路径 - 使用屏幕坐标
                        QPointF endPos = m_chart->mapToPosition(peak.pEnd, abstractSeries);
                        path.lineTo(endPos);

                        // 计算基线坐标（使用Peak中的实际基线）
                        QPointF baseStart = m_chart->mapToPosition(peak.pStart, abstractSeries);
                        QPointF baseEnd = m_chart->mapToPosition(peak.pEnd, abstractSeries);
                        path.lineTo(baseEnd);
                        path.lineTo(baseStart);

                        // 设置路径
                        peak.shadeItem->setPath(path);
                    }
                }

                // 确保阴影区域在场景中（只在不存在时添加）
                if (!peak.shadeItem->scene())
                {
                    m_chart->scene()->addItem(peak.shadeItem);
                }
            }

            // 更新文本标签位置
            if (peak.textItem)
            {
                // 获取峰顶点数据坐标
                QPointF dataPoint = peak.pTop;
                // 将数据坐标转换为屏幕坐标
                QPointF screenPos = m_chart->mapToPosition(dataPoint, abstractSeries);

                // 根据曲线类型和配置生成峰标签文本
                LabelFiledType labelType = LabelFiledType::Blank;
                GlobalEnums::TrackType trackType = data->getTrackType();

                switch (trackType)
                {
                case GlobalEnums::TrackType::TIC:
                case GlobalEnums::TrackType::XIC:
                    labelType = OptionsDialogSettings::getChromatogramsSettings().FiledType;
                    break;
                case GlobalEnums::TrackType::MS:
                    labelType = OptionsDialogSettings::getMassSettings().FiledType;
                    break;
                case GlobalEnums::TrackType::DAD:
                    labelType = OptionsDialogSettings::getDADSettings().FiledType;
                    break;
                default:
                    labelType = LabelFiledType::All; // 默认显示全部信息
                    break;
                }

                QString peakText = generatePeakLabelText(peak, labelType, trackType, data);
                QFontMetrics fm(m_peakLabelFont);
                QSizeF textSize = fm.size(Qt::TextExpandTabs | Qt::TextDontClip, peakText);

                // 计算上方位置
                QPointF labelPos(screenPos.x() - textSize.width() / 2, screenPos.y() - m_peakLabelOffset - textSize.height());
                QRectF labelRect(labelPos, textSize);

                // 检查是否与现有标签重叠
                bool hasOverlap = false;
                for (const QRectF &existingRect : globalExistingLabels)
                {
                    if (labelRect.intersects(existingRect))
                    {
                        hasOverlap = true;
                        break;
                    }
                }

                // 检查是否在可视区域内
                bool inPlotArea = plotArea.contains(labelRect);

                // 决定是否显示标签
                bool shouldShow = inPlotArea && !hasOverlap && abstractSeries->isVisible();

                if (shouldShow)
                {
                    peak.textItem->setPos(labelPos);
                    peak.textItem->setVisible(true);
                    peak.originalTextVisible = true;        // 记录原始显示状态
                    globalExistingLabels.append(labelRect); // 记录这个标签的位置
                    // qDebug() << "缩放后更新峰标签显示，峰坐标:" << peak.pTop << "位置:" << labelPos;
                }
                else
                {
                    peak.textItem->setVisible(false);
                    peak.originalTextVisible = false; // 记录原始隐藏状态
                    // qDebug() << "缩放后峰标签仍隐藏，峰坐标:" << peak.pTop << "inPlotArea:" << inPlotArea << "hasOverlap:" << hasOverlap;
                }

                // 确保文本标签在场景中（只在不存在时添加）
                if (!peak.textItem->scene())
                {
                    m_chart->scene()->addItem(peak.textItem);
                }
            }
            else // 如果没有文本标签，检查是否可以创建
            {
                // 获取峰顶点数据坐标
                QPointF dataPoint = peak.pTop;
                // 将数据坐标转换为屏幕坐标
                QPointF screenPos = m_chart->mapToPosition(dataPoint, abstractSeries);

                // 检查峰标记是否应该可见
                bool peakVisible = plotArea.contains(screenPos) && abstractSeries->isVisible();

                // 只有当峰标记可见时才尝试创建文本标签
                if (!peakVisible)
                {
                    continue; // 跳过这个峰
                }

                // 根据曲线类型和配置生成峰标签文本
                LabelFiledType labelType = LabelFiledType::Blank;
                GlobalEnums::TrackType trackType = data->getTrackType();

                switch (trackType)
                {
                case GlobalEnums::TrackType::TIC:
                case GlobalEnums::TrackType::XIC:
                    labelType = OptionsDialogSettings::getChromatogramsSettings().FiledType;
                    break;
                case GlobalEnums::TrackType::MS:
                    labelType = OptionsDialogSettings::getMassSettings().FiledType;
                    break;
                case GlobalEnums::TrackType::DAD:
                    labelType = OptionsDialogSettings::getDADSettings().FiledType;
                    break;
                default:
                    labelType = LabelFiledType::All; // 默认显示全部信息
                    break;
                }

                QString peakText = generatePeakLabelText(peak, labelType, trackType, data);
                QFontMetrics fm(m_peakLabelFont);
                QSizeF textSize = fm.size(Qt::TextExpandTabs | Qt::TextDontClip, peakText);

                // 计算上方位置
                QPointF labelPos(screenPos.x() - textSize.width() / 2, screenPos.y() - m_peakLabelOffset - textSize.height());
                QRectF labelRect(labelPos, textSize);

                // 检查是否在可视区域内
                bool inPlotArea = plotArea.contains(labelRect);

                // 检查是否与现有标签重叠
                bool hasOverlap = false;
                for (const QRectF &existingRect : globalExistingLabels)
                {
                    if (labelRect.intersects(existingRect))
                    {
                        hasOverlap = true;
                        break;
                    }
                }

                // 如果可以显示，创建新的文本标签（但要确保不重复创建）
                if (inPlotArea && !hasOverlap)
                {
                    // 检查是否已经存在文本标签，避免重复创建
                    if (!peak.textItem)
                    {
                        peak.textItem = new QGraphicsTextItem(peakText, m_chart);
                        peak.textItem->setFont(m_peakLabelFont);
                        peak.textItem->setDefaultTextColor(m_peakLabelColor);
                        peak.textItem->setPos(labelPos);
                        peak.textItem->setZValue(15);

                        // 添加到场景
                        m_chart->scene()->addItem(peak.textItem);

                        // 设置为可见，并记录原始显示状态
                        peak.textItem->setVisible(true);
                        peak.originalTextVisible = true; // 记录峰标签的原始显示状态

                        // qDebug() << "缩放后新创建峰标签，峰坐标:" << peak.pTop << "位置:" << labelPos;
                    }
                    else
                    {
                        // 如果已经存在，只更新位置和可见性
                        peak.textItem->setPos(labelPos);
                        peak.textItem->setVisible(true);
                        peak.originalTextVisible = true; // 记录原始显示状态
                        // qDebug() << "缩放后更新现有峰标签位置，峰坐标:" << peak.pTop << "位置:" << labelPos;
                    }

                    // 记录这个标签的位置
                    globalExistingLabels.append(labelRect);
                }
                else
                {
                    // qDebug() << "缩放后仍无法创建峰标签，峰坐标:" << peak.pTop << "inPlotArea:" << inPlotArea << "hasOverlap:" << hasOverlap;
                }
            }

            // 更新引线位置 - 由于我们只在上方显示标签，不需要引线
            if (peak.lineItem)
            {
                // 隐藏引线，因为标签总是在峰点正上方
                peak.lineItem->setVisible(false);

                // 确保引线在场景中（只在不存在时添加）
                if (!peak.lineItem->scene())
                {
                    m_chart->scene()->addItem(peak.lineItem);
                }
            }
        }
    }

    // 更新阈值线位置
    updateThresholdLinePosition();
}

// 刷新峰标签文本（不重新寻峰）
void LxChart::refreshPeakLabels()
{
    qDebug() << "LxChart::refreshPeakLabels: 开始刷新峰标签文本";

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        qDebug() << "图表无数据系列，跳过峰标签刷新";
        return;
    }

    // 遍历所有数据
    for (LxChartData *data : m_chartDataVec)
    {
        if (!data || data->peakVec.empty())
        {
            qDebug() << "跳过1111";
            continue;
        }

        // 通过UniqueID查找对应的曲线series
        QAbstractSeries *abstractSeries = nullptr;
        QString uniqueID = data->getUniqueID();

        for (QAbstractSeries *series : m_chart->series())
        {
            if (series->name() == uniqueID)
            {
                abstractSeries = series;
                break;
            }
        }
        qDebug() << "1111111111111";
        if (!abstractSeries)
        {
            qDebug() << "refreshPeakLabels: 找不到对应的曲线series，UniqueID:" << uniqueID;
            continue;
        }
        qDebug() << "2222222222222";

        // 获取当前曲线类型对应的标签类型
        LabelFiledType labelType = LabelFiledType::Blank;
        GlobalEnums::TrackType trackType = data->getTrackType();

        switch (trackType)
        {
        case GlobalEnums::TrackType::TIC:
        case GlobalEnums::TrackType::XIC:
            labelType = OptionsDialogSettings::getChromatogramsSettings().FiledType;
            break;
        case GlobalEnums::TrackType::MS:
            labelType = OptionsDialogSettings::getMassSettings().FiledType;
            break;
        case GlobalEnums::TrackType::DAD:
            labelType = OptionsDialogSettings::getDADSettings().FiledType;
            break;
        default:
            labelType = LabelFiledType::Blank;
            break;
        }
        qDebug() << "labelType=======" << static_cast<int>(labelType);
        // 更新每个峰的标签文本
        for (int i = 0; i < data->peakVec.size(); i++)
        {
            Peak &peak = data->peakVec[i];

            if (peak.textItem)
            {
                // 生成新的峰标签文本
                QString newPeakText = generatePeakLabelText(peak, labelType, trackType, data);

                // 更新文本内容
                peak.textItem->setPlainText(newPeakText);

                // 如果是空白类型，隐藏标签
                if (labelType == LabelFiledType::Blank)
                {
                    peak.textItem->setVisible(false);
                }
                else if (abstractSeries->isVisible())
                {
                    // 如果曲线可见且不是空白类型，显示标签
                    peak.textItem->setVisible(true);
                }

                qDebug() << "刷新峰标签文本，峰坐标:" << peak.pTop << "新文本:" << newPeakText << "SNR:" << peak.snr;
            }
        }
    }

    // 刷新完成后，重新调整标签位置以避免重叠
    updatePeaksPos();

    qDebug() << "LxChart::refreshPeakLabels: 峰标签文本刷新完成";
}

// 连接坐标轴范围变化信号
void LxChart::connectAxisRangeChangedSignals()
{
    if (!m_chart || !m_chart->axes().size())
        return;

    // 连接X轴范围变化信号
    for (QAbstractAxis *axis : m_chart->axes(Qt::Horizontal))
    {
        QValueAxis *valueAxis = qobject_cast<QValueAxis *>(axis);
        if (valueAxis)
        {
            // 断开之前的连接，避免重复连接
            disconnect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updatePeaksPos);
            disconnect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updateVertexLabelsPosition);
            // 重新连接
            connect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updatePeaksPos);
            connect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updateVertexLabelsPosition);
        }
    }

    // 连接Y轴范围变化信号
    for (QAbstractAxis *axis : m_chart->axes(Qt::Vertical))
    {
        QValueAxis *valueAxis = qobject_cast<QValueAxis *>(axis);
        if (valueAxis)
        {
            // 断开之前的连接，避免重复连接
            disconnect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updatePeaksPos);
            disconnect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updateVertexLabelsPosition);
            // 重新连接
            connect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updatePeaksPos);
            connect(valueAxis, &QValueAxis::rangeChanged, this, &LxChart::updateVertexLabelsPosition);
        }
    }

    // qDebug() << "LxChart::connectAxisRangeChangedSignals: 坐标轴范围变化信号连接完成";
}

// 🎯 为MRM数据创建智能顶点标签（带重叠检测）
void LxChart::createMrmLabels()
{
    qDebug() << "LxChart::createMrmLabels: 🎯 开始创建MRM顶点标签";

    // 先清除现有的MRM标签
    clearMrmLabels();

    // 初始化顶点标签字体（与峰标签一致）
    QFont vertexLabelFont = m_peakLabelFont;    // 使用与峰标签相同的字体
    QColor vertexLabelColor = m_peakLabelColor; // 使用与峰标签相同的颜色

    // 全局收集所有现有标签的位置，用于重叠检测
    QVector<QRectF> globalExistingLabels;

    // 遍历所有图表数据，找到MRM数据
    for (LxChartData *data : m_chartDataVec)
    {
        if (!data)
            continue;

        // 检查是否是MRM数据（直接检查，不依赖shouldUseBarChart）
        if (!data->isMrmData())
            continue;

        MassChartData *massData = qobject_cast<MassChartData *>(data);
        if (!massData)
            continue;

        // 获取数据点
        QVector<QPointF> dataPoints = data->getData();
        if (dataPoints.isEmpty())
            continue;

        // 通过UniqueID查找对应的曲线series
        QAbstractSeries *abstractSeries = nullptr;
        QString uniqueID = data->getUniqueID();

        for (QAbstractSeries *series : m_chart->series())
        {
            if (series->name() == uniqueID)
            {
                abstractSeries = series;
                break;
            }
        }

        if (!abstractSeries)
        {
            qDebug() << "LxChart::createMrmLabels: 找不到对应的曲线series，UniqueID:" << uniqueID;
            continue;
        }

        // 🎯 为每个数据点创建智能标签，增加保护
        for (int i = 0; i < dataPoints.size(); ++i)
        {
            try
            {
                const QPointF &point = dataPoints[i];

                // 🎯 验证数据点有效性
                if (std::isnan(point.x()) || std::isnan(point.y()) || std::isinf(point.x()) || std::isinf(point.y()))
                {
                    qDebug() << "LxChart::createMrmLabels: 跳过无效数据点，索引:" << i << "，点:" << point;
                    continue;
                }

                // 🎯 计算棒子在X轴上的实际显示位置（使用分组位置）
                qreal barXPosition = massData->getMassStartPosition() + i;

                // 验证位置有效性
                if (std::isnan(barXPosition) || std::isinf(barXPosition))
                {
                    qDebug() << "LxChart::createMrmLabels: 棒子X位置无效，索引:" << i << "，位置:" << barXPosition;
                    continue;
                }

                QPointF barDisplayPoint(barXPosition, point.y());

                // 创建顶点标签（显示m/z和强度，单行格式）
                QString labelText = QString("%1, %2").arg(point.x(), 0, 'f', 1).arg(point.y(), 0, 'f', 0);

                // 🎯 创建智能顶点标签，增加异常保护
                createVertexLabel(barDisplayPoint, labelText, abstractSeries, vertexLabelFont, vertexLabelColor, globalExistingLabels, massData->getParamPath(),
                                  i);
            }
            catch (const std::exception &e)
            {
                qDebug() << "LxChart::createMrmLabels: 处理数据点" << i << "时发生异常:" << e.what();
                continue;
            }
            catch (...)
            {
                qDebug() << "LxChart::createMrmLabels: 处理数据点" << i << "时发生未知异常";
                continue;
            }
        }
    }

    qDebug() << "LxChart::createMrmLabels: 🎯 创建完成，共创建" << m_mrmLabels.size() << "个顶点标签";
}

// 🎯 创建智能顶点标签（带重叠检测和智能定位）
void LxChart::createVertexLabel(const QPointF &vertexPos, const QString &labelText, QAbstractSeries *abstractSeries, const QFont &font, const QColor &color,
                                QVector<QRectF> &existingLabels, const QString &filePath, int barIndex)
{
    if (!abstractSeries || labelText.isEmpty())
    {
        return;
    }

    // 将数据坐标转换为屏幕坐标
    QPointF screenPos = m_chart->mapToPosition(vertexPos, abstractSeries);

    // 计算文本尺寸
    QFontMetrics fm(font);
    QSizeF textSize = fm.size(Qt::TextExpandTabs | Qt::TextDontClip, labelText);

    // 🎯 简化位置计算：直接放在棒子顶部上方
    QPointF bestPos(screenPos.x() - textSize.width() / 2, screenPos.y() - textSize.height() - 5);

    // 🎯 暂时禁用重叠检测，确保所有标签都能创建
    // TODO: 后续可以重新启用重叠检测

    // 🎯 创建文本标签，增加异常保护
    QGraphicsTextItem *textItem = nullptr;
    try
    {
        textItem = new QGraphicsTextItem(labelText);
        if (!textItem)
        {
            qDebug() << "LxChart::createVertexLabel: 创建QGraphicsTextItem失败";
            return;
        }

        textItem->setFont(font);
        textItem->setDefaultTextColor(color);
        textItem->setPos(bestPos);
        textItem->setZValue(16); // 比峰标签(15)稍高，确保在最上层

        // 🎯 验证场景有效性后添加到场景
        if (m_chart && m_chart->scene())
        {
            m_chart->scene()->addItem(textItem);
            textItem->setVisible(true);

            // 保存到MRM标签列表
            m_mrmLabels.append(textItem);

            qDebug() << "LxChart::createVertexLabel: 成功创建标签:" << labelText << "，位置:" << bestPos;
        }
        else
        {
            qDebug() << "LxChart::createVertexLabel: 图表场景无效，删除标签";
            delete textItem;
            textItem = nullptr;
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "LxChart::createVertexLabel: 创建标签时发生异常:" << e.what();
        if (textItem)
        {
            delete textItem;
            textItem = nullptr;
        }
    }
    catch (...)
    {
        qDebug() << "LxChart::createVertexLabel: 创建标签时发生未知异常";
        if (textItem)
        {
            delete textItem;
            textItem = nullptr;
        }
    }

    // 标签创建成功
}

// 🎯 计算顶点标签的最佳位置（智能避让重叠）
QPointF LxChart::calculateOptimalVertexLabelPosition(const QPointF &vertexScreenPos, const QSizeF &textSize, const QVector<QRectF> &existingLabels)
{
    // 定义多个候选位置（按优先级排序）
    QVector<QPointF> candidatePositions;

    const qreal margin = 5.0; // 标签与顶点的间距

    // 1. 优先位置：正上方
    candidatePositions.append(QPointF(vertexScreenPos.x() - textSize.width() / 2, vertexScreenPos.y() - textSize.height() - margin));

    // 2. 备选位置：左上方
    candidatePositions.append(QPointF(vertexScreenPos.x() - textSize.width() - margin, vertexScreenPos.y() - textSize.height() - margin));

    // 3. 备选位置：右上方
    candidatePositions.append(QPointF(vertexScreenPos.x() + margin, vertexScreenPos.y() - textSize.height() - margin));

    // 4. 备选位置：左侧
    candidatePositions.append(QPointF(vertexScreenPos.x() - textSize.width() - margin, vertexScreenPos.y() - textSize.height() / 2));

    // 5. 备选位置：右侧
    candidatePositions.append(QPointF(vertexScreenPos.x() + margin, vertexScreenPos.y() - textSize.height() / 2));

    // 检查每个候选位置是否可用
    for (const QPointF &pos : candidatePositions)
    {
        QRectF testRect(pos, textSize);
        bool isAvailable = true;

        // 检查是否与现有标签重叠
        for (const QRectF &existingRect : existingLabels)
        {
            if (testRect.intersects(existingRect))
            {
                isAvailable = false;
                break;
            }
        }

        // 检查是否在图表可视区域内
        QRectF plotArea = m_chart->plotArea();
        if (isAvailable && plotArea.contains(testRect))
        {
            return pos;
        }
    }

    // 如果所有位置都不可用，返回默认位置（正上方）
    return candidatePositions.first();
}

// 🎯 更新顶点标签位置（在图表缩放或尺寸改变时调用）
void LxChart::updateVertexLabelsPosition()
{
    // 🎯 先清理所有现有标签，避免重复显示
    clearMrmLabels();

    // 检查图表是否有数据系列
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->series().isEmpty())
    {
        return;
    }

    // 如果没有默认曲线，则尝试获取
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            return;
        }
    }

    // 获取当前图表的可视范围
    QRectF plotArea = m_chart->plotArea();

    // 全局收集所有现有标签的位置，用于重叠检测
    QVector<QRectF> globalExistingLabels;

    // 遍历所有MRM数据
    for (LxChartData *data : m_chartDataVec)
    {
        if (!data || !data->isMrmData())
        {
            continue;
        }

        // 通过UniqueID查找对应的曲线series
        QAbstractSeries *abstractSeries = nullptr;
        QString uniqueID = data->getUniqueID();

        for (QAbstractSeries *series : m_chart->series())
        {
            if (series->name() == uniqueID)
            {
                abstractSeries = series;
                break;
            }
        }

        if (!abstractSeries)
        {
            continue;
        }

        // 🎯 更新每个MRM棒子顶点标签的位置
        updateMrmVertexLabels(data, abstractSeries, plotArea, globalExistingLabels);
    }
}

// 🎯 更新单个MRM数据的顶点标签
void LxChart::updateMrmVertexLabels(LxChartData *data, QAbstractSeries *abstractSeries, const QRectF &plotArea, QVector<QRectF> &globalExistingLabels)
{
    if (!data || !abstractSeries)
        return;

    // 获取MRM数据点
    QVector<QPointF> dataPoints = data->getData();
    QStringList categories = data->getMrmCategories();

    // 确保数据点和类别数量一致
    if (dataPoints.size() != categories.size())
    {
        qDebug() << "LxChart::updateMrmVertexLabels: 数据点和类别数量不一致";
        return;
    }

    // 🎯 为每个MRM棒子顶点更新标签
    for (int i = 0; i < dataPoints.size(); i++)
    {
        const QPointF &vertex = dataPoints[i];
        const QString &mzValue = categories[i];

        // 计算棒子在图表中的实际X位置
        int barPosition = data->getMassStartPosition() + i;
        QPointF barVertex(barPosition, vertex.y());

        // 将数据坐标转换为屏幕坐标
        QPointF screenPos = m_chart->mapToPosition(barVertex, abstractSeries);

        // 🎯 更新或创建顶点标签
        updateSingleVertexLabel(data, i, barVertex, mzValue, screenPos, plotArea, globalExistingLabels, abstractSeries);
    }
}

// 🎯 更新单个顶点标签
void LxChart::updateSingleVertexLabel(LxChartData *data, int vertexIndex, const QPointF &vertex, const QString &mzValue, const QPointF &screenPos,
                                      const QRectF &plotArea, QVector<QRectF> &globalExistingLabels, QAbstractSeries *abstractSeries)
{
    // 🎯 优化标签文本格式，减少长度避免重叠
    // 简化m/z值显示（去掉小数点后多余的0）
    QString simplifiedMz = mzValue;
    if (simplifiedMz.contains('.'))
    {
        // 移除末尾的0和小数点
        while (simplifiedMz.endsWith('0'))
            simplifiedMz.chop(1);
        if (simplifiedMz.endsWith('.'))
            simplifiedMz.chop(1);
    }

    // 格式化顶点标签文本（显示简化的m/z值和强度值）
    QString vertexText = QString("%1\n%2").arg(simplifiedMz).arg(vertex.y(), 0, 'f', 0);

    // 获取文本大小
    QFontMetrics fm(m_peakLabelFont); // 使用与峰标签相同的字体
    QSizeF textSize = fm.size(Qt::TextExpandTabs | Qt::TextDontClip, vertexText);

    // 计算标签位置（在顶点上方）
    QPointF labelPos(screenPos.x() - textSize.width() / 2, screenPos.y() - m_peakLabelOffset - textSize.height());
    QRectF labelRect(labelPos, textSize);

    // 🎯 扩展标签矩形，增加间距避免过于拥挤
    qreal margin = 5.0; // 标签间距
    QRectF expandedRect = labelRect.adjusted(-margin, -margin, margin, margin);

    // 🎯 MRM标签允许超出边界，只检查重叠
    // bool inPlotArea = plotArea.contains(labelRect);  // 不再检查边界

    // 🎯 使用扩展矩形检查重叠，确保标签间有足够间距
    bool hasOverlap = false;
    for (const QRectF &existingRect : globalExistingLabels)
    {
        if (expandedRect.intersects(existingRect))
        {
            hasOverlap = true;
            break;
        }
    }

    // 🎯 决定是否显示标签（只检查重叠和系列可见性，允许超出边界）
    bool shouldShow = !hasOverlap && abstractSeries->isVisible();

    // 🎯 查找或创建对应的标签
    QGraphicsTextItem *textItem = findOrCreateVertexLabel(data, vertexIndex);

    if (shouldShow && textItem)
    {
        textItem->setPlainText(vertexText);
        textItem->setPos(labelPos);
        textItem->setVisible(true);
        globalExistingLabels.append(expandedRect); // 🎯 记录扩展矩形，确保间距
    }
    else if (textItem)
    {
        textItem->setVisible(false);
    }
}

// 🎯 查找或创建顶点标签
QGraphicsTextItem *LxChart::findOrCreateVertexLabel(LxChartData *data, int vertexIndex)
{
    if (!data || !m_chart)
        return nullptr;

    // 🎯 为每个MRM数据创建独立的标签管理
    // 使用数据的UniqueID和顶点索引作为标识
    QString labelKey = QString("%1_%2").arg(data->getUniqueID()).arg(vertexIndex);

    // 查找现有标签
    for (QGraphicsTextItem *label : m_mrmLabels)
    {
        if (label && label->data(0).toString() == labelKey)
        {
            return label;
        }
    }

    // 创建新标签
    QGraphicsTextItem *newLabel = new QGraphicsTextItem(m_chart);
    newLabel->setFont(m_peakLabelFont);
    newLabel->setDefaultTextColor(m_peakLabelColor);
    newLabel->setZValue(16);        // 🎯 使用16避免与峰标签(15)冲突
    newLabel->setData(0, labelKey); // 存储标识符

    // 🎯 安全地添加到场景
    try
    {
        // 检查是否已经在场景中
        if (!newLabel->scene())
        {
            m_chart->scene()->addItem(newLabel);
        }

        // 添加到标签列表
        m_mrmLabels.append(newLabel);

        // qDebug() << "LxChart::findOrCreateVertexLabel: 创建新标签，key:" << labelKey;
    }
    catch (...)
    {
        qDebug() << "LxChart::findOrCreateVertexLabel: 添加标签到场景时出现异常";
        delete newLabel;
        return nullptr;
    }

    return newLabel;
}

// 设置峰阴影区域的颜色
void LxChart::setPeakShadeColors(const QColor &color1, const QColor &color2)
{
    m_peakShadeColor1 = color1;
    m_peakShadeColor2 = color2;
}

// 清除指定曲线的峰标记
void LxChart::clearPeaksForChartData(LxChartData *chartData)
{
    if (!chartData)
    {
        return;
    }

    // 清除该曲线的所有峰标记
    for (int i = 0; i < chartData->peakVec.size(); i++)
    {
        Peak &peak = chartData->peakVec[i];

        // 不再删除峰点，因为已移除峰点绘制
        // peak.item 始终为 nullptr

        // 删除阴影区域
        if (peak.shadeItem)
        {
            if (peak.shadeItem->scene())
            {
                m_chart->scene()->removeItem(peak.shadeItem);
            }
            delete peak.shadeItem;
            peak.shadeItem = nullptr;
        }

        // 删除文本标签
        if (peak.textItem)
        {
            try
            {
                if (peak.textItem->scene())
                {
                    m_chart->scene()->removeItem(peak.textItem);
                }
                delete peak.textItem;
                peak.textItem = nullptr;
            }
            catch (...)
            {
                qDebug() << "LxChart::clearPeaksForChartData: 删除峰文本标签时发生异常";
                peak.textItem = nullptr; // 防止重复删除
            }
        }

        // 删除引线
        if (peak.lineItem)
        {
            if (peak.lineItem->scene())
            {
                m_chart->scene()->removeItem(peak.lineItem);
            }
            delete peak.lineItem;
            peak.lineItem = nullptr;
        }
    }

    // 清空峰数据向量
    chartData->peakVec.clear();

    // 强制刷新图表场景，确保所有图形项都被清除
    m_chart->scene()->update();
    m_chartView->viewport()->update();

    // qDebug() << "已清除曲线的所有峰标记和峰数据，曲线ID:" << chartData->getUniqueID();
}

// 清除所有峰标记的图形项（但保留峰数据）
void LxChart::clearAllPeakGraphicsItems()
{
    // qDebug() << "清除峰标记，曲线数量:" << m_chartDataVec.size();

    // 遍历所有曲线数据
    for (LxChartData *chartData : m_chartDataVec)
    {
        if (!chartData)
        {
            continue;
        }

        // qDebug() << "清除峰标记，UniqueID:" << chartData->getUniqueID() << "峰数量:" << chartData->peakVec.size();

        // 清除该曲线的所有峰标记图形项
        for (int i = 0; i < chartData->peakVec.size(); i++)
        {
            Peak &peak = chartData->peakVec[i];

            // 不再删除峰点图形项，因为已移除峰点绘制
            // peak.item 始终为 nullptr

            // 🎯 安全删除阴影区域图形项
            if (peak.shadeItem)
            {
                try
                {
                    if (peak.shadeItem->scene())
                    {
                        peak.shadeItem->scene()->removeItem(peak.shadeItem);
                    }
                    // 🎯 QGraphicsPathItem没有deleteLater，直接删除
                    delete peak.shadeItem;
                    peak.shadeItem = nullptr;
                }
                catch (...)
                {
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 删除峰阴影区域时出现异常";
                    peak.shadeItem = nullptr;
                }
            }

            // 🎯 安全删除文本标签图形项
            if (peak.textItem)
            {
                try
                {
                    if (peak.textItem->scene())
                    {
                        peak.textItem->scene()->removeItem(peak.textItem);
                    }
                    peak.textItem->deleteLater();
                    peak.textItem = nullptr;
                }
                catch (...)
                {
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 删除峰文本标签时出现异常";
                    peak.textItem = nullptr;
                }
            }

            // 🎯 安全删除引线图形项
            if (peak.lineItem)
            {
                try
                {
                    if (peak.lineItem->scene())
                    {
                        peak.lineItem->scene()->removeItem(peak.lineItem);
                    }
                    // 🎯 QGraphicsLineItem没有deleteLater，直接删除
                    delete peak.lineItem;
                    peak.lineItem = nullptr;
                }
                catch (...)
                {
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 删除峰引线时出现异常";
                    peak.lineItem = nullptr;
                }
            }
        }

        // 注意：这里不清空peakVec，保留峰数据
    }

    // 额外安全措施：检查场景中是否还有遗留的峰相关图形项
    QList<QGraphicsItem *> allItems = m_chart->scene()->items();
    int removedCount = 0;
    for (QGraphicsItem *item : allItems)
    {
        // 🎯 安全检查峰文本标签（通过ZValue判断，峰文本标签的ZValue是15）
        if (QGraphicsTextItem *textItem = qgraphicsitem_cast<QGraphicsTextItem *>(item))
        {
            if (textItem->zValue() == 15 && textItem != m_coordinateText)
            {
                try
                {
                    m_chart->scene()->removeItem(textItem);
                    textItem->deleteLater();
                    removedCount++;
                }
                catch (...)
                {
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 清理遗留文本标签时出现异常";
                }
            }
        }
        // 🎯 安全检查峰阴影区域（通过ZValue判断，峰阴影区域的ZValue是5）
        else if (QGraphicsPathItem *pathItem = qgraphicsitem_cast<QGraphicsPathItem *>(item))
        {
            if (pathItem->zValue() == 5)
            {
                try
                {
                    m_chart->scene()->removeItem(pathItem);
                    // 🎯 QGraphicsPathItem没有deleteLater，直接删除
                    delete pathItem;
                    removedCount++;
                }
                catch (...)
                {
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 清理遗留阴影区域时出现异常";
                }
            }
        }
        // 🎯 安全检查峰引线（通过ZValue判断，峰引线的ZValue是12）
        else if (QGraphicsLineItem *lineItem = qgraphicsitem_cast<QGraphicsLineItem *>(item))
        {
            if (lineItem->zValue() == 12)
            {
                try
                {
                    m_chart->scene()->removeItem(lineItem);
                    // 🎯 QGraphicsLineItem没有deleteLater，直接删除
                    delete lineItem;
                    removedCount++;
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 额外清除遗留的峰引线";
                }
                catch (...)
                {
                    qDebug() << "LxChart::clearAllPeakGraphicsItems: 清理遗留引线时出现异常";
                }
            }
        }
    }

    if (removedCount > 0)
    {
        qDebug() << "LxChart::clearAllPeakGraphicsItems: 额外清除了" << removedCount << "个遗留的峰图形项";
    }

    // 强制刷新图表场景，确保所有图形项都被清除
    m_chart->scene()->update();
    m_chartView->viewport()->update();

    // qDebug() << "峰标记清除完成";
}

// 计算峰标记文本的最佳位置
QPointF LxChart::calculateOptimalLabelPosition(const QPointF &peakPos, const QString &text, const QVector<QRectF> &existingLabels)
{
    // 获取文本大小
    QFontMetrics fm(m_peakLabelFont);
    QSizeF textSize = fm.size(Qt::TextSingleLine, text);

    // 优先尝试在峰点上方显示
    QPointF preferredPos(peakPos.x() - textSize.width() / 2, peakPos.y() - m_peakLabelOffset - textSize.height());

    // 检查上方位置是否可用
    if (isPositionAvailable(preferredPos, textSize, existingLabels))
    {
        return preferredPos;
    }

    // 如果上方不可用，寻找替代位置
    return findAlternativePosition(peakPos, textSize, existingLabels);
}

// 检查指定位置是否可用（不与现有标签重叠）
bool LxChart::isPositionAvailable(const QPointF &pos, const QSizeF &size, const QVector<QRectF> &existingLabels)
{
    QRectF newRect(pos, size);

    // 检查是否在图表可视区域内
    QRectF plotArea = m_chart->plotArea();
    if (!plotArea.contains(newRect))
    {
        return false;
    }

    // 检查是否与现有标签重叠
    for (const QRectF &existingRect : existingLabels)
    {
        if (newRect.intersects(existingRect))
        {
            return false;
        }
    }

    return true;
}

// 寻找替代位置
QPointF LxChart::findAlternativePosition(const QPointF &peakPos, const QSizeF &textSize, const QVector<QRectF> &existingLabels)
{
    // 尝试不同的位置：右上、左上、右下、左下
    QVector<QPointF> candidatePositions = {// 右上
                                           QPointF(peakPos.x() + m_peakLabelOffset, peakPos.y() - m_peakLabelOffset - textSize.height()),
                                           // 左上
                                           QPointF(peakPos.x() - m_peakLabelOffset - textSize.width(), peakPos.y() - m_peakLabelOffset - textSize.height()),
                                           // 右下
                                           QPointF(peakPos.x() + m_peakLabelOffset, peakPos.y() + m_peakLabelOffset),
                                           // 左下
                                           QPointF(peakPos.x() - m_peakLabelOffset - textSize.width(), peakPos.y() + m_peakLabelOffset)};

    for (const QPointF &pos : candidatePositions)
    {
        if (isPositionAvailable(pos, textSize, existingLabels))
        {
            return pos;
        }
    }

    // 如果所有基本位置都不可用，尝试更远的位置
    for (int distance = m_peakLabelOffset * 2; distance <= m_peakLabelOffset * 4; distance += m_peakLabelOffset)
    {
        QVector<QPointF> farPositions = {QPointF(peakPos.x() + distance, peakPos.y() - distance - textSize.height()),
                                         QPointF(peakPos.x() - distance - textSize.width(), peakPos.y() - distance - textSize.height()),
                                         QPointF(peakPos.x() + distance, peakPos.y() + distance),
                                         QPointF(peakPos.x() - distance - textSize.width(), peakPos.y() + distance)};

        for (const QPointF &pos : farPositions)
        {
            if (isPositionAvailable(pos, textSize, existingLabels))
            {
                return pos;
            }
        }
    }

    // 如果仍然找不到合适位置，返回默认位置（可能会重叠）
    return QPointF(peakPos.x() - textSize.width() / 2, peakPos.y() - m_peakLabelOffset - textSize.height());
}

// 创建峰标记文本标签
void LxChart::createPeakLabel(Peak &peak, LxChartData *chartData, QAbstractSeries *abstractSeries, const QVector<QRectF> &existingLabels,
                              QVector<QRectF> &newLabels)
{
    // 根据曲线类型获取对应的LabelFiledType设置
    LabelFiledType labelType = LabelFiledType::Blank;
    GlobalEnums::TrackType trackType = chartData->getTrackType();

    switch (trackType)
    {
    case GlobalEnums::TrackType::TIC:
    case GlobalEnums::TrackType::XIC:
        labelType = OptionsDialogSettings::getChromatogramsSettings().FiledType;
        break;
    case GlobalEnums::TrackType::MS:
        labelType = OptionsDialogSettings::getMassSettings().FiledType;
        break;
    case GlobalEnums::TrackType::DAD:
        labelType = OptionsDialogSettings::getDADSettings().FiledType;
        break;
    default:
        labelType = LabelFiledType::Blank;
        break;
    }

    // 如果设置为空白，则不显示峰标签
    if (labelType == LabelFiledType::Blank)
    {
        return;
    }

    // 根据LabelFiledType生成峰标签文本
    QString peakText = generatePeakLabelText(peak, labelType, trackType, chartData);

    // 将数据坐标转换为屏幕坐标
    QPointF screenPos = m_chart->mapToPosition(peak.pTop, abstractSeries);

    // 获取文本大小
    QFontMetrics fm(m_peakLabelFont);
    QSizeF textSize = fm.size(Qt::TextExpandTabs | Qt::TextDontClip, peakText);

    // 优先尝试在峰点上方显示
    QPointF preferredPos(screenPos.x() - textSize.width() / 2, screenPos.y() - m_peakLabelOffset - textSize.height());
    QRectF preferredRect(preferredPos, textSize);

    // 检查图表边界
    QRectF plotArea = m_chart->plotArea();
    if (!plotArea.contains(preferredRect))
    {
        // qDebug() << "峰标签超出边界，峰值:" << peak.pTop.y() << "位置:" << preferredPos << "边界:" << plotArea;
        return; // 如果连上方位置都超出边界，就不显示
    }

    // 检查是否与现有标签重叠
    bool hasOverlap = false;
    for (const QRectF &existingRect : existingLabels)
    {
        if (preferredRect.intersects(existingRect))
        {
            hasOverlap = true;
            // qDebug() << "峰标签重叠，峰值:" << peak.pTop.y() << "当前位置:" << preferredRect << "重叠位置:" << existingRect;
            break;
        }
    }

    // 如果有重叠，不创建标签
    if (hasOverlap)
    {
        return;
    }

    // qDebug() << "创建峰标签成功，峰坐标:" << peak.pTop << "位置:" << preferredPos << "峰高:" << peak.height << "半峰宽:" << peak.fwhm;

    // 创建文本标签
    peak.textItem = new QGraphicsTextItem(peakText, m_chart);
    peak.textItem->setFont(m_peakLabelFont);
    peak.textItem->setDefaultTextColor(m_peakLabelColor);
    peak.textItem->setPos(preferredPos);
    peak.textItem->setZValue(15); // 确保文本在最上层

    // 记录新标签的边界矩形
    newLabels.append(preferredRect);

    // 由于我们只使用上方位置，不需要引线
    // 引线逻辑已移除，因为标签总是在峰点正上方

    // 添加文本到场景（检查是否已存在）
    if (!peak.textItem->scene())
    {
        m_chart->scene()->addItem(peak.textItem);
    }

    // 设置为可见，并记录原始显示状态
    peak.textItem->setVisible(true);
    peak.originalTextVisible = true; // 记录峰标签的原始显示状态
}

// ==================== 阈值控制函数 ====================

// 设置阈值并更新峰点可见性
void LxChart::setThresholdValue(double threshold)
{
    m_thresholdValue = threshold;

    if (m_thresholdEnabled)
    {
        // 更新峰点和峰标签的可见性
        updatePeakVisibilityByThreshold();

        // 更新阈值线位置
        updateThresholdLinePosition();
    }
}

// 启用/禁用阈值控制
void LxChart::enableThreshold(bool enable)
{
    m_thresholdEnabled = enable;

    if (enable)
    {
        // 启用阈值，显示阈值线并更新峰点可见性
        showThresholdLine();
        updatePeakVisibilityByThreshold();
    }
    else
    {
        // 禁用阈值，隐藏阈值线并恢复所有峰点的原始可见性
        hideThresholdLine();

        // 🔧 修复：检查图表数据是否为空，避免崩溃
        if (!m_chartDataVec.isEmpty())
        {
            // 恢复所有峰点和峰标签的原始可见性
            for (LxChartData *data : m_chartDataVec)
            {
                if (!data)
                    continue;

                for (auto &peak : data->peakVec)
                {
                    // 恢复峰点可见性
                    if (peak.item)
                    {
                        peak.item->setVisible(true);
                        peak.visible = true;
                    }

                    // 恢复峰标签的原始可见性
                    if (peak.textItem)
                    {
                        peak.textItem->setVisible(peak.originalTextVisible);
                    }

                    // 恢复积分区域可见性
                    if (peak.shadeItem)
                    {
                        peak.shadeItem->setVisible(m_peakAreasVisible);
                    }
                }
            }
        }
    }
}

// 根据阈值更新峰点和峰标签的可见性
void LxChart::updatePeakVisibilityByThreshold()
{
    if (!m_thresholdEnabled)
        return;

    // 🔧 修复：检查图表数据是否为空，避免崩溃
    if (m_chartDataVec.isEmpty())
    {
        qDebug() << "LxChart::updatePeakVisibilityByThreshold: 图表数据为空，跳过峰点可见性更新";
        return;
    }

    // 遍历所有图表数据
    for (LxChartData *data : m_chartDataVec)
    {
        if (!data)
            continue;

        // 遍历每个峰
        for (auto &peak : data->peakVec)
        {
            // 判断峰高度是否超过阈值
            bool shouldBeVisible = (peak.pTop.y() >= m_thresholdValue);

            // 不再更新峰点可见性，因为已移除峰点绘制
            // 但仍需要更新 peak.visible 状态供其他逻辑使用
            peak.visible = shouldBeVisible;

            // 更新峰标签可见性
            if (peak.textItem)
            {
                if (shouldBeVisible && peak.originalTextVisible)
                {
                    // 峰高度超过阈值且原本应该显示，显示峰标签
                    peak.textItem->setVisible(true);
                }
                else
                {
                    // 峰高度低于阈值或原本就不应该显示，隐藏峰标签
                    peak.textItem->setVisible(false);
                }
            }

            // 更新积分区域可见性
            if (peak.shadeItem)
            {
                if (shouldBeVisible && m_peakAreasVisible)
                {
                    // 峰高度超过阈值且积分区域开关打开，还需要检查是否在视口内
                    QAbstractSeries *abstractSeries = data->getSeries();
                    if (abstractSeries && abstractSeries->isVisible())
                    {
                        QRectF plotArea = m_chart->plotArea();
                        QPointF topScreenPos = m_chart->mapToPosition(peak.pTop, abstractSeries);
                        QPointF startScreenPos = m_chart->mapToPosition(peak.pStart, abstractSeries);
                        QPointF endScreenPos = m_chart->mapToPosition(peak.pEnd, abstractSeries);

                        bool isInViewport = (plotArea.contains(topScreenPos) || plotArea.contains(startScreenPos) || plotArea.contains(endScreenPos));

                        if (isInViewport)
                        {
                            peak.shadeItem->setVisible(true);
                        }
                        else
                        {
                            peak.shadeItem->setVisible(false);
                        }
                    }
                    else
                    {
                        peak.shadeItem->setVisible(false);
                    }
                }
                else
                {
                    // 峰高度低于阈值或积分区域开关关闭，隐藏积分区域
                    peak.shadeItem->setVisible(false);
                }
            }
        }
    }
}

// 显示阈值线
void LxChart::showThresholdLine()
{
    if (!m_thresholdLine)
    {
        // 创建阈值线
        m_thresholdLine = new QGraphicsLineItem(m_chart);
        QPen pen(Qt::red, 2, Qt::DashLine); // 红色虚线
        m_thresholdLine->setPen(pen);
        m_thresholdLine->setZValue(5); // 确保在曲线上方但在峰标记下方
        m_chart->scene()->addItem(m_thresholdLine);
        // qDebug() << "阈值线创建成功";
    }

    m_thresholdLine->setVisible(true);
    // qDebug() << "阈值线设置为可见";
    updateThresholdLinePosition();
}

// 隐藏阈值线
void LxChart::hideThresholdLine()
{
    if (m_thresholdLine)
    {
        m_thresholdLine->setVisible(false);
    }
}

// 更新阈值线位置
void LxChart::updateThresholdLinePosition()
{
    if (!m_thresholdLine || !m_thresholdEnabled)
        return;

    // 🔧 修复：检查图表数据是否为空，避免崩溃
    if (m_chartDataVec.isEmpty() || !m_chart)
    {
        qDebug() << "LxChart::updateThresholdLinePosition: 图表数据为空或图表不存在，跳过阈值线更新";
        return;
    }

    // 获取默认曲线用于坐标转换
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "LxChart::updateThresholdLinePosition: 没有默认曲线，无法更新阈值线位置";
            return;
        }
    }

    // 🎯 检查阈值是否在当前Y轴显示范围内
    if (!m_chart->axes(Qt::Vertical).isEmpty())
    {
        QValueAxis *yAxis = qobject_cast<QValueAxis *>(m_chart->axes(Qt::Vertical).first());
        if (yAxis)
        {
            double yAxisMin = yAxis->min();
            double yAxisMax = yAxis->max();

            // 如果阈值超出Y轴范围，隐藏阈值线
            if (m_thresholdValue < yAxisMin || m_thresholdValue > yAxisMax)
            {
                m_thresholdLine->setVisible(false);
                qDebug() << "LxChart::updateThresholdLinePosition: 阈值" << m_thresholdValue << "超出Y轴范围[" << yAxisMin << "," << yAxisMax
                         << "]，隐藏阈值线";
                return;
            }
        }
    }

    // 获取图表的绘图区域
    QRectF plotArea = m_chart->plotArea();

    // 🎯 安全地将阈值Y坐标转换为屏幕坐标
    QPointF dataPoint(0, m_thresholdValue); // X坐标任意，只关心Y坐标

    // 检查defaultSeries有效性
    if (!defaultSeries || !m_chart->series().contains(defaultSeries))
    {
        qDebug() << "LxChart::updateThresholdLinePosition: 默认曲线无效，跳过阈值线更新";
        return;
    }

    QPointF screenPoint;
    try
    {
        screenPoint = m_chart->mapToPosition(dataPoint, defaultSeries);
    }
    catch (...)
    {
        qDebug() << "LxChart::updateThresholdLinePosition: mapToPosition调用失败";
        return;
    }

    // 设置阈值线的位置（横跨整个绘图区域）
    QLineF thresholdLine(plotArea.left(), screenPoint.y(), plotArea.right(), screenPoint.y());
    m_thresholdLine->setLine(thresholdLine);

    // 确保阈值线可见（阈值在Y轴范围内）
    m_thresholdLine->setVisible(true);

    // 🎯 图表缩放时不需要更新滑块位置，因为阈值没有变化

    // qDebug() << "阈值线位置更新: 阈值=" << m_thresholdValue << "屏幕Y=" << screenPoint.y() << "线段=" << thresholdLine;
}

// ==================== 积分区域显示控制函数 ====================

void LxChart::showPeakAreas()
{
    m_peakAreasVisible = true;
    qDebug() << "显示积分区域";

    // 🔧 修复：检查图表数据是否为空，避免崩溃
    if (m_chartDataVec.isEmpty())
    {
        qDebug() << "LxChart::showPeakAreas: 图表数据为空，跳过积分区域显示";
        return;
    }

    // 积分区域显示不需要特殊处理，因为它依赖于已经显示的峰标记

    // 遍历所有图表数据，显示积分区域
    foreach (LxChartData *data, m_chartDataVec)
    {
        if (!data || data->peakVec.empty())
            continue;

        for (auto &peak : data->peakVec)
        {
            if (peak.shadeItem)
            {
                // 检查峰是否在视口内
                QAbstractSeries *abstractSeries = data->getSeries();
                if (!abstractSeries || !abstractSeries->isVisible())
                    continue;

                QRectF plotArea = m_chart->plotArea();
                QPointF topScreenPos = m_chart->mapToPosition(peak.pTop, abstractSeries);
                QPointF startScreenPos = m_chart->mapToPosition(peak.pStart, abstractSeries);
                QPointF endScreenPos = m_chart->mapToPosition(peak.pEnd, abstractSeries);

                bool isInViewport = (plotArea.contains(topScreenPos) || plotArea.contains(startScreenPos) || plotArea.contains(endScreenPos));

                // 检查阈值控制
                bool aboveThreshold = !m_thresholdEnabled || (peak.pTop.y() >= m_thresholdValue);

                if (isInViewport && aboveThreshold)
                {
                    peak.shadeItem->setVisible(true);
                }
            }
        }
    }
}

void LxChart::hidePeakAreas()
{
    m_peakAreasVisible = false;
    qDebug() << "隐藏积分区域";

    // 🔧 修复：检查图表数据是否为空，避免崩溃
    if (m_chartDataVec.isEmpty())
    {
        qDebug() << "LxChart::hidePeakAreas: 图表数据为空，跳过积分区域隐藏";
        return;
    }

    // 遍历所有图表数据，隐藏积分区域
    foreach (LxChartData *data, m_chartDataVec)
    {
        if (!data || data->peakVec.empty())
            continue;

        for (auto &peak : data->peakVec)
        {
            if (peak.shadeItem)
            {
                peak.shadeItem->setVisible(false);
            }
        }
    }
}

void LxChart::togglePeakAreas()
{
    if (m_peakAreasVisible)
    {
        hidePeakAreas();
    }
    else
    {
        showPeakAreas();
    }
}

bool LxChart::isPeakAreasVisible() const
{
    return m_peakAreasVisible;
}

// ==================== 样式加载函数 ====================

void LxChart::loadLxChartStyles()
{
    // 尝试多个可能的QSS文件路径
    QString possiblePath = ":/QssFiles/lxchart_styles.qss"; // 资源文件路径

    QString styleSheet;
    QString usedPath;

    // 尝试加载QSS文件
    QFile file(possiblePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream stream(&file);
        styleSheet = stream.readAll();
        usedPath = possiblePath;
        file.close();
    }

    if (!styleSheet.isEmpty())
    {
        // 应用样式到当前控件
        this->setStyleSheet(styleSheet);
        qDebug() << "LxChart样式加载成功，路径:" << usedPath;
    }
    else
    {
        qWarning() << "LxChart样式文件未找到，尝试的路径:" << possiblePath;
        // 应用基本的内联样式作为后备
        // applyFallbackStyles();
    }
}

/**
 * @brief 处理导出数据请求
 * @param uniqueId 要导出的曲线唯一ID
 */
void LxChart::handleExportData(const QString &uniqueId)
{
    qDebug() << "LxChart::handleExportData: 开始导出数据，UniqueID:" << uniqueId;

    // 查找对应的LxChartData
    LxChartData *chartData = nullptr;
    for (LxChartData *data : m_chartDataVec)
    {
        if (data && data->getUniqueID() == uniqueId)
        {
            chartData = data;
            break;
        }
    }

    if (!chartData)
    {
        QMessageBox::warning(this, "错误", "未找到对应的曲线数据。");
        return;
    }

    // 弹出文件保存对话框
    QString defaultFileName = QString("%1_export.xlsx").arg(chartData->getTitle());
    QString fileName = QFileDialog::getSaveFileName(
        this, "导出数据", QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/" + defaultFileName, "Excel文件 (*.xlsx)");

    if (fileName.isEmpty())
    {
        return; // 用户取消了保存
    }

    // 执行导出操作
    exportChartDataToExcel(chartData, fileName);
}

/**
 * @brief 将曲线数据导出到Excel文件
 * @param chartData 要导出的曲线数据
 * @param fileName 导出文件名
 */
void LxChart::exportChartDataToExcel(LxChartData *chartData, const QString &fileName)
{
    if (!chartData)
    {
        QMessageBox::warning(this, "错误", "曲线数据为空。");
        return;
    }

    try
    {
        // 创建Excel文档
        QXlsx::Document xlsx;

        // 获取曲线数据
        QVector<QPointF> dataPoints = chartData->getData();
        if (dataPoints.isEmpty())
        {
            QMessageBox::warning(this, "警告", "曲线数据为空，无法导出。");
            return;
        }

        // 根据轨迹类型确定X轴标题
        QString xAxisTitle;
        GlobalEnums::TrackType trackType = chartData->getTrackType();
        if (trackType == GlobalEnums::TrackType::TIC || trackType == GlobalEnums::TrackType::XIC)
        {
            xAxisTitle = "Time(min)";
        }
        else
        {
            xAxisTitle = "Mass/Charge(Da)";
        }

        // Sheet1: 导出曲线数据
        xlsx.selectSheet("Sheet1");
        xlsx.write(1, 1, xAxisTitle);
        xlsx.write(1, 2, "Intensity");

        for (int i = 0; i < dataPoints.size(); ++i)
        {
            xlsx.write(i + 2, 1, dataPoints[i].x());
            xlsx.write(i + 2, 2, dataPoints[i].y());
        }

        qDebug() << "LxChart::exportChartDataToExcel: Sheet1数据导出完成，数据点数量:" << dataPoints.size();

        // Sheet2: 导出峰数据
        std::vector<Peak> peaks = chartData->getPeakVec();
        if (!peaks.empty())
        {
            xlsx.addSheet("Sheet2");
            xlsx.selectSheet("Sheet2");

            // 写入峰数据表头
            xlsx.write(1, 1, "Index");
            if (trackType == GlobalEnums::TrackType::TIC || trackType == GlobalEnums::TrackType::XIC)
            {
                xlsx.write(1, 2, "Time(min)");
            }
            else
            {
                xlsx.write(1, 2, "Mass/Charge(Da)");
            }
            xlsx.write(1, 3, "Area");
            xlsx.write(1, 4, "Height");
            xlsx.write(1, 5, "Start");
            xlsx.write(1, 6, "End");
            xlsx.write(1, 7, "Width");
            xlsx.write(1, 8, "Width at 50%");
            xlsx.write(1, 9, "S/N");

            // 写入峰数据
            for (size_t i = 0; i < peaks.size(); ++i)
            {
                const Peak &peak = peaks[i];
                xlsx.write(i + 2, 1, static_cast<int>(i + 1));                                  // Index
                xlsx.write(i + 2, 2, QString::number(peak.pTop.x(), 'f', 2));                   // Time/Mass
                xlsx.write(i + 2, 3, QString::number(peak.area, 'f', 2));                       // Area
                xlsx.write(i + 2, 4, QString::number(peak.height, 'f', 2));                     // Height
                xlsx.write(i + 2, 5, QString::number(peak.pStart.x(), 'f', 2));                 // Start
                xlsx.write(i + 2, 6, QString::number(peak.pEnd.x(), 'f', 2));                   // End
                xlsx.write(i + 2, 7, QString::number(peak.pEnd.x() - peak.pStart.x(), 'f', 2)); // Width
                xlsx.write(i + 2, 8, QString::number(peak.fwhm, 'f', 2));                       // Width at 50%
                xlsx.write(i + 2, 9, QString::number(peak.snr, 'f', 2));                        // S/N
            }

            qDebug() << "LxChart::exportChartDataToExcel: Sheet2峰数据导出完成，峰数量:" << peaks.size();
        }

        // 保存文件
        if (xlsx.saveAs(fileName))
        {
            QMessageBox::information(this, "成功", QString("数据已成功导出到：\n%1").arg(fileName));
            qDebug() << "LxChart::exportChartDataToExcel: 文件保存成功:" << fileName;
        }
        else
        {
            QMessageBox::warning(this, "错误", "文件保存失败，请检查文件路径和权限。");
            qDebug() << "LxChart::exportChartDataToExcel: 文件保存失败:" << fileName;
        }
    }
    catch (const std::exception &e)
    {
        QMessageBox::critical(this, "错误", QString("导出过程中发生异常：%1").arg(e.what()));
        qDebug() << "LxChart::exportChartDataToExcel: 异常:" << e.what();
    }
    catch (...)
    {
        QMessageBox::critical(this, "错误", "导出过程中发生未知异常。");
        qDebug() << "LxChart::exportChartDataToExcel: 未知异常";
    }
}

// ==================== 🎯 自定义滑块实现函数 ====================

/**
 * @brief 初始化自定义滑块
 */
void LxChart::initCustomSlider()
{
    // 🎯 创建自定义滑块控件，父控件设置为chartView以便正确定位
    m_customSlider = new QLabel(m_chartView);
    m_customSlider->setFixedSize(m_sliderImageSize, m_sliderImageSize);

    // 🎯 直接加载SVG图片，充满整个QLabel
    QPixmap pixmap(":/Icons/LxChart/Slider.svg");
    if (!pixmap.isNull())
    {
        // 🎯 缩放到指定大小，忽略宽高比，充满整个QLabel
        pixmap = pixmap.scaled(m_sliderImageSize, m_sliderImageSize, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
    }
    else
    {
        // 如果SVG加载失败，创建一个简单的黑色三角形
        pixmap = QPixmap(m_sliderImageSize, m_sliderImageSize);
        pixmap.fill(Qt::transparent);
        QPainter painter(&pixmap);
        painter.setRenderHint(QPainter::Antialiasing);
        painter.setBrush(QBrush(Qt::black));
        painter.setPen(QPen(Qt::black, 1));

        // 🎯 创建三角形路径（右侧箭头），尖角在中心且紧贴右边缘
        QPolygonF triangle;
        double size = m_sliderImageSize;
        triangle << QPointF(0, 0)                 // 左上
                 << QPointF(size - 1, size * 0.5) // 右中（箭头尖端，紧贴右边缘）
                 << QPointF(0, size);             // 左下
        painter.drawPolygon(triangle);

        qDebug() << "LxChart::initCustomSlider: SVG加载失败，使用默认三角形";
    }

    m_customSlider->setPixmap(pixmap);
    m_customSlider->setToolTip("阈值滑块：拖动设置峰点显示阈值");

    // 设置鼠标追踪和样式
    m_customSlider->setMouseTracking(true);
    m_customSlider->setCursor(Qt::PointingHandCursor);

    // 🎯 设置滑块样式（移除调试边框）
    m_customSlider->setStyleSheet("");

    // 🎯 安装事件过滤器以处理鼠标事件
    m_customSlider->installEventFilter(this);

    // 🎯 延迟初始化滑块位置，确保数据完全加载后再设置
    QTimer::singleShot(100, this, [this]()
                       {
        // 初始化数据范围
        updateDataRange();

        // 🎯 默认阈值设置为0
        m_thresholdValue = 0.0;

        // 更新滑块位置
        updateCustomSliderPosition();

        qDebug() << "LxChart::initCustomSlider延迟初始化: 初始阈值=" << m_thresholdValue << "数据范围[" << m_dataMinY << "," << m_dataMaxY << "]"; });

    qDebug() << "LxChart::initCustomSlider: 自定义滑块初始化完成，图片大小:" << m_sliderImageSize;
}

/**
 * @brief 更新自定义滑块位置
 */
void LxChart::updateCustomSliderPosition()
{
    if (!m_customSlider || !m_chart || m_chartDataVec.isEmpty())
    {
        if (m_customSlider)
            m_customSlider->setVisible(false);
        return;
    }

    // 🎯 获取Y轴并检查类型
    QValueAxis *yAxis = nullptr;
    if (!m_chart->axes(Qt::Vertical).isEmpty())
    {
        yAxis = qobject_cast<QValueAxis *>(m_chart->axes(Qt::Vertical).first());
    }

    if (!yAxis)
    {
        // qDebug() << "LxChart::updateCustomSliderPosition: 无法获取Y轴";
        return;
    }

    // 🎯 获取当前Y轴的实际范围
    double yAxisMin = yAxis->min();
    double yAxisMax = yAxis->max();

    // 🎯 滑块始终显示
    m_customSlider->setVisible(true);

    // 🎯 安全获取默认曲线用于坐标转换
    if (!defaultSeries)
    {
        defaultSeries = getDefaultSeries();
    }

    // 🎯 双重检查defaultSeries的有效性
    if (!defaultSeries)
    {
        qDebug() << "LxChart::updateCustomSliderPosition: 无法获取默认曲线，跳过滑块更新";
        return;
    }

    // 🎯 检查defaultSeries是否还在图表中
    if (!m_chart->series().contains(defaultSeries))
    {
        qDebug() << "LxChart::updateCustomSliderPosition: 默认曲线已不在图表中，重新获取";
        defaultSeries = getDefaultSeries();
        if (!defaultSeries)
        {
            qDebug() << "LxChart::updateCustomSliderPosition: 重新获取默认曲线失败";
            return;
        }

        // 🎯 再次验证重新获取的defaultSeries
        if (!m_chart->series().contains(defaultSeries))
        {
            qDebug() << "LxChart::updateCustomSliderPosition: 重新获取的默认曲线仍然无效";
            defaultSeries = nullptr;
            return;
        }
    }

    // 🎯 将阈值Y坐标转换为屏幕坐标
    // 智能调整：如果阈值超出Y轴范围，则限制在Y轴范围内显示
    double displayThreshold = m_thresholdValue;
    if (displayThreshold < yAxisMin)
    {
        displayThreshold = yAxisMin; // 阈值太小，滑块显示在底端
    }
    else if (displayThreshold > yAxisMax)
    {
        displayThreshold = yAxisMax; // 阈值太大，滑块显示在顶端
    }

    QPointF dataPoint(0, displayThreshold);

    // 🎯 安全地进行坐标转换
    QPointF screenPoint;
    try
    {
        screenPoint = m_chart->mapToPosition(dataPoint, defaultSeries);
    }
    catch (...)
    {
        qDebug() << "LxChart::updateCustomSliderPosition: mapToPosition调用失败";
        return;
    }

    // 获取图表绘图区域
    QRectF plotArea = m_chart->plotArea();

    // 🎯 计算滑块位置：以QLabel右侧中心点（尖角）为基准
    // 让QLabel右边缘紧贴Y轴左侧（plotArea.left()就是Y轴位置）
    double sliderX = plotArea.left() - m_sliderImageSize;       // QLabel左边缘位置，使右边缘紧贴Y轴
    double sliderY = screenPoint.y() - m_sliderImageSize / 2.0; // QLabel上边缘位置，使中心对准阈值线

    // 🎯 严格限制滑块在Y轴绘图区域内
    // 确保QLabel右侧中心点（sliderY + sliderImageSize/2）在plotArea范围内
    // 所以sliderY的范围是：[plotArea.top() - sliderImageSize/2, plotArea.bottom() - sliderImageSize/2]
    sliderY = qMax(plotArea.top() - m_sliderImageSize / 2.0, qMin(plotArea.bottom() - m_sliderImageSize / 2.0, sliderY));

    m_customSlider->move(sliderX, sliderY);

    // 🎯 计算QLabel右侧中心点的实际位置用于调试
    double rightCenterY = sliderY + m_sliderImageSize / 2.0;

    // qDebug() << "LxChart::updateCustomSliderPosition: 滑块位置=(" << sliderX << "," << sliderY << ")"
    //          << "右侧中心点Y=" << rightCenterY << "阈值线Y=" << screenPoint.y() << "plotArea=" << plotArea << "阈值=" << m_thresholdValue << "Y轴范围["
    //          << yAxisMin << "," << yAxisMax << "]";

    // 🎯 计算当前滑块在Y轴上的百分比位置
    double yPercent = (plotArea.bottom() - (sliderY + m_sliderImageSize / 2.0)) / plotArea.height();
    yPercent = qMax(0.0, qMin(1.0, yPercent));

    // qDebug() << "LxChart::updateCustomSliderPosition: 滑块Y百分比=" << yPercent
    //          << "阈值=" << m_thresholdValue << "Y轴范围[" << yAxisMin << "," << yAxisMax << "]";

    // qDebug() << "LxChart::updateCustomSliderPosition: 滑块位置更新 阈值=" << m_thresholdValue
    //          << "屏幕位置=(" << sliderX << "," << sliderY << ")";
}

/**
 * @brief 更新数据范围（用于滑块移动范围限制）
 * 🎯 现在基于Y轴范围而不是数据范围，确保滑块移动范围与Y轴一致
 */
void LxChart::updateDataRange()
{
    if (m_chartDataVec.isEmpty() || !m_chart || m_chart->axes(Qt::Vertical).isEmpty())
    {
        m_dataMinY = 0.0;
        m_dataMaxY = 1.0;
        // qDebug() << "LxChart::updateDataRange: 数据为空或Y轴不存在，使用默认范围[0,1]";
        return;
    }

    // 🎯 获取Y轴的当前范围作为滑块移动范围
    QValueAxis *yAxis = qobject_cast<QValueAxis *>(m_chart->axes(Qt::Vertical).first());
    if (yAxis)
    {
        m_dataMinY = yAxis->min();
        m_dataMaxY = yAxis->max();
        // qDebug() << "LxChart::updateDataRange: 从Y轴获取范围[" << m_dataMinY << "," << m_dataMaxY << "]";
    }
    else
    {
        // 如果无法获取Y轴，则计算所有数据的Y值范围作为备用
        bool firstData = true;
        for (LxChartData *chartData : m_chartDataVec)
        {
            if (!chartData)
                continue;

            QVector<QPointF> dataPoints = chartData->getData();
            for (const QPointF &point : dataPoints)
            {
                if (firstData)
                {
                    m_dataMinY = point.y();
                    m_dataMaxY = point.y();
                    firstData = false;
                }
                else
                {
                    m_dataMinY = qMin(m_dataMinY, point.y());
                    m_dataMaxY = qMax(m_dataMaxY, point.y());
                }
            }
        }
    }

    // 确保范围有效
    if (m_dataMinY >= m_dataMaxY)
    {
        m_dataMaxY = m_dataMinY + 1.0;
    }

    // qDebug() << "LxChart::updateDataRange: 滑块移动范围更新为Y轴范围 [" << m_dataMinY << "," << m_dataMaxY << "]";
}

/**
 * @brief 为MRM数据设置特殊的X轴标签显示
 * @param valueAxisX X轴指针
 * @param mzValues MRM数据的m/z值列表
 */
void LxChart::setupMrmXAxisLabels(QValueAxis *valueAxisX, const QVector<qreal> &mzValues)
{
    if (!valueAxisX || mzValues.isEmpty())
    {
        qDebug() << "LxChart::setupMrmXAxisLabels: 参数无效，跳过设置";
        return;
    }

    try
    {
        // qDebug() << "LxChart::setupMrmXAxisLabels: 为MRM数据隐藏X轴刻度";
        // qDebug() << "  数据点数:" << mzValues.size() << "，m/z范围: [" << mzValues.first() << "," << mzValues.last() << "]";

        int dataCount = mzValues.size();

        // 🎯 安全地设置刻度数量
        if (dataCount > 0)
        {
            valueAxisX->setTickCount(dataCount + 2);
            valueAxisX->setMinorTickCount(0);
        }

        // 隐藏X轴标签和刻度，m/z信息将显示在顶部标签中
        valueAxisX->setLabelsVisible(false);
        valueAxisX->setLineVisible(true);     // 保持轴线可见
        valueAxisX->setGridLineVisible(true); // 保持网格线可见

        // 设置X轴标题
        valueAxisX->setTitleText("m/z");

        // qDebug() << "LxChart::setupMrmXAxisLabels: X轴刻度隐藏完成";
    }
    catch (...)
    {
        qDebug() << "LxChart::setupMrmXAxisLabels: 设置X轴标签时出现异常";
    }
}

/**
 * @brief 为MRM柱状图添加标签（顶部显示Y值，底部显示m/z值）
 * @param chartData 图表数据
 * @param dataPoints 数据点列表
 */
void LxChart::addMrmBarLabels(LxChartData *chartData, const QVector<QPointF> &dataPoints)
{
    if (!chartData || !chartData->hasMrmCategories() || !m_chart)
    {
        qDebug() << "LxChart::addMrmBarLabels: 参数检查失败";
        return;
    }

    // 清除之前的MRM标签
    for (QGraphicsTextItem *label : m_mrmIntensityLabels)
    {
        if (label && m_chart->scene())
        {
            m_chart->scene()->removeItem(label);
            delete label;
        }
    }
    m_mrmIntensityLabels.clear();

    qDebug() << "LxChart::addMrmBarLabels: 开始添加MRM标签，原始数据点数:" << dataPoints.size();

    QRectF plotArea = m_chart->plotArea();
    qDebug() << "LxChart::addMrmBarLabels: 图表区域:" << plotArea;

    if (plotArea.isEmpty())
    {
        qDebug() << "LxChart::addMrmBarLabels: 图表绘图区域为空，跳过处理（将在UpdateGlobalRange中统一创建）";
        return;
    }

    // 获取MRM类别标签（m/z值）
    QStringList mrmCategories = chartData->getMrmCategories();
    int dataCount = mrmCategories.size();

    qDebug() << "LxChart::addMrmBarLabels: MRM类别数:" << dataCount << "，类别:" << mrmCategories;

    for (int i = 0; i < dataCount; ++i)
    {
        // 🎯 计算垂直线在绘图区域中的X位置，使用分组位置
        // 垂直线的X坐标为MASS起始位置 + 棒子索引
        qreal lineXPos = chartData->getMassStartPosition() + i;
        qreal xRatio = (lineXPos - m_globalMinX) / (m_globalMaxX - m_globalMinX);
        qreal lineCenterX = plotArea.left() + xRatio * plotArea.width();

        // 从原始数据获取强度值
        qreal intensity = 0;
        if (i < dataPoints.size())
        {
            intensity = dataPoints[i].y();
        }

        // 计算强度值在绘图区域中的Y位置
        qreal yRatio = (intensity - m_globalMinY) / (m_globalMaxY - m_globalMinY);
        qreal intensityY = plotArea.bottom() - yRatio * plotArea.height();

        qDebug() << "  垂直线" << i << "详细计算:";
        qDebug() << "    lineXPos:" << lineXPos << "，xRatio:" << xRatio << "，lineCenterX:" << lineCenterX;
        qDebug() << "    intensity:" << intensity << "，yRatio:" << yRatio << "，intensityY:" << intensityY;
        qDebug() << "    全局范围: X[" << m_globalMinX << "," << m_globalMaxX << "] Y[" << m_globalMinY << "," << m_globalMaxY << "]";

        // 计算底部位置（Y=0对应的像素位置）
        qreal bottomYRatio = (0 - m_globalMinY) / (m_globalMaxY - m_globalMinY);
        qreal bottomY = plotArea.bottom() - bottomYRatio * plotArea.height();

        // 在垂直线顶部添加 "m/z, 强度" 格式的标签
        QGraphicsTextItem *intensityLabel = new QGraphicsTextItem();
        QString labelText = QString("%1, %2").arg(mrmCategories[i]).arg(QString::number(intensity, 'f', 0));
        intensityLabel->setPlainText(labelText);
        intensityLabel->setDefaultTextColor(Qt::red);

        QFont intensityFont = intensityLabel->font();
        intensityFont.setPointSize(10);
        intensityFont.setBold(true);
        intensityLabel->setFont(intensityFont);

        QRectF intensityRect = intensityLabel->boundingRect();
        intensityLabel->setPos(lineCenterX - intensityRect.width() / 2, intensityY - intensityRect.height() - 3);
        m_chart->scene()->addItem(intensityLabel);
        m_mrmIntensityLabels.append(intensityLabel); // 存储标签

        qDebug() << "LxChart::addMrmBarLabels: 垂直线" << i << "，标签:" << labelText << "，lineCenterX:" << lineCenterX
                 << "，标签Y:" << (intensityY - intensityRect.height() - 3);
    }

    qDebug() << "LxChart::addMrmBarLabels: 所有MRM标签添加完成";
}

/**
 * @brief 更新MRM标签位置（在图表缩放或尺寸改变时调用）
 */
void LxChart::updateMrmLabelsPosition()
{
    // qDebug() << "LxChart::updateMrmLabelsPosition: 开始更新MRM标签位置";

    if (m_mrmIntensityLabels.isEmpty())
    {
        // qDebug() << "LxChart::updateMrmLabelsPosition: 没有MRM标签需要更新";
        return;
    }

    // 找到MRM数据和对应的系列
    LxChartData *mrmChartData = nullptr;
    QVector<QPointF> mrmDataPoints;
    QAbstractSeries *mrmSeries = nullptr;

    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->isMrmData())
        {
            mrmChartData = chartData;
            mrmDataPoints = chartData->getData(); // 原始数据点（m/z, intensity）

            // 找到对应的系列
            QString uniqueID = chartData->getUniqueID();
            for (QAbstractSeries *series : m_chart->series())
            {
                if (series->name() == uniqueID)
                {
                    mrmSeries = series;
                    break;
                }
            }
            break;
        }
    }

    if (!mrmChartData || mrmDataPoints.isEmpty() || !mrmSeries)
    {
        qDebug() << "LxChart::updateMrmLabelsPosition: 未找到MRM数据或系列";
        return;
    }

    // 获取MRM类别标签
    QStringList mrmCategories = mrmChartData->getMrmCategories();
    if (mrmCategories.size() != mrmDataPoints.size())
    {
        qDebug() << "LxChart::updateMrmLabelsPosition: MRM类别数量与数据点数量不匹配";
        return;
    }

    // 获取当前图表的可视范围
    QRectF plotArea = m_chart->plotArea();

    // 🎯 获取当前坐标轴的实际显示范围
    QAbstractAxis *axisX = m_chart->axes(Qt::Horizontal).at(0);
    QAbstractAxis *axisY = m_chart->axes(Qt::Vertical).at(0);
    QValueAxis *valueAxisX = qobject_cast<QValueAxis *>(axisX);
    QValueAxis *valueAxisY = qobject_cast<QValueAxis *>(axisY);

    if (!valueAxisX || !valueAxisY)
    {
        qDebug() << "LxChart::updateMrmLabelsPosition: 无法获取坐标轴";
        return;
    }

    qreal currentMinX = valueAxisX->min();
    qreal currentMaxX = valueAxisX->max();
    qreal currentMinY = valueAxisY->min();
    qreal currentMaxY = valueAxisY->max();

    qDebug() << "LxChart::updateMrmLabelsPosition: 开始更新" << m_mrmIntensityLabels.size() << "个标签位置";
    qDebug() << "LxChart::updateMrmLabelsPosition: 当前坐标轴范围 X[" << currentMinX << "," << currentMaxX << "] Y[" << currentMinY << "," << currentMaxY
             << "]";

    // 更新每个标签的位置
    for (int i = 0; i < m_mrmIntensityLabels.size() && i < mrmDataPoints.size(); ++i)
    {
        QGraphicsTextItem *label = m_mrmIntensityLabels[i];
        if (!label)
            continue;

        // 🎯 关键修复：使用当前坐标轴范围而不是全局范围来计算位置
        // 🎯 计算垂直线在绘图区域中的X位置，使用分组位置
        qreal lineXPos = mrmChartData->getMassStartPosition() + i; // 使用MASS起始位置 + 棒子索引
        qreal xRatio = (lineXPos - currentMinX) / (currentMaxX - currentMinX);
        qreal lineCenterX = plotArea.left() + xRatio * plotArea.width();

        // 从原始数据获取强度值
        qreal intensity = mrmDataPoints[i].y();

        // 计算强度值在绘图区域中的Y位置
        qreal yRatio = (intensity - currentMinY) / (currentMaxY - currentMinY);
        qreal intensityY = plotArea.bottom() - yRatio * plotArea.height();

        // 🎯 更新标签文本内容，确保显示正确的Y值
        QString labelText = QString("%1, %2").arg(mrmCategories[i]).arg(QString::number(intensity, 'f', 0));
        label->setPlainText(labelText);

        // 计算标签位置（在顶点上方）
        QRectF labelRect = label->boundingRect();
        QPointF labelPos(lineCenterX - labelRect.width() / 2, intensityY - labelRect.height() - 3);

        // 🎯 检查垂直线是否在当前坐标轴可视范围内
        bool lineInAxisRange = (lineXPos >= currentMinX && lineXPos <= currentMaxX && intensity >= currentMinY && intensity <= currentMaxY);

        // 更新位置和可见性
        label->setPos(labelPos);
        label->setVisible(lineInAxisRange && mrmSeries->isVisible());

        qDebug() << "  标签" << i << "位置更新: 原始数据点" << mrmDataPoints[i] << "-> 垂直线X:" << lineXPos << "-> 屏幕X:" << lineCenterX
                 << "-> 强度Y:" << intensityY << "-> 标签位置" << labelPos << "-> 坐标轴范围内:" << lineInAxisRange
                 << "-> 最终可见:" << (lineInAxisRange && mrmSeries->isVisible());
    }

    qDebug() << "LxChart::updateMrmLabelsPosition: MRM标签位置更新完成";
}

/**
 * @brief 清除所有MRM标签
 */
void LxChart::clearMrmLabels()
{
    // qDebug() << "LxChart::clearMrmLabels: 开始清除MRM标签，当前标签数量:" << m_mrmLabels.size();

    // 🎯 安全地清除所有MRM强度标签（原有的）
    for (QGraphicsTextItem *label : m_mrmIntensityLabels)
    {
        if (label)
        {
            try
            {
                if (label->scene())
                {
                    label->scene()->removeItem(label);
                }
                // 🎯 使用deleteLater延迟删除
                label->deleteLater();
            }
            catch (...)
            {
                qDebug() << "LxChart::clearMrmLabels: 清理强度标签时出现异常";
            }
        }
    }
    m_mrmIntensityLabels.clear();

    // 🎯 更安全地清除所有MRM顶点标签
    QList<QGraphicsTextItem *> labelsToDelete = m_mrmLabels; // 复制列表
    m_mrmLabels.clear();                                     // 立即清空原列表，避免重复访问

    for (QGraphicsTextItem *label : labelsToDelete)
    {
        if (label)
        {
            try
            {
                // 🎯 检查标签是否还有效
                if (label->scene())
                {
                    label->scene()->removeItem(label);
                }

                // 安全删除对象
                label->deleteLater();
            }
            catch (...)
            {
                qDebug() << "LxChart::clearMrmLabels: 清理标签时出现异常";
            }
        }
    }

    // 🎯 重置标签创建标志
    m_mrmLabelsCreated = false;

    // qDebug() << "LxChart::clearMrmLabels: MRM标签清除完成";
}

// 🎯 开始MASS批量加载
void LxChart::startMassBatch(int expectedCount)
{
    QMutexLocker locker(&m_massCountMutex);

    m_expectedMassCount = expectedCount;
    m_completedMassCount = 0;
    m_pendingMassData.clear();
    m_isBatchLoading = true;

    qDebug() << "LxChart::startMassBatch: 开始MASS批量加载，预期数量:" << expectedCount;
}

// 🎯 MASS加载完成通知
void LxChart::onMassCompleted(LxChartData *massData)
{
    if (!massData || !massData->isMrmData())
        return;

    QMutexLocker locker(&m_massCountMutex);

    m_pendingMassData.append(massData);
    m_completedMassCount++;

    qDebug() << "LxChart::onMassCompleted: MASS加载完成，路径:" << massData->getParamPath() << "，已完成:" << m_completedMassCount << "/"
             << m_expectedMassCount;

    // 检查是否所有MASS都已加载完成
    if (m_completedMassCount >= m_expectedMassCount)
    {
        qDebug() << "LxChart::onMassCompleted: 所有MASS数据已加载完成，开始显示";
        m_isBatchLoading = false;

        // 🎯 在主线程中显示所有MASS数据
        QTimer::singleShot(0, this, [this]()
                           {
            // 获取待处理的数据（避免在lambda中持有锁）
            QVector<LxChartData *> dataToProcess;
            {
                QMutexLocker locker(&m_massCountMutex);
                dataToProcess = m_pendingMassData;
            }

            // 先移除所有现有的MASS数据（内部会清理MRM标签）
            removeAllMassData();

            // 🎯 统一分配位置（从位置1开始，首尾留空）
            int currentPosition = 1; // 从位置1开始，位置0留空

            // 先为所有MASS数据分配位置
            for (LxChartData *massData : dataToProcess) {
                if (massData) {
                    // 计算棒子数量
                    QVector<QPointF> dataPoints = massData->getData();
                    int barCount = 0;
                    for (const QPointF &point : dataPoints) {
                        if (!std::isnan(point.x()) && !std::isinf(point.x()) && !std::isnan(point.y()) && !std::isinf(point.y())) {
                            barCount++;
                        }
                    }

                    // 设置位置信息
                    massData->setMassStartPosition(currentPosition);
                    massData->setMassBarCount(barCount);

                    qDebug() << "LxChart::onMassCompleted: 分配位置，路径:" << massData->getParamPath() << "，起始位置:" << currentPosition
                             << "，棒子数量:" << barCount;

                    // 更新位置
                    currentPosition += barCount;
                }
            }

            // 添加新的MASS数据
            for (LxChartData *massData : dataToProcess) {
                if (massData) {
                    // 直接调用原始的AddLxChartData，不走批量逻辑
                    m_chartDataVec.append(massData);

                    // 🎯 创建系列并添加到图表（createSeries内部会处理现有系列）
                    massData->createSeries(this, massData->getUniqueID());

                    // 🎯 统一添加所有系列到图表
                    QList<QAbstractSeries *> allSeries = massData->getAllSeries();
                    if (!allSeries.isEmpty()) {
                        QColor massColor = generateRandomColor();

                        for (QAbstractSeries *series : allSeries) {
                            if (series) {
                                try {
                                    // 检查系列是否已经在图表中
                                    if (!m_chart->series().contains(series)) {
                                        m_chart->addSeries(series);

                                        // 附加坐标轴
                                        if (!m_chart->axes(Qt::Horizontal).isEmpty() && !m_chart->axes(Qt::Vertical).isEmpty()) {
                                            series->attachAxis(m_chart->axes(Qt::Horizontal).first());
                                            series->attachAxis(m_chart->axes(Qt::Vertical).first());
                                        }

                                        // 设置统一颜色
                                        if (QLineSeries *lineSeries = qobject_cast<QLineSeries *>(series)) {
                                            lineSeries->setColor(massColor);
                                        }
                                    } else {
                                        qDebug() << "LxChart::onMassCompleted: 系列已在图表中，跳过添加";
                                    }
                                } catch (...) {
                                    qDebug() << "LxChart::onMassCompleted: 添加系列时出现异常，忽略";
                                }
                            }
                        }
                    }
                }
            }

            // 🎯 更新总棒子数
            int totalBars = currentPosition - 1; // 减去起始位置1
            {
                QMutexLocker locker(&m_barCountMutex);
                m_totalBarCount = totalBars;
            }

            qDebug() << "LxChart::onMassCompleted: 更新总棒子数为:" << totalBars;

            // 🎯 安全地更新全局范围
            try {
                UpdateGlobalRange();
            } catch (...) {
                qDebug() << "LxChart::onMassCompleted: 更新全局范围时出现异常";
            }

            // 🎯 创建MRM顶点标签（按照峰标签逻辑）
            QTimer::singleShot(50, this, [this]() {
                qDebug() << "LxChart::onMassCompleted: 开始创建MRM顶点标签";
                updateVertexLabelsPosition();
                qDebug() << "LxChart::onMassCompleted: MRM顶点标签创建完成";
            });

            // 清理状态
            {
                QMutexLocker locker(&m_massCountMutex);
                m_pendingMassData.clear();
                m_expectedMassCount = 0;
                m_completedMassCount = 0;
            }

            qDebug() << "LxChart::onMassCompleted: 批量显示完成"; });
    }
}

// 🎯 移除所有MASS数据
void LxChart::removeAllMassData()
{
    qDebug() << "LxChart::removeAllMassData: 开始移除所有MASS数据";

    // 从后往前遍历，避免索引问题
    for (int i = m_chartDataVec.size() - 1; i >= 0; i--)
    {
        LxChartData *chartData = m_chartDataVec[i];
        if (chartData && chartData->isMrmData())
        {
            qDebug() << "LxChart::removeAllMassData: 移除MASS数据，路径:" << chartData->getParamPath();

            // 🎯 先从管理数组中移除，避免其他地方访问
            m_chartDataVec.removeAt(i);

            // 🎯 从图表中移除所有系列
            QList<QAbstractSeries *> allSeries = chartData->getAllSeries();
            for (QAbstractSeries *series : allSeries)
            {
                if (series && m_chart->series().contains(series))
                {
                    m_chart->removeSeries(series);
                }
            }

            // 🎯 统一清理所有系列对象
            chartData->clearAllSeries();

            // 🎯 使用deleteLater延迟删除数据对象，避免虚函数调用
            chartData->deleteLater();
        }
    }

    // 🎯 强制清除MRM标签
    clearMrmLabels();

    // 🎯 重置棒状图计数器
    {
        QMutexLocker locker(&m_barCountMutex);
        m_totalBarCount = 0;
    }

    qDebug() << "LxChart::removeAllMassData: 所有MASS数据移除完成";
}

// 🎯 统一更新所有MASS系列样式
void LxChart::updateAllMassSeriesStyle()
{
    qDebug() << "LxChart::updateAllMassSeriesStyle: 开始更新所有MASS系列样式";

    for (LxChartData *chartData : m_chartDataVec)
    {
        if (chartData && chartData->isMrmData())
        {
            // 获取当前颜色和线宽
            QColor currentColor = chartData->getLineColor();
            double currentLineWidth = OptionsDialogSettings::getSpectrumLineWidth();
            QPen pen(currentColor, currentLineWidth);

            // 🎯 更新主系列样式
            if (QLineSeries *mainSeries = qobject_cast<QLineSeries *>(chartData->getSeries()))
            {
                mainSeries->setPen(pen);
            }

            // 🎯 更新所有MRM棒子系列样式
            for (QAbstractSeries *barSeries : chartData->getMrmBarSeries())
            {
                if (QLineSeries *lineSeries = qobject_cast<QLineSeries *>(barSeries))
                {
                    lineSeries->setPen(pen);
                    lineSeries->setColor(currentColor);
                }
            }

            qDebug() << "LxChart::updateAllMassSeriesStyle: 更新MASS样式，路径:" << chartData->getParamPath() << "，颜色:" << currentColor.name()
                     << "，线宽:" << currentLineWidth;
        }
    }

    qDebug() << "LxChart::updateAllMassSeriesStyle: 所有MASS系列样式更新完成";
}

// 🎯 更新棒状图计数器
void LxChart::updateBarCount(int delta)
{
    QMutexLocker locker(&m_barCountMutex);
    m_totalBarCount += delta;

    if (m_totalBarCount < 0)
        m_totalBarCount = 0;

    qDebug() << "LxChart::updateBarCount: 更新棒状图计数，变化:" << delta << "，当前总数:" << m_totalBarCount;
}

// 🎯 获取当前总棒状图数量
int LxChart::getTotalBarCount() const
{
    QMutexLocker locker(&m_barCountMutex);
    return m_totalBarCount;
}

/**
 * @brief 根据LabelFiledType生成峰标签文本
 * @param peak 峰数据
 * @param labelType 标签类型
 * @param trackType 曲线类型
 * @param chartData 曲线数据（用于计算X轴间隔）
 * @return 格式化的峰标签文本
 */
QString LxChart::generatePeakLabelText(const Peak &peak, LabelFiledType labelType, GlobalEnums::TrackType trackType, LxChartData *chartData)
{
    QString peakText;
    switch (labelType)
    {
    case LabelFiledType::Time:
        peakText = QString("%1").arg(peak.pTop.x(), 0, 'f', 2);
        break;

    case LabelFiledType::Area:
        peakText = QString("%1").arg(peak.area, 0, 'f', 2);
        break;

    case LabelFiledType::Height:
        peakText = QString("%1").arg(peak.height, 0, 'f', 2);
        break;

    case LabelFiledType::Noise:
        peakText = QString("%1").arg(peak.snr, 0, 'f', 2);
        break;

    case LabelFiledType::MZ:
        // MZ主要用于质谱数据
        if (trackType == GlobalEnums::TrackType::MS)
        {
            peakText = QString("%1").arg(peak.pTop.x(), 0, 'f', 2);
        }
        else
        {
            // 对于非质谱数据，显示时间
            peakText = QString("%1").arg(peak.pTop.x(), 0, 'f', 2);
        }
        break;

    case LabelFiledType::WateLength:
        // 波长主要用于DAD数据，暂时显示X坐标
        peakText = QString("%1").arg(peak.pTop.x(), 0, 'f', 2);
        break;

    case LabelFiledType::FWHM:
        // 🎯 半峰宽显示：需要将点数转换为实际的时间/m/z差值
        // 半峰宽 = 半峰宽值 × (曲线第二个点 - 第一个点的差)
        {
            double xInterval = 1.0; // 默认间隔

            // 如果提供了曲线数据，计算实际的X轴间隔
            if (chartData != nullptr)
            {
                QVector<double> xData = chartData->getDataX();
                if (xData.size() >= 2)
                {
                    // 计算第二个点与第一个点的差值作为间隔
                    xInterval = xData[1] - xData[0];
                    qDebug() << "🔍 FWHM模式调试: X轴前两个点 [" << xData[0] << "," << xData[1] << "], 间隔=" << xInterval;
                }
                else
                {
                    qDebug() << "⚠️ FWHM模式调试: X轴数据点不足，数据点数=" << xData.size();
                }
            }
            else
            {
                qDebug() << "⚠️ FWHM模式调试: chartData为空，使用默认间隔";
            }

            double correctedFwhm = std::abs(peak.fwhm) * xInterval; // 🎯 使用绝对值确保为正数

            // 🔍 详细调试输出
            qDebug() << "==================== FWHM模式详细调试 ====================";
            qDebug() << "峰坐标:" << peak.pTop;
            qDebug() << "原始FWHM值(点数):" << peak.fwhm;
            qDebug() << "FWHM是否为负:" << (peak.fwhm < 0 ? "是" : "否");
            qDebug() << "X轴间隔(时间差):" << xInterval;
            qDebug() << "计算公式: |" << peak.fwhm << "| × " << xInterval << " = " << correctedFwhm;
            qDebug() << "修正后FWHM(实际单位):" << correctedFwhm;
            qDebug() << "========================================================";

            // 🎯 检查异常值
            if (peak.fwhm < 0)
            {
                qDebug() << "❌ 警告: 原始FWHM为负数! 峰坐标=" << peak.pTop << ", 原始FWHM=" << peak.fwhm << ", 已使用绝对值修正";
            }

            peakText = QString("%1").arg(correctedFwhm, 0, 'f', 2);
        }
        break;

    case LabelFiledType::All:
        // 显示所有信息，按行排列
        if (trackType == GlobalEnums::TrackType::MS)
        {
            // 质谱数据：m/z, 峰面积, 峰高, 半峰宽
            double xInterval = 1.0;
            if (chartData != nullptr)
            {
                QVector<double> xData = chartData->getDataX();
                if (xData.size() >= 2)
                {
                    xInterval = xData[1] - xData[0];
                }
            }
            double correctedFwhm = std::abs(peak.fwhm) * xInterval; // 🎯 使用绝对值确保为正数
            peakText = QString::number(peak.pTop.x(), 'f', 2) + "\n" + QString::number(peak.area, 'f', 2) + "\n" + QString::number(peak.height, 'f', 2) + "\n" + QString::number(correctedFwhm, 'f', 2);

            // 🔍 详细调试输出
            qDebug() << "================== All模式-质谱FWHM详细调试 ==================";
            qDebug() << "峰坐标:" << peak.pTop;
            qDebug() << "原始FWHM值(点数):" << peak.fwhm;
            qDebug() << "FWHM是否为负:" << (peak.fwhm < 0 ? "是" : "否");
            qDebug() << "X轴间隔(m/z差):" << xInterval;
            qDebug() << "计算公式: |" << peak.fwhm << "| × " << xInterval << " = " << correctedFwhm;
            qDebug() << "修正后FWHM(实际单位):" << correctedFwhm;
            qDebug() << "==============================================================";

            // 🎯 检查异常值
            if (peak.fwhm < 0)
            {
                qDebug() << "❌ 警告: All模式质谱原始FWHM为负数! 峰坐标=" << peak.pTop << ", 原始FWHM=" << peak.fwhm << ", 已使用绝对值修正";
            }
        }
        else
        {
            // 色谱数据：时间, 峰面积, 峰高, 信噪比, 半峰宽
            double xInterval = 1.0;
            if (chartData != nullptr)
            {
                QVector<double> xData = chartData->getDataX();
                if (xData.size() >= 2)
                {
                    xInterval = xData[1] - xData[0];
                    qDebug() << "🔍 All模式-色谱X轴数据调试:";
                    qDebug() << "   X轴数据总点数:" << xData.size();
                    qDebug() << "   前10个X轴点:" << xData.mid(0, qMin(10, xData.size()));
                    qDebug() << "   第1个点:" << xData[0];
                    qDebug() << "   第2个点:" << xData[1];
                    qDebug() << "   计算间隔:" << xData[1] << "-" << xData[0] << "=" << xInterval;

                    // 🎯 检查是否所有点都相同
                    bool allSame = true;
                    for (int i = 1; i < qMin(5, xData.size()); i++)
                    {
                        if (qAbs(xData[i] - xData[0]) > 0.0001)
                        {
                            allSame = false;
                            break;
                        }
                    }
                    if (allSame)
                    {
                        qDebug() << "⚠️ 警告: X轴数据前几个点都相同，可能数据有问题!";
                    }
                }
                else
                {
                    qDebug() << "⚠️ All模式-色谱FWHM调试: X轴数据点不足，数据点数=" << xData.size();
                }
            }
            else
            {
                qDebug() << "⚠️ All模式-色谱FWHM调试: chartData为空，使用默认间隔";
            }

            // 🎯 如果X轴间隔异常，尝试使用峰的实际坐标计算间隔
            if (xInterval <= 0.0001 || xInterval >= 10.0)
            {
                // 使用峰起点到终点的距离除以点数作为间隔估算
                double peakTimeSpan = peak.pEnd.x() - peak.pStart.x();
                int peakPointCount = peak.end - peak.start;
                if (peakPointCount > 0)
                {
                    double estimatedInterval = peakTimeSpan / peakPointCount;
                    qDebug() << "🔧 X轴间隔异常(" << xInterval << ")，使用峰坐标估算:";
                    qDebug() << "   峰时间跨度:" << peakTimeSpan << "(" << peak.pEnd.x() << "-" << peak.pStart.x() << ")";
                    qDebug() << "   峰点数:" << peakPointCount << "(" << peak.end << "-" << peak.start << ")";
                    qDebug() << "   估算间隔:" << estimatedInterval;
                    xInterval = estimatedInterval;
                }
            }

            double correctedFwhm = std::abs(peak.fwhm) * xInterval; // 🎯 使用绝对值确保为正数

            // 🔍 详细调试输出
            qDebug() << "================== All模式-色谱FWHM详细调试 ==================";
            qDebug() << "峰坐标:" << peak.pTop;
            qDebug() << "峰起点:" << peak.pStart;
            qDebug() << "峰终点:" << peak.pEnd;
            qDebug() << "峰高度:" << peak.height;
            qDebug() << "峰面积:" << peak.area;
            qDebug() << "信噪比:" << peak.snr;
            qDebug() << "原始FWHM值(点数):" << peak.fwhm;
            qDebug() << "FWHM是否为负:" << (peak.fwhm < 0 ? "是" : "否");
            qDebug() << "X轴间隔(时间差):" << xInterval;
            qDebug() << "计算公式: |" << peak.fwhm << "| × " << xInterval << " = " << correctedFwhm;
            qDebug() << "修正后FWHM(实际单位):" << correctedFwhm;
            qDebug() << "==============================================================";

            // 🎯 检查异常值
            if (peak.fwhm < 0)
            {
                qDebug() << "❌ 警告: All模式原始FWHM为负数! 峰坐标=" << peak.pTop << ", 原始FWHM=" << peak.fwhm << ", 已使用绝对值修正";
            }

            peakText = QString::number(peak.pTop.x(), 'f', 2) + "\n" + QString::number(peak.area, 'f', 2) + "\n" + QString::number(peak.height, 'f', 2) + "\n" +
                       QString::number(peak.snr, 'f', 2) + "\n" + QString::number(correctedFwhm, 'f', 2);
        }
        break;

    case LabelFiledType::Blank:
    default:
        // 空白或未知类型，返回空字符串
        peakText = "";
        break;
    }
    return peakText;
}

// 为自定义区域创建平均质谱
void LxChart::createAvgMassForCustomRange(int customRangeIndex)
{
    if (customRangeIndex < 0 || customRangeIndex >= vecCustomRange.size())
    {
        qDebug() << "LxChart::createAvgMassForCustomRange: 无效的自定义区域索引:" << customRangeIndex;
        return;
    }

    GlobalDefine::CustomRange &customRange = vecCustomRange[customRangeIndex];

    // 检查是否已经有平均质谱
    if (customRange.hasAvgMass || customRange.avgMassData)
    {
        qDebug() << "LxChart::createAvgMassForCustomRange: 自定义区域已有平均质谱，索引:" << customRangeIndex;
        return;
    }

    qDebug() << "LxChart::createAvgMassForCustomRange: 开始为自定义区域创建平均质谱，索引:" << customRangeIndex;

    // 直接使用AvgMassManager计算平均质谱
    AvgMassManager *avgMassManager = AvgMassManager::instance();
    if (!avgMassManager)
    {
        qDebug() << "LxChart::createAvgMassForCustomRange: 无法获取AvgMassManager实例";
        return;
    }

    // 计算平均质谱数据
    QVector<double> avgMassX, avgMassY;
    if (!avgMassManager->calculateCustomRangeAvgMass(customRange, this, avgMassX, avgMassY))
    {
        qDebug() << "LxChart::createAvgMassForCustomRange: 平均质谱计算失败";
        return;
    }

    // 创建平均质谱MASS数据对象，使用QUuid生成唯一虚拟路径
    QString uniqueId = QUuid::createUuid().toString(QUuid::WithoutBraces);
    QString avgMassPath = QString("VIRTUAL://AvgMass_%1").arg(uniqueId);
    MassChartData *massData = new MassChartData(avgMassPath, GlobalEnums::IonMode::NagativeIon, GlobalEnums::ScanMode::FullScan, "平均质谱", "平均质谱",
                                                "平均质谱", customRangeIndex); // 使用自定义区域索引作为事件ID

    if (massData)
    {
        // 设置标题为"平均质谱"
        massData->setTitle("平均质谱");

        // 设置数据
        massData->setDataThreadSafe(avgMassX, avgMassY);

        // 设置特殊的颜色，区别于普通MASS
        QColor avgMassColor(255, 165, 0); // 橙色
        massData->setLineColor(avgMassColor);
        massData->setOriginalColor(avgMassColor);

        // 关联到自定义区域
        customRange.avgMassData = massData;
        customRange.hasAvgMass = true;
        customRange.avgMassUniqueId = massData->getUniqueID();

        // 存储虚拟路径，便于后续识别和管理
        qDebug() << "LxChart::createAvgMassForCustomRange: 虚拟路径:" << avgMassPath;

        // 发射信号，让MainWindow将MASS数据添加到massChart
        emit sg_addAvgMassToMassChart(massData);

        qDebug() << "LxChart::createAvgMassForCustomRange: 平均质谱创建成功，MASS ID:" << customRange.avgMassUniqueId;
    }
    else
    {
        qDebug() << "LxChart::createAvgMassForCustomRange: MASS数据创建失败";
    }
}

// 删除自定义区域的平均质谱
void LxChart::removeAvgMassForCustomRange(int customRangeIndex)
{
    if (customRangeIndex < 0 || customRangeIndex >= vecCustomRange.size())
    {
        qDebug() << "LxChart::removeAvgMassForCustomRange: 无效的自定义区域索引:" << customRangeIndex;
        return;
    }

    GlobalDefine::CustomRange &customRange = vecCustomRange[customRangeIndex];

    if (!customRange.hasAvgMass && !customRange.avgMassData)
    {
        qDebug() << "LxChart::removeAvgMassForCustomRange: 自定义区域没有平均质谱，索引:" << customRangeIndex;
        return;
    }

    qDebug() << "LxChart::removeAvgMassForCustomRange: 开始删除平均质谱，索引:" << customRangeIndex;

    if (customRange.avgMassData)
    {
        QString massId = customRange.avgMassData->getUniqueID();

        // 发射信号，让MainWindow从massChart中移除MASS数据
        emit sg_removeAvgMassFromMassChart(massId);

        // 清理自定义区域中的引用
        customRange.avgMassData = nullptr;
        customRange.hasAvgMass = false;
        customRange.avgMassUniqueId.clear();

        qDebug() << "LxChart::removeAvgMassForCustomRange: 平均质谱删除成功，MASS ID:" << massId;
    }
}

void LxChart::updateAvgMassForCustomRange(int customRangeIndex)
{
    if (customRangeIndex < 0 || customRangeIndex >= vecCustomRange.size())
    {
        qDebug() << "LxChart::updateAvgMassForCustomRange: 无效的自定义区域索引:" << customRangeIndex;
        return;
    }

    GlobalDefine::CustomRange &customRange = vecCustomRange[customRangeIndex];

    if (!customRange.hasAvgMass || !customRange.avgMassData)
    {
        qDebug() << "LxChart::updateAvgMassForCustomRange: 自定义区域没有平均质谱数据，索引:" << customRangeIndex;
        return;
    }

    qDebug() << "LxChart::updateAvgMassForCustomRange: 开始更新平均质谱数据，索引:" << customRangeIndex;

    // 获取自定义区域的时间范围
    double startTime = customRange.m_StartLine->line().x1();
    double endTime = customRange.m_EndLine->line().x1();

    qDebug() << "LxChart::updateAvgMassForCustomRange: 自定义区域时间范围 [" << startTime << ", " << endTime << "]";

    // 调用AvgMassManager计算新的平均质谱
    AvgMassManager *avgMassManager = AvgMassManager::instance();
    if (!avgMassManager)
    {
        qDebug() << "LxChart::updateAvgMassForCustomRange: 无法获取AvgMassManager实例";
        return;
    }

    QVector<double> avgMassX, avgMassY;
    bool success = avgMassManager->calculateCustomRangeAvgMass(customRange, this, avgMassX, avgMassY);

    if (success && !avgMassX.isEmpty() && !avgMassY.isEmpty())
    {
        // 🎯 直接更新现有MASS数据对象的数据，不重新创建对象
        customRange.avgMassData->setDataThreadSafe(avgMassX, avgMassY);

        qDebug() << "LxChart::updateAvgMassForCustomRange: 平均质谱数据更新成功，数据点数:" << avgMassX.size();
        qDebug() << "LxChart::updateAvgMassForCustomRange: MASS ID:" << customRange.avgMassUniqueId;
    }
    else
    {
        qDebug() << "LxChart::updateAvgMassForCustomRange: 平均质谱计算失败";
    }
}
