#include "wh_peakSearch.h"
#include <vector>
#include <cmath>
#include <algorithm>
#include <numeric>
#include "wh_signal_smothing.h"
#include "wh_baselineCorrect.h"
// 移动平均基线校正
std::vector<double> baselineCorrection(const std::vector<double> &signal, int windowSize)
{
    std::vector<double> baseline(signal.size(), 0.0);
    for (int i = 0; i < signal.size(); ++i)
    {
        int start = std::max(0, i - windowSize / 2);
        int end = std::min(static_cast<int>(signal.size()), i + windowSize / 2);
        double sum = 0.0;
        for (int j = start; j < end; ++j)
        {
            sum += signal[j];
        }
        baseline[i] = sum / (end - start);
    }
    return baseline;
}
// 计算信号的导数（斜率）
std::vector<double> calculateDerivative(const std::vector<double> &signal)
{
    std::vector<double> derivative(signal.size(), 0.0);
    for (size_t i = 1; i < signal.size(); ++i)
    {
        derivative[i] = signal[i] - signal[i - 1];
    }
    return derivative;
}

// 动态噪声估计（计算信号的局部标准差）
double estimateNoiseLevel(const std::vector<double> &signal, int windowSize)
{
    std::vector<double> noiseLevels;
    for (int i = 0; i < signal.size(); i += windowSize)
    {
        int end = std::min(i + windowSize, static_cast<int>(signal.size()));
        double mean = std::accumulate(signal.begin() + i, signal.begin() + end, 0.0) / (end - i);
        double variance = 0.0;
        for (int j = i; j < end; ++j)
        {
            variance += std::pow(signal[j] - mean, 2);
        }
        noiseLevels.push_back(std::sqrt(variance / (end - i)));
    }
    // 返回噪声水平的中位数
    std::sort(noiseLevels.begin(), noiseLevels.end());
    return noiseLevels[noiseLevels.size() / 2];
}

// 线性插值辅助函数
// 在两点(x0,y0)和(x1,y1)之间，根据目标Y值(targetY)插值得到对应的X值
// 可用于索引插值（返回浮点索引）或实际坐标插值（返回实际X坐标）
double linearInterpolate(double x0, double y0, double x1, double y1, double targetY)
{
    return x0 + (targetY - y0) * (x1 - x0) / (y1 - y0);
}

// 计算半峰宽
double calculateFWHM(const std::vector<double> &signal, int peakStart, int peakTop, int peakEnd)
{
    double peakHeight = signal[peakTop];
    double halfHeight = peakHeight / 2.0;

    // 左侧半高位置
    int left = peakStart;
    while (left < peakTop && signal[left] < halfHeight)
        left++;

    double leftFWHM = linearInterpolate(left - 1, signal[left - 1], left, signal[left], halfHeight);

    // 右侧半高位置
    int right = peakTop;
    while (right < peakEnd && signal[right] > halfHeight)
        right++;

    double rightFWHM = linearInterpolate(right - 1, signal[right - 1], right, signal[right], halfHeight);

    return rightFWHM - leftFWHM; // 半峰宽
}

// 计算半峰宽（返回真实X轴差值）
double calculateFWHMWithXData(const std::vector<double> &signal, const std::vector<double> &xData,
                              int peakStart, int peakTop, int peakEnd)
{
    if (xData.size() != signal.size())
    {
        // 如果X轴数据大小不匹配，回退到原始方法并乘以平均间隔
        double fwhmPoints = calculateFWHM(signal, peakStart, peakTop, peakEnd);
        if (xData.size() >= 2)
        {
            double avgInterval = (xData.back() - xData.front()) / (xData.size() - 1);
            return fwhmPoints * avgInterval;
        }
        return fwhmPoints; // 无法计算间隔时返回点数
    }

    double peakHeight = signal[peakTop];
    double halfHeight = peakHeight / 2.0;

    // 左侧半高位置
    int left = peakStart;
    while (left < peakTop && signal[left] < halfHeight)
        left++;

    // 使用X轴数据进行线性插值
    double leftX = linearInterpolate(xData[left - 1], signal[left - 1], xData[left], signal[left], halfHeight);

    // 右侧半高位置
    int right = peakTop;
    while (right < peakEnd && signal[right] > halfHeight)
        right++;

    // 使用X轴数据进行线性插值
    double rightX = linearInterpolate(xData[right - 1], signal[right - 1], xData[right], signal[right], halfHeight);

    return rightX - leftX; // 真实的半峰宽
}

// 获取半高处的两个点坐标
FWHMPoints getFWHMPoints(const std::vector<double> &signal, const std::vector<double> &xData,
                         int peakStart, int peakTop, int peakEnd)
{
    FWHMPoints result = {0.0, 0.0, 0.0};

    if (xData.size() != signal.size())
    {
        // 数据大小不匹配，无法准确计算
        return result;
    }

    double peakHeight = signal[peakTop];
    double halfHeight = peakHeight / 2.0;

    // 左侧半高位置
    int left = peakStart;
    while (left < peakTop && signal[left] < halfHeight)
        left++;

    // 计算左侧半高点的X坐标
    result.leftX = linearInterpolate(xData[left - 1], signal[left - 1], xData[left], signal[left], halfHeight);

    // 右侧半高位置
    int right = peakTop;
    while (right < peakEnd && signal[right] > halfHeight)
        right++;

    // 计算右侧半高点的X坐标
    result.rightX = linearInterpolate(xData[right - 1], signal[right - 1], xData[right], signal[right], halfHeight);

    // 计算半峰宽
    result.fwhm = result.rightX - result.leftX;

    return result;
}

// 获取半峰宽详细信息（包含插值索引）
FWHMDetails getFWHMDetails(const std::vector<double> &signal, int peakStart, int peakTop, int peakEnd)
{
    FWHMDetails result = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};

    double peakHeight = signal[peakTop];
    double halfHeight = peakHeight / 2.0;

    // 左侧半高位置
    int left = peakStart;
    while (left < peakTop && signal[left] < halfHeight)
        left++;

    // 计算左侧半高点的插值索引
    result.leftIndex = linearInterpolate(left - 1, signal[left - 1], left, signal[left], halfHeight);

    // 右侧半高位置
    int right = peakTop;
    while (right < peakEnd && signal[right] > halfHeight)
        right++;

    // 计算右侧半高点的插值索引
    result.rightIndex = linearInterpolate(right - 1, signal[right - 1], right, signal[right], halfHeight);

    // 计算半峰宽（点数差值）
    result.fwhmPoints = result.rightIndex - result.leftIndex;

    return result;
}

// 获取半峰宽详细信息（包含真实坐标，需要X轴数据）
FWHMDetails getFWHMDetailsWithXData(const std::vector<double> &signal, const std::vector<double> &xData,
                                    int peakStart, int peakTop, int peakEnd)
{
    // 先获取基础的插值索引信息
    FWHMDetails result = getFWHMDetails(signal, peakStart, peakTop, peakEnd);

    if (xData.size() != signal.size())
    {
        // 数据大小不匹配，只能返回索引信息
        return result;
    }

    // 根据插值索引计算真实的X坐标
    // 使用线性插值将浮点索引转换为真实X坐标
    int leftFloor = static_cast<int>(std::floor(result.leftIndex));
    int leftCeil = leftFloor + 1;
    if (leftCeil < xData.size())
    {
        double leftFraction = result.leftIndex - leftFloor;
        result.leftX = xData[leftFloor] + leftFraction * (xData[leftCeil] - xData[leftFloor]);
    }

    int rightFloor = static_cast<int>(std::floor(result.rightIndex));
    int rightCeil = rightFloor + 1;
    if (rightCeil < xData.size())
    {
        double rightFraction = result.rightIndex - rightFloor;
        result.rightX = xData[rightFloor] + rightFraction * (xData[rightCeil] - xData[rightFloor]);
    }

    // 计算真实半峰宽
    result.fwhmReal = result.rightX - result.leftX;

    return result;
}
/*
参数	作用
noiseWindow	动态噪声估计的局部窗口大小（建议20-50）。
slopeFactor	斜率阈值倍数（如3.0表示阈值=3×噪声标准差）。
minPeakWidth	最小峰宽（单位：数据点，根据采样频率调整）。
minPeakArea	最小峰面积（单位：信号强度×点数，需实验校准）。
*/
std::vector<Peak> findPeaks(
    const std::vector<double> &signal,
    int noiseWindow,
    int minPeakWidth,
    double slopeFactor,
    double minPeakArea,
    double minPeakHeight // 峰高不指定限制为0.0，此时以噪声10倍高度为峰高限值
)
{
    std::vector<Peak> peaks;

    // 1. 基线校正,在前期步骤中完成;此处认为 signal是经过平滑与基线扣除后的信号
    std::vector<double> correctedSignal = signal;

    // 2. 动态噪声估计、斜率阈值、峰面积阈值、峰高阈值
    double noiseLevel = estimateNoiseLevel(correctedSignal, noiseWindow);
    double slopeThreshold = slopeFactor * noiseLevel;
    if (minPeakHeight == 0.0)
        minPeakHeight = noiseLevel * 10; // 此处倍数需要调试确定
    if (minPeakArea == 0.0)
        minPeakArea = noiseLevel * 25; // 此处倍数需要调试确定

    // 3. 微分
    std::vector<double> derivative = calculateDerivative(correctedSignal);

    // 4. 寻峰逻辑，微分峰起点斜率超过噪声均值slopeFactor倍认为一个峰开始出现
    size_t i = 1;
    while (i < correctedSignal.size() - 1)
    {
        // 检测峰起点（斜率超过阈值）
        if (derivative[i] > slopeThreshold)
        {
            int start = i - 1;
            // 寻找顶点（导数由正变负）
            i = i + minPeakWidth / 2; // 峰至少5个点
            while (i < correctedSignal.size() - 1 && derivative[i] > 0)
                i++;
            int top = i - 1;
            // 寻找终点（斜率回到负阈值）
            i = i + minPeakWidth / 2;
            while (i < correctedSignal.size() - 1 && derivative[i] < -slopeThreshold)
                i++;
            int end = i - 1;

            // 计算峰宽、峰高、面积
            int width = end - start;
            double height = signal[top];
            double area = 0.0;
            for (int j = start; j <= end; ++j)
            {
                area += correctedSignal[j];
            }

            // 过滤小峰
            if (width >= minPeakWidth && area >= minPeakArea && height >= minPeakHeight)
            {
                // 计算半峰宽
                double fwhm = calculateFWHM(correctedSignal, start, top, end);
                peaks.push_back({top, start, end, height, area, fwhm});
            }
        }
        i++;
    }
    return peaks;
}

std::vector<Peak> searchPeaks(const std::vector<double> &original_signal_intensity_vector)
{
    // return findPeaks(original_signal_intensity_vector);
    // return findPeaks(movingAverage(original_signal_intensity_vector));
    return findPeaks(cubicBaselineCorrection(movingAverage(original_signal_intensity_vector, 2)));
}

///
/// 窗口内的标准偏差（总体） stdDev = [ (sum(squareOfError)/(windowSize) ]^0.5
////// \brief WindowStdDev
////// \param data
////// \return
///
double WindowStdDev(const std::vector<double> &data)
{
    int sizeOfData = data.size();
    double mean = std::accumulate(data.begin(), data.end(), 0.0) / sizeOfData;
    double sumOfSquare = 0;
    for (int i = 0; i < sizeOfData; ++i)
    {
        sumOfSquare += (data[i] - mean) * (data[i] - mean);
    }
    return std::sqrt(sumOfSquare / sizeOfData); // 这是窗口所有点的方差，所以分母是 sizeOfData
}

///
/// 计算单个峰的 SNR
/// step 1、 取 峰前 噪声全部范围
/// step 2、 在范围内 滑动窗口 遍历所有点 （需要窗口宽度参数--用半峰宽为宽度）
/// step 3、 拷贝窗口内 数据点 到 新的vector<doubl> data;
/// step 4、 每个窗口 计算窗口内全部数据 data 的 stdDev (通过WindowStdDev函数实现）
/// \brief SNRofSinglePeak 以FWHM为噪音窗口宽度，计算信噪比 SNR（一个已经寻到的峰所对应的 信噪比）
/// \param target_peak
/// \param original_signal_intensity_vector
/// \param folderWidthOfNoise - 噪声窗口相对于FWHM的倍数
/// \return
///
double SNRofSinglePeak(Peak target_peak, const std::vector<double> &original_signal_intensity_vector, int folderWidthOfNoise)
{
    int widow_size = static_cast<int>(target_peak.fwhm); // 以半峰宽为窗口宽度
    if (widow_size <= 0)
        widow_size = 1; // 防止窗口大小为0

    // 全体噪声范围确定此 singlePeak 的对应噪声区域
    int prePeak_startIndex = std::max(0, target_peak.start - widow_size * folderWidthOfNoise);
    int prePeak_endIndex = target_peak.start;
    int postPeak_startIndex = target_peak.end;
    int postPeak_endIndex = std::min(static_cast<int>(original_signal_intensity_vector.size()), target_peak.end + widow_size * folderWidthOfNoise);

    std::vector<double> stdDevList; // 标准偏差列表

    // 滑动峰前数据
    for (int i = prePeak_startIndex; i < prePeak_endIndex - widow_size && i >= 0; ++i)
    {
        if (i + widow_size <= static_cast<int>(original_signal_intensity_vector.size()))
        {
            std::vector<double> data(original_signal_intensity_vector.begin() + i, original_signal_intensity_vector.begin() + i + widow_size);
            stdDevList.push_back(WindowStdDev(data));
        }
    }

    // 滑动峰后数据
    for (int i = postPeak_startIndex; i < postPeak_endIndex - widow_size && i >= 0; ++i)
    {
        if (i + widow_size <= static_cast<int>(original_signal_intensity_vector.size()))
        {
            std::vector<double> data(original_signal_intensity_vector.begin() + i, original_signal_intensity_vector.begin() + i + widow_size);
            stdDevList.push_back(WindowStdDev(data));
        }
    }

    if (stdDevList.empty())
    {
        return 1.0; // 如果没有噪声数据，返回默认值
    }

    double min_SNR = stdDevList[0];
    for (double num : stdDevList)
    {
        if (num < min_SNR)
            min_SNR = num;
    }

    if (min_SNR == 0.0)
    {
        return target_peak.height; // 避免除零错误
    }

    return target_peak.height / min_SNR;
}

///
/// 计算步骤： 以数据（vector<double> signal_intensity_vector)及其得到的peakList,
/// 遍历 每一个 peak, 每个 peak，得到其 该有的信噪比 SNR(通过SNRofSinglePeak函数实现)
/// 数据可以是 校正后的数据 or 平滑后的数据 or 原始数据
/// \brief CalcSNRofPeakList
/// \param peakList
/// \param original_signal_intensity_vector
/// \return 信噪比SNR数组
///
// 确保函数被正确编译和导出
std::vector<double> CalcSNRofPeakList(const std::vector<double> &signal_intensity_vector, const std::vector<Peak> &peakList, int folderWidthOfNoise)
{
    std::vector<double> SNRList;

    size_t numberOfPeak = peakList.size();
    for (size_t i = 0; i < numberOfPeak; i++)
    {
        SNRList.push_back(SNRofSinglePeak(peakList[i], signal_intensity_vector, folderWidthOfNoise));
    }
    return SNRList;
}
