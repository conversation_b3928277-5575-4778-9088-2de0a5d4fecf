# 平均质谱功能实现说明

## 功能概述

为自定义区域实现了平均质谱计算和显示功能，用户可以通过 alt+鼠标左键拖拽创建自定义区域，系统会自动计算该区域内的平均质谱并作为 MASS 数据显示。平均质谱支持所有 MASS 数据的菜单功能，但无法加载 XIC 数据。

## 主要组件

### 1. AvgMassManager 扩展 (FileData/avgmassmanager.h/cpp)

- **作用**: 直接计算自定义区域的平均质谱，复用现有的平均质谱计算架构
- **新增功能**:
  - `calculateCustomRangeAvgMass()`: 为自定义区域计算平均质谱数据
  - 复用现有的平均质谱状态管理和数据存储机制

### 2. AvgMassDisplayManager (FileData/avgmassdisplaymanager.h/cpp)

- **作用**: 简化为平均质谱显示的辅助管理器（可选组件）
- **主要功能**:
  - `createAvgMassForCustomRange()`: 调用 AvgMassManager 进行计算
  - `removeAvgMassForCustomRange()`: 删除平均质谱的辅助方法
  - 维护平均质谱与自定义区域的映射关系

### 3. GlobalDefine::CustomRange 扩展 (Globals/GlobalDefine.h)

- **新增字段**:
  - `MassChartData *avgMassData`: 关联的平均质谱 MASS 数据
  - `bool hasAvgMass`: 是否已计算平均质谱
  - `QString avgMassUniqueId`: 平均质谱 MASS 的唯一 ID

### 4. LxChart 集成 (LxChart/lxchart.h/cpp)

- **新增方法**:
  - `createAvgMassForCustomRange()`: 直接调用 AvgMassManager 计算并创建 TIC
  - `removeAvgMassForCustomRange()`: 删除自定义区域的平均质谱
  - `getVecCustomRange()`: 获取自定义区域向量的访问接口
  - `removeCustomRange()`: 删除指定索引的自定义区域

## 工作流程

### 创建平均质谱

1. 用户按住 alt+鼠标左键拖拽创建自定义区域
2. `LxChart::handleCustomRangeSelection()` 被调用
3. 创建自定义区域后，自动调用 `createAvgMassForCustomRange()`
4. `LxChart::createAvgMassForCustomRange()` 直接调用 `AvgMassManager::calculateCustomRangeAvgMass()`
5. AvgMassManager 计算自定义区域内的平均质谱数据
6. 在 LxChart 中创建 `MassChartData` 对象，设置标题为"平均质谱"，颜色为橙色
7. 通过 `LxChart::AddLxChartData()` 添加到图表，自动创建 `LxChartLegend`

### 删除机制（双向关联）

1. **删除自定义区域时**:

   - `LxChart::clearCustomRangeSelection()` 调用 `removeAvgMassForCustomRange()`
   - 通过 `LxChart::RemoveLxChartDataByUniqueID()` 从图表中移除平均质谱 MASS

2. **删除平均质谱 MASS 时**:
   - `LxChart::RemoveLxChartDataByUniqueID()` 检测到平均质谱 MASS 删除（通过路径判断）
   - 直接清理对应自定义区域的平均质谱引用
   - 自动删除对应的自定义区域

### 动态更新机制

1. **自定义区域拖拽更新**:

   - 拖拽自定义区域边界时，实时更新区域范围
   - 拖拽完成后自动更新平均质谱数据
   - **优化策略**：直接更新现有 MASS 对象的数据，不重新创建对象
   - 类似背景区域修改的处理方式

2. **高效更新机制**:

   - 只有一个平均质谱对象，避免重复创建
   - 使用 `setDataThreadSafe()` 直接更新数据
   - 保持对象引用和 ID 不变，只更新数据内容

3. **实时响应**:
   - 区域范围变化立即反映在平均质谱计算中
   - 保持数据的一致性和准确性

## 数据计算

### 当前实现

- 使用模拟数据进行测试
- 生成 1000 个数据点，m/z 范围 50-500
- 包含三个模拟峰值：110、210、360 处的高斯峰

### 架构优势

- **复用现有架构**: 直接使用 AvgMassManager，与背景平均质谱使用相同的计算框架
- **简化数据流**: 减少中间层，直接从 AvgMassManager 获取计算结果
- **统一管理**: 平均质谱状态管理和数据存储使用统一的机制

### 未来扩展

- 可以集成真实的平均质谱计算算法到 AvgMassManager 中
- 参考 `FileDataManager::loadMassDataForFrameWithAvg()` 的实现
- 使用 `LxDataReader::loadMassDataForAvg()` 进行数据读取
- 支持多个自定义区域的平均质谱计算

## 特性

### 显示特性

- 平均质谱 MASS 显示为橙色，区别于普通 MASS
- 标题固定为"平均质谱"
- 支持所有标准 MASS 操作（显示/隐藏、颜色设置、删除等）

### 菜单功能支持

- **右键菜单**：删除、隐藏、颜色、宽度、字体、导出数据
- **导出数据**：支持导出平均质谱数据到 Excel 文件
- **样式设置**：支持修改颜色、线宽、字体等样式属性
- **所有功能**：作为 MassChartData，自动继承所有 MASS 数据的菜单功能

### XIC 加载策略

- **扫描模式自适应**：
  - **FullScan 模式**：遍历所有 MASS（除平均质谱）加载 XIC
  - **MRM/SIM 模式**：单线 XIC 加载（只为最近的棒子加载 XIC）
- **平均质谱跳过**：在所有模式下都跳过平均质谱，不加载 XIC
- **路径识别**：通过虚拟路径前缀"VIRTUAL://AvgMass\_"识别平均质谱
- **无提示干扰**：不显示错误提示，静默跳过

### 虚拟路径特性

- **唯一性保证**：使用 QUuid 生成唯一的虚拟路径，格式为"VIRTUAL://AvgMass\_{uuid}"
- **不冲突**：虚拟路径不会与真实文件路径冲突
- **易识别**：通过"VIRTUAL://"前缀可以快速识别平均质谱数据
- **事件 ID 映射**：使用自定义区域索引作为事件 ID，便于关联管理

### 其他限制

- 目前只支持一个自定义区域（`CUSTOM_RANGE_SIZE = 1`）
- 平均质谱计算使用模拟数据

## 测试建议

### 基本功能测试

1. **创建测试**:

   - 加载 TIC 数据
   - 按住 alt+鼠标左键拖拽创建自定义区域
   - 验证平均质谱 MASS 是否出现在图表中
   - 检查图例是否正确显示

2. **删除测试**:

   - 右键点击自定义区域删除
   - 验证平均质谱 MASS 是否同时被删除
   - 右键点击平均质谱图例删除
   - 验证自定义区域是否同时被删除

3. **交互测试**:

   - 测试平均质谱 MASS 的显示/隐藏功能
   - 测试颜色设置功能
   - 测试图例点击选中功能

4. **菜单功能测试**:

   - 右键点击平均质谱图例，测试所有菜单项
   - 测试导出数据功能
   - 测试样式修改功能（颜色、宽度、字体）

5. **XIC 加载限制测试**:

   - 双击平均质谱 MASS，验证是否直接跳过不加载 XIC
   - 确认不会显示错误提示
   - 双击其他正常 MASS，验证 XIC 加载功能正常

6. **动态更新测试**:
   - 拖拽自定义区域边界，验证平均质谱是否实时更新
   - 测试拖拽完成后平均质谱数据的准确性
   - 验证拖拽过程中的用户体验（光标、提示等）

### 边界情况测试

1. 在没有 TIC 数据时创建自定义区域
2. 创建多个自定义区域（应该受到数量限制）
3. 快速创建和删除自定义区域

## 编译和运行

### 编译要求

- 确保所有新增的头文件都已正确包含
- 检查前向声明是否正确

### 运行提示

- 功能集成到现有的 LxChart 中，无需额外配置
- 平均质谱功能会在创建自定义区域时自动触发

## 注意事项

1. **线程安全**: AvgMassDisplayManager 使用互斥锁保护共享数据
2. **内存管理**: 平均质谱 TIC 的生命周期由 LxChart 管理
3. **双向删除**: 确保自定义区域和平均质谱的关联关系正确维护
4. **扩展性**: 代码结构支持未来添加真实的平均质谱计算算法

## 后续改进

1. **真实数据计算**: 集成实际的平均质谱计算算法
2. **性能优化**: 对大数据量的平均质谱计算进行优化
3. **用户界面**: 添加平均质谱计算的进度提示
4. **配置选项**: 允许用户配置平均质谱的显示属性
